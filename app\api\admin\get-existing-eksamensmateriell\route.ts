import { NextRequest, NextResponse } from 'next/server';
import EksamensmateriellService from '@/db/services/eksamensmateriellService';
import { validateEksamensmateriellForm } from '@/lib/shared/validation';
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/authOptions";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const userRoles = Array.isArray(session.user.role)
      ? session.user.role
      : [session.user.role];
    
    const hasAdminRole = userRoles.some(role => 
      role === "urn:udir:pgsa:administrator"
    );

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const fagkode = searchParams.get('fagkode');
    const eksamensperiode = searchParams.get('eksamensperiode');
    const variant = searchParams.get('variant') || undefined;

    if (!fagkode || !eksamensperiode) {
      return NextResponse.json(
        { error: 'Fagkode og eksamensperiode er påkrevet' },
        { status: 400 }
      );
    }

    // Validate the input parameters for security
    const validationResult = validateEksamensmateriellForm({
      fagkode,
      variant: variant || '',
      eksamensperiode,
      eksamensmaterielltype: 'dummy',
      eksamensdel: 'dummy',
      malForm: 'dummy'
    });

    if (!validationResult.isValid) {
      return NextResponse.json(
        { error: 'Ugyldig input: ' + validationResult.errors.join(', ') },
        { status: 400 }
      );
    }

    const existingFiles = await EksamensmateriellService.getExistingEksamensmateriell(fagkode, eksamensperiode, variant);

    const formattedFiles = existingFiles.map(item => ({
      id: item.eksamensmateriell.EksamensmateriellID.toString(),
      blobReference: item.eksamensmateriell.BlobReferanseEksamensmateriell,
      filename: item.eksamensmateriell.Filnavn,
      kategori: item.eksamensmateriell.Eksamensmateriellkategori,
      malform: item.eksamensmateriell.EksamensmateriellMalform,
      eksamensdel: item.eksamensdel,
      fileSize: item.eksamensmateriell.FileSize,
      mimeType: item.eksamensmateriell.MimeType,
      opphavsrett: item.eksamensmateriell.Opphavsrett,
      vedlegg: item.eksamensmateriell.Vedlegg
    }));

    return NextResponse.json({ files: formattedFiles });

  } catch (error) {
    console.error('Error fetching existing exam materials:', error);
    
    return NextResponse.json(
      { error: 'Kunne ikke hente eksisterende filer' },
      { status: 500 }
    );
  }
}