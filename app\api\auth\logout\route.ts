import { cookies } from "next/headers";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  const cookieStore = await cookies();

  // Delete all cookies by getting all cookies and clearing them
  const allCookies = cookieStore.getAll();

  for (const cookie of allCookies) {
    // Try to delete with different configurations to ensure removal
    cookieStore.delete(cookie.name);
  }

  return new Response(JSON.stringify({ url: process.env.NEXTAUTH_URL }), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
