"use client";

import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
  use,
} from "react";
import { useSchoolData } from "@/context/SchoolDataContext";
import { useRole } from "@/context/RoleContext";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Check, ChevronDown, ChevronsUpDown } from "lucide-react";
import { LiaTimesSolid } from "react-icons/lia";
import { cn } from "@/lib/shared/utils";
import { Badge } from "./ui/badge";
import { ImSpinner2 } from "react-icons/im";
import { Skeleton } from "./ui/skeleton";

const ITEMS_PER_PAGE = 50;

interface SchoolSelectorProps {
  labelId?: string;
}

const SchoolSelector: React.FC<SchoolSelectorProps> = ({ labelId }) => {
  // State and context hooks
  const { combinedSchoolData, isLoading, error } = useSchoolData();
  const { selectedRole, setSelectedRole } = useRole();
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const commandListRef = useRef<HTMLDivElement>(null);

  // Check if the role is for a single school
  const isSingleSchoolRole = useMemo(
    () =>
      selectedRole?.role.startsWith("urn:udir:eksamen:sa") ||
      selectedRole?.role.startsWith("urn:udir:eksamen:sa+") ||
      selectedRole?.role.startsWith("urn:udir:eksamen:ev"),
    [selectedRole?.role]
  );

  // Filter schools based on role
  const schoolData = useMemo(() => {
    if (!selectedRole) return [];
    if (isSingleSchoolRole) {
      let selectedSchool = combinedSchoolData.find(
        (s) => s.organisasjonsnummer === selectedRole.orgId
      );

      return selectedSchool ? [selectedSchool] : [];
    }

    //TODO: Update with correct role values
    // Define filters for different roles
    const filterMap: Record<string, (school: any) => boolean> = {
      "urn:udir:eksamen:vea": (school) => school.erVideregaendeskole,
      "urn:udir:eksamen:sf": (school) =>
        school.ansvarligFylkesmannOrganisasjonsnummer === selectedRole.orgId,
      "urn:udir:eksamen:ko": (school) =>
        school.skoleansvarligOrganisasjonsnummer === selectedRole.orgId,
      "urn:udir:eksamen:fk": (school) =>
        school.skoleansvarligOrganisasjonsnummer === selectedRole.orgId,
    };

    const filterFunction = Object.entries(filterMap).find(([key]) =>
      selectedRole.role.startsWith(key)
    )?.[1];

    return filterFunction
      ? combinedSchoolData.filter(filterFunction)
      : combinedSchoolData;
  }, [isSingleSchoolRole, selectedRole, combinedSchoolData]);

  // Filter schools based on search term
  const filteredSchools = useMemo(
    () =>
      isSingleSchoolRole
        ? schoolData
        : schoolData.filter((school) =>
            school.navn.toLowerCase().includes(searchTerm.toLowerCase())
          ),
    [schoolData, searchTerm, isSingleSchoolRole]
  );

  // Reset scroll position when filtered schools change
  useEffect(() => {
    if (commandListRef.current) {
      commandListRef.current.scrollTop = 0;
    }
  }, [filteredSchools]);

  // Paginate schools
  const paginatedSchools = useMemo(
    () =>
      isSingleSchoolRole
        ? schoolData
        : filteredSchools.slice(0, (page + 1) * ITEMS_PER_PAGE),
    [filteredSchools, page, isSingleSchoolRole, schoolData]
  );

  // Load more schools
  const loadMore = useCallback(() => {
    if (
      !isSingleSchoolRole &&
      (page + 1) * ITEMS_PER_PAGE < filteredSchools.length
    ) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [page, filteredSchools.length, isSingleSchoolRole]);

  // Handle school selection
  const handleSelectSchool = useCallback(
    (school: any) => {
      setSelectedRole((prevRole) => {
        if (!prevRole) return prevRole;
        if (
          prevRole.selectedSchoolId !== school.organisasjonsnummer ||
          prevRole.selectedSchoolName !== school.navn
        ) {
          return {
            ...prevRole,
            selectedSchoolId: school.organisasjonsnummer,
            selectedSchoolName: school.navn,
          };
        }
        return prevRole;
      });
      setOpen(false);
    },
    [setSelectedRole]
  );

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchTerm("");
    setPage(0);
  }, []);

  function getSchoolType(school: IPasxSchool) {
    if (school.erGrunnskole && school.erVideregaendeskole) {
      return "GRUNNSKOLE/VIDEREGÅENDE";
    } else if (school.erGrunnskole && !school.erVideregaendeskole) {
      return "GRUNNSKOLE";
    } else if (!school.erGrunnskole && school.erVideregaendeskole) {
      return "VIDEREGÅENDE";
    }
  }

  // Generate display name for school
  const getSchoolDisplayName = (school: IPasxSchool | undefined) => {
    if (!school)
      return <span className="text-gray-500">Skole ikke funnet</span>;
    return (
      <div className="flex flex-col items-start w-full text-wrap">
        <span className="font-medium text-sm text-gray-900">{school.navn}</span>
        {school.besoksadresse.postnummer || school.besoksadresse.gateadresse ? (
          <span className="text-xs text-gray-600">
            {`${school.besoksadresse.postnummer} ${school.besoksadresse.poststed}`.trim() ||
              ""}
            {school.besoksadresse.postnummer || school.besoksadresse.poststed
              ? ` - ${getSchoolType(school)}`
              : getSchoolType(school) || ""}
          </span>
        ) : (
          <div className="flex gap-1">
            <Skeleton className="w-[30px] h-[10px] bg-[#868698] " />
            <Skeleton className="w-[50px] h-[10px] bg-[#868698]" />
            <Skeleton className="w-[80px] h-[10px] bg-[#868698]" />
          </div>
        )}
      </div>
    );
  };

  // Effect to handle single school role
  useEffect(() => {
    if (isSingleSchoolRole && schoolData.length > 0) {
      const selectedSchool = schoolData[0];
      setSelectedRole((roleData) => {
        if (!roleData) return roleData;
        if (
          roleData.selectedSchoolId !== selectedSchool.organisasjonsnummer ||
          roleData.selectedSchoolName !== selectedSchool.navn
        ) {
          return {
            ...roleData,
            selectedSchoolId: selectedSchool.organisasjonsnummer,
            selectedSchoolName: selectedSchool.navn,
          };
        }
        return roleData;
      });
    }
  }, [isSingleSchoolRole, schoolData, setSelectedRole]);

  if (!selectedRole) return null;

  if (isLoading) {
    return (
      <div className="flex items-center gap-1 border-2 rounded p-2 text-sm bg-transparent border-black h-12 sm:w-[300px] lg:w-[400px]">
        <ImSpinner2
          className="animate-spin text-lg"
          aria-hidden="true"
          aria-label="spinner ikon"
          role="img"
        />
        <span>Laster skoler...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-1 border-2 rounded p-2 text-sm bg-transparent border-red-500 h-12 sm:w-[300px] lg:w-[400px] text-red-500">
        <span>Kunne ikke laste skoler: {error.message || "Ukjent feil"}</span>
      </div>
    );
  }

  if (isSingleSchoolRole) {
    const selectedSchool = schoolData[0];
    return (
      <div className="flex items-center border-2 rounded p-2 bg-transparent border-black h-12 sm:w-[300px] lg:w-[400px]">
        {selectedSchool ? getSchoolDisplayName(selectedSchool) : "Ukjent skole"}
      </div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label={labelId ? undefined : "Skolevalg"}
          aria-labelledby={labelId}
          aria-controls="Skolevalg"
          id="Skolevalg"
          className="w-full sm:w-[350px] lg:w-[450px] justify-between h-12 text-gray-900 bg-transparent hover:bg-gray-200"
          disabled={isSingleSchoolRole}
        >
          <div>
            {selectedRole.selectedSchoolId ? (
              getSchoolDisplayName(
                schoolData.find(
                  (s) => s.organisasjonsnummer === selectedRole.selectedSchoolId
                )
              )
            ) : (
              <span className="font-medium">Velg skole...</span>
            )}
          </div>
          <ChevronDown
            className={cn(
              "ml-2 h-5 w-5 shrink-0 opacity-70 transition-transform duration-200",
              isSingleSchoolRole && "hidden"
            )}
            style={{
              transform: open ? "rotate(180deg)" : "rotate(0deg)",
            }}
            aria-label="Åpne/lukk skolevalg"
            role="img"
          />
        </Button>
      </PopoverTrigger>
      {!isSingleSchoolRole && (
        <PopoverContent
          className="w-[350px] sm:w-[350px] lg:w-[450px] p-0"
          id="schoolChoiceList"
          role="dialog"
          aria-label="School Choice List"
          avoidCollisions={false}
          side="bottom"
          align="start"
          sideOffset={4}
        >
          <Command>
            <div>
              <div className="flex items-center border-b relative">
                <div className="relative flex-grow">
                  <CommandInput
                    placeholder="Søk etter skolenavn..."
                    aria-label="Søk etter skolenavn"
                    value={searchTerm}
                    onValueChange={setSearchTerm}
                    autoFocus
                    className="pr-8"
                  />
                  {searchTerm && (
                    <button
                      onClick={clearSearch}
                      className="absolute right-5 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      aria-label="Fjern søketekst"
                    >
                      <LiaTimesSolid />
                    </button>
                  )}
                </div>
              </div>
              {filteredSchools.length > 0 && (
                <div className="w-full px-4 py-2 text-sm flex-shrink-0">
                  <Badge variant="default" className="text-white font-normal">
                    {filteredSchools.length}{" "}
                    {filteredSchools.length > 1 ? "skoler" : "skole"}
                  </Badge>
                </div>
              )}
            </div>
            <CommandList
              className="max-h-[min(30vh,250px)] overflow-auto"
              ref={commandListRef}
              tabIndex={0}
            >
              <CommandEmpty className="text-gray-500 p-4">
                Ingen skoler funnet.
              </CommandEmpty>
              <CommandGroup>
                {paginatedSchools.map((school) => (
                  <CommandItem
                    key={school.organisasjonsnummer}
                    value={`${school.navn} ${school.organisasjonsnummer} ${school.besoksadresse.poststed}`}
                    onSelect={() => handleSelectSchool(school)}
                    className={cn(
                      "flex items-center p-2 text-gray-900 bg-transparent hover:cursor-pointer border-b",
                      selectedRole.selectedSchoolId ===
                        school.organisasjonsnummer && "bg-gray-100"
                    )}
                  >
                    <Check
                      role="img"
                      aria-label="huk av ikon"
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedRole.selectedSchoolId ===
                          school.organisasjonsnummer
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {getSchoolDisplayName(school)}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
            {(page + 1) * ITEMS_PER_PAGE < filteredSchools.length && (
              <Button
                variant="default"
                onClick={loadMore}
                className="w-full border-t-1 mt-2"
              >
                Last flere
              </Button>
            )}
          </Command>
        </PopoverContent>
      )}
    </Popover>
  );
};

export default SchoolSelector;
