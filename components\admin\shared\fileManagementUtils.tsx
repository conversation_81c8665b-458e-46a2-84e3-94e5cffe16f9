"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Trash2 } from "lucide-react";

// Map database eksamensdel values to user-friendly names
const EKSAMENSDEL_DISPLAY_MAP: Record<string, string> = {
  "EksamenDel1": "Eksamen del 1",
  "EksamenDel2": "Eksamen del 2", 
  "Eksamen": "Eksamen",
  "Forberedelse": "Forberedelse",
  "Del1og2": "Eksamen del 1 og del 2"
};

const mapEksamensdelForDisplay = (eksamensdel: string): string => {
  return EKSAMENSDEL_DISPLAY_MAP[eksamensdel] || eksamensdel;
};

export interface ExistingFile {
  id: string;
  blobReference: string;
  filename: string;
  kategori: string;
  malform: string;
  eksamensdel: string;
  fileSize: number;
  mimeType: string;
  opphavsrett?: boolean;
  vedlegg?: boolean;
}

/**
 * Utility function to format file sizes in a human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
};

/**
 * Shared delete confirmation dialog component
 */
interface DeleteConfirmationDialogProps {
  open: boolean;
  filename: string | undefined;
  onConfirm: () => void;
  onCancel: () => void;
  isDeleting?: boolean;
  isShared?: boolean;
  eksamensdeler?: string[];
}

export function DeleteConfirmationDialog({
  open,
  filename,
  onConfirm,
  onCancel,
  isDeleting = false,
  isShared = false,
  eksamensdeler = [],
}: DeleteConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Bekreft kansellering</DialogTitle>
          <DialogDescription className="text-left">
            Er du sikker på at du vil kansellere <strong>{filename}</strong>?
          </DialogDescription>
          {isShared && eksamensdeler.length > 1 && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mt-3">
              <p className="text-sm text-amber-800 font-medium">
                <strong>Advarsel:</strong> Denne filen er knyttet til flere eksamensdeler og vil bli kansellert fra alle:
              </p>
              <ul className="text-sm text-amber-700 mt-2 ml-2">
                {eksamensdeler.map((del, index) => (
                  <li key={index}>• {mapEksamensdelForDisplay(del)}</li>
                ))}
              </ul>
            </div>
          )}
        </DialogHeader>
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onCancel} disabled={isDeleting}>
            Avbryt
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            {isDeleting ? "Kansellerer..." : "Kanseller fil"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Custom hook for managing delete confirmation dialog state
 */
export function useDeleteConfirmation() {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<ExistingFile | null>(null);
  const [isShared, setIsShared] = useState(false);
  const [eksamensdeler, setEksamensdeler] = useState<string[]>([]);
  const [checkingShared, setCheckingShared] = useState(false);

  const openDeleteDialog = async (file: ExistingFile) => {
    setFileToDelete(file);
    setCheckingShared(true);
    
    try {
      // Check if this file is shared between multiple eksamensdeler
      const response = await fetch(
        `/api/admin/check-shared-eksamensmateriell?blobReference=${encodeURIComponent(file.blobReference)}`
      );
      
      if (response.ok) {
        const data = await response.json();
        setIsShared(data.isShared);
        setEksamensdeler(data.eksamensdeler || []);
      } else {
        // If check fails, proceed as non-shared
        setIsShared(false);
        setEksamensdeler([]);
      }
    } catch (error) {
      console.warn("Failed to check if file is shared:", error);
      // If check fails, proceed as non-shared
      setIsShared(false);
      setEksamensdeler([]);
    } finally {
      setCheckingShared(false);
      setDeleteDialogOpen(true);
    }
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setFileToDelete(null);
    setIsShared(false);
    setEksamensdeler([]);
  };

  const handleDeleteCancel = () => {
    closeDeleteDialog();
  };

  return {
    deleteDialogOpen,
    fileToDelete,
    isShared,
    eksamensdeler,
    checkingShared,
    openDeleteDialog,
    closeDeleteDialog,
    handleDeleteCancel,
  };
}
