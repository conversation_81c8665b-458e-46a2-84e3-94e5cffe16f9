import { NextRequest, NextResponse } from "next/server";
import { generateSignalRAccessToken } from "@/lib/server/generateSignalRAccessToken";
import { v4 as uuidv4 } from "uuid";
import { SignalRMessageEnum } from "@/enums/SignalRMessageEnum";
import {
  deleteHashFieldFromRedis,
  setHashFieldInRedis,
} from "@/app/lib/redisHelper";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { getStatusInfoFromPgsa } from "@/lib/server/getStatusInfoFromPgsa";
import { updateCandidateStatus } from "@/lib/server/updateCandidateStatus";
import { getClientIp } from "@/lib/server/getClientIp";

// Configuration for Azure SignalR with endpoint extraction from connection string
const SIGNALR_CONFIG = {
  hubName: process.env.AZURE_SIGNALR_HUB_NAME || "pgshub",
  connectionString:
    process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "",
  get endpoint() {
    return this.connectionString.match(/Endpoint=(.*?);/)?.[1] ?? "";
  },
};

export async function POST(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const { candidateNumber, userId, schoolId, sessionId } =
      await request.json();

    if (!isValidRequest(candidateNumber, schoolId, userId)) {
      return createErrorResponse(
        "Candidate number, schoolId and userId is required",
        400
      );
    }

    await Promise.all([
      updateIsAuthorizedFlagInRedis(candidateNumber, sessionId),
      sendSignalRStatusUpdate(schoolId, candidateNumber, false),
      removeAccessRequestFromRedis(schoolId, candidateNumber),
    ]);

    const userInfoFromPgsa = await getStatusInfoFromPgsa(userId);

    if (
      userInfoFromPgsa.Status ===
      CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert
    ) {
      const clientIp = await getClientIp(request);

      await updateCandidateStatus(
        CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
        CandidateStatusEnum.InnloggetAutentisert,
        userInfoFromPgsa.TestPartId,
        userInfoFromPgsa.CandidateNumber,
        userInfoFromPgsa.ExamGroupCode,
        userInfoFromPgsa.CandidateNumber,
        userInfoFromPgsa.UserId,
        clientIp
      );
    }

    await notifyCandidateOfAccess(candidateNumber, sessionId);

    return createSuccessResponse("Access request processed successfully");
  } catch (error) {
    return handleApiError(error);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { candidateNumber, schoolId, sessionId } = await request.json();

    if (!candidateNumber || !schoolId) {
      return createErrorResponse(
        "Candidate number and schoolId are required",
        400
      );
    }

    // Remove SignalR status update and access request from Redis
    await sendSignalRStatusUpdate(schoolId, candidateNumber, false);
    await removeAccessRequestFromRedis(schoolId, candidateNumber);

    // Send notification to candidate that access was denied
    if (sessionId) {
      await notifyCandidateOfDenial(candidateNumber, sessionId);
    }

    return createSuccessResponse("Access request deleted successfully");
  } catch (error) {
    return handleApiError(error);
  }
}

// Sends a notification to the candidate through SignalR when access is granted
async function notifyCandidateOfAccess(
  candidateNumber: string,
  sessionId: string
) {
  const url = buildSignalRUrl(`users/${candidateNumber}`);
  const message = {
    target: SignalRMessageEnum.GotAccess,
    arguments: [{ id: sessionId, candidateNumber }],
  };

  const response = await sendSignalRMessage(url, message);

  if (!response.ok) {
    const errorText = await response.text();
    console.error("SignalR error:", errorText);
    throw new Error(`Failed to send SignalR message: ${response.status}`);
  }
}

// Sends a notification to the candidate through SignalR when access is denied
async function notifyCandidateOfDenial(
  candidateNumber: string,
  sessionId: string
) {
  const url = buildSignalRUrl(`users/${candidateNumber}`);
  const message = {
    target: SignalRMessageEnum.AccessDenied,
    arguments: [{ id: sessionId, candidateNumber }],
  };

  const response = await sendSignalRMessage(url, message);

  if (!response.ok) {
    const errorText = await response.text();
    console.error("SignalR error:", errorText);
    throw new Error(`Failed to send SignalR message: ${response.status}`);
  }
}

// Updates isAuthorized flag in Redis for the candidate
async function updateIsAuthorizedFlagInRedis(
  candidateNumber: string,
  sessionId: string
) {
  try {
    const key = `candidate:${candidateNumber}:${sessionId}`;

    await setHashFieldInRedis(key, "isAuthorized", "true");
  } catch (error) {
    console.error("Error updating isAuthorized flag in Redis:", error);
    throw new Error("Failed to update isAuthorized flag in Redis");
  }
}

// Sends SignalR message to update access request status and returns request ID for tracking
async function sendSignalRStatusUpdate(
  schoolId: string,
  candidateNumber: string,
  hasRequestedAccess: boolean
): Promise<string> {
  try {
    const url = buildSignalRUrl(`groups/${schoolId}`);
    const requestId = uuidv4();
    const message = {
      target: SignalRMessageEnum.AccessRequest,
      arguments: [
        {
          id: requestId,
          candidateNumber,
          hasRequestedAccess: hasRequestedAccess,
          schoolId,
        },
      ],
    };

    const response = await sendSignalRMessage(url, message);

    if (!response.ok) {
      throw new Error("Failed to send access request");
    }

    return requestId;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
}

// Cleans up Redis after access request is processed
async function removeAccessRequestFromRedis(
  schoolId: string,
  candidateNumber: string
) {
  const key = `AccessRequest:${schoolId}`;
  if (candidateNumber) {
    await deleteHashFieldFromRedis(key, candidateNumber);
  }
}

// Helper functions
function buildSignalRUrl(path: string): string {
  return `${SIGNALR_CONFIG.endpoint}/api/v1/hubs/${SIGNALR_CONFIG.hubName}/${path}`;
}

async function sendSignalRMessage(url: string, message: any) {
  const accessToken = generateSignalRAccessToken({
    audience: url,
    lifetime: 60, // Token expires after 60 seconds
  });

  return fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(message),
  });
}

function isValidRequest(
  candidateNumber?: string,
  schoolId?: string,
  userId?: string
): boolean {
  return Boolean(candidateNumber && schoolId && userId);
}

function createSuccessResponse(message: string) {
  return NextResponse.json({
    success: true,
    message,
  });
}

function createErrorResponse(error: string, status: number) {
  return NextResponse.json(
    {
      success: false,
      error,
    },
    { status }
  );
}

function handleApiError(error: unknown) {
  console.error("Error handling access request:", error);
  return NextResponse.json(
    {
      success: false,
      error: "Failed to process access request",
      details: error instanceof Error ? error.message : "Unknown error",
    },
    { status: 500 }
  );
}
