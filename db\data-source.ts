import "reflect-metadata";
import { DataSource } from "typeorm";
import { AuditLog } from "./models/auditLog";
import { getAzureSqlToken } from "@/lib/server/getAzureSqlToken";
import { Operation } from "./models/operation";
import { Fagkodeeksamen } from "./models/Fagkodeeksamen";
import { Eksamensdel } from "./models/Eksamensdel";
import { Eksamensmateriell } from "./models/Eksamensmateriell";
import { EksamensdelEksamensmateriell } from "./models/EksamensdelEksamensmateriell";
import { Kandidatgruppe } from "./models/Kandidatgruppe";
import { Kandidateksamen } from "./models/Kandidateksamen";
import { Person } from "./models/Person";
import { Besvarelsesdel } from "./models/Besvarelsesdel";
import { Besvarelsesfil } from "./models/Besvarelsesfil";
import { BesvarelsesDelBesvarelsesfil } from "./models/BesvarelsesDelBesvarelsesfil";

const isLocalhost = process.env.PGS_ENVIRONMENT === "localhost";

const AppDataSource = isLocalhost
  ? new DataSource({
      type: "mssql",
      host: process.env.DATABASE_HOST,
      port: 1433,
      username: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      synchronize: false,
      logging: true,
      entities: [AuditLog, Operation, Fagkodeeksamen, Eksamensdel, Eksamensmateriell, EksamensdelEksamensmateriell, Kandidatgruppe, Kandidateksamen, Person, Besvarelsesdel, Besvarelsesfil, BesvarelsesDelBesvarelsesfil],
      subscribers: [],
      options: {
        encrypt: true,
        enableArithAbort: true,
        trustServerCertificate: false,
      },
      extra: {
        validateConnection: false,
        trustServerCertificate: false,
      },
      pool: {
        max: 10,
        min: 2,
        idleTimeoutMillis: 30000,
      },
      connectionTimeout: 30000,
      requestTimeout: 30000,
      migrationsRun: false,
      migrationsTransactionMode: "all",
    })
  : new DataSource({
      type: "mssql",
      host: process.env.DATABASE_HOST,
      database: process.env.DATABASE_NAME,
      connectionTimeout: 30000,
      requestTimeout: 30000,
      options: {
        encrypt: true,
      },
      authentication: {
        type: "azure-active-directory-access-token",
        options: {
          token: "", // will be set dynamically below
        },
      },
      pool: {
        max: 20,
        min: 2,
        idleTimeoutMillis: 30000,
      },
      entities: [AuditLog, Operation, Fagkodeeksamen, Eksamensdel, Eksamensmateriell, EksamensdelEksamensmateriell, Kandidatgruppe, Kandidateksamen, Person, Besvarelsesdel, Besvarelsesfil, BesvarelsesDelBesvarelsesfil],
      logging: false,
    });

export default AppDataSource;
