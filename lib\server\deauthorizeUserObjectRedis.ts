import {
  getHashObjectsWithPrefix,
  setHashFieldInRedis,
} from "@/app/lib/redisHelper";

export async function deauthorizeUserObjectRedis(
  candidateNumber: string,
  excludeSessionId?: string
): Promise<{ deauthorizedSessions: string[], userId?: string }> {
  try {
    const userRedisObjects = await getHashObjectsWithPrefix<IRedisObject>(
      `candidate:${candidateNumber}:`
    );

    if (!userRedisObjects || userRedisObjects.length === 0) {
      return { deauthorizedSessions: [] };
    }

    const deauthorizedSessions: string[] = [];
    let userId: string | undefined;

    // Extract userId from the first available Redis object
    if (userRedisObjects.length > 0) {
      userId = userRedisObjects[0].userId;
    }

    // Update each Redis hash object that has isAuthorized = true
    const updatePromises = userRedisObjects.map(async (redisObject) => {
      try {
        // Only update if the user is currently authorized and it's not the current requesting session
        if (redisObject.isAuthorized && redisObject.userSessionId !== excludeSessionId) {
          const redisKey = `candidate:${candidateNumber}:${redisObject.userSessionId}`;

          // Directly update just the isAuthorized field in the hash
          await setHashFieldInRedis(redisKey, "isAuthorized", "false");
          
          // Add to deauthorized sessions list
          deauthorizedSessions.push(redisObject.userSessionId);
        }
        return;
      } catch (error) {
        console.error(`Error updating Redis hash: ${error}`);
        return;
      }
    });

    await Promise.all(updatePromises);
    return { deauthorizedSessions, userId };
  } catch (error) {
    throw new Error("An error occurred when updating authorization status");
  }
}
