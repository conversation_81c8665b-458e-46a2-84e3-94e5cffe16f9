"use server";

import { getPgsaStatusInfoFromRedis } from "@/app/lib/redisHelper";
import { IStatusInfo } from "@/interface/IStatusInfo";

export async function getStatusInfoFromPgsa(
  userId: string
): Promise<IStatusInfo> {
  try {
    const statusInfo = await getPgsaStatusInfoFromRedis(userId);
    if (statusInfo === null) {
      throw new Error("Status info not found");
    }
    return statusInfo;
  } catch (error) {
    throw error;
  } finally {
  }
}
