"use client";
import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs";
import { ImSpinner2 } from "react-icons/im";
import { VscSend } from "react-icons/vsc";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { useRole } from "@/context/RoleContext";
import { columnsError } from "@/components/tables/kandidatmonitor/leverFor/fileTableError/columnsError";
import { DataTableError } from "@/components/tables/kandidatmonitor/leverFor/fileTableError/data-table-error";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DataTableSubmitted } from "@/components/tables/kandidatmonitor/leverFor/leverteFiler/data-table-submitted";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/tables/kandidatmonitor/leverFor/opplastet/data-table";
import { deliverFiles } from "./fileUtils";
import useFileHandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import { toast } from "@/components/ui/use-toast";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { ActivityLogger } from "@/lib/shared/ActivityLogger";
import { OperationEnum } from "@/enums/OperationEnum";
import { TestPartsEnum } from "@/enums/TestPart";

export default function TabsComponent() {
  const [selectedTab, setSelectedTab] = useState("opplastedeFiler");
  const [previousErrorCount, setPreviousErrorCount] = useState(0);
  const [errorFiles, setErrorFiles] = useState<IUploadedFile[]>([]);
  const { selectedRole } = useRole();
  const { uploadedFiles, addFile, deliverFile, isUploading } =
    useFileHandlerForMonitor();
  const [isOpen, setIsOpen] = useState(false);
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('sessionId');

  useEffect(() => {
    const errorFiles = uploadedFiles.filter((file) => file.IsRejected);

    // Bytt til riktig tab basert på siste opplasting, men ikke hvis brukeren er på leverteFiler
    if (selectedTab !== "leverteFiler") {
      if (errorFiles.length > previousErrorCount) {
        setSelectedTab("feilFiler");
      } else {
        setSelectedTab("opplastedeFiler");
      }
    }

    setErrorFiles(errorFiles);
    setPreviousErrorCount(errorFiles.length);
  }, [uploadedFiles]);

  const uploadedFilesCount = uploadedFiles.filter(
    (file) =>
      !file.IsRejected &&
      !file.Delivered &&
      selectedRole?.selectedSchoolId === file.SchoolId.toString()
  ).length;

  const deliveredFilesCount = uploadedFiles.filter(
    (file) =>
      file.Delivered &&
      selectedRole?.selectedSchoolId === file.SchoolId.toString()
  ).length;

  const errorFilesCount = errorFiles.length;

  const circularBadgeStyle =
    "rounded-full w-5 h-5 flex items-center justify-center text-[11px] mr-1 text-white drop-shadow-sm";

  async function logDeliveryActivity(storedDataJson: ICandidateMonitor) {
    const candidateInfo = {
      userId: storedDataJson.userId || "",
      firstName: storedDataJson.candidateName?.split(" ")[0] || "",
      lastName: storedDataJson.candidateName?.split(" ")[1] || "",
      registrationId:
        storedDataJson.candidateRegistrationId || storedDataJson.userId,
      candidateNumber: storedDataJson.candidateNumber || "",
    };

    const onePartExam = storedDataJson.deliveryStatusPart2 === -1;

    if (onePartExam) {
      await ActivityLogger.logActivity(
        OperationEnum.StatusLevertDigitalt,
        candidateInfo,
        {
          roleName: "PGS",
          testPartId: TestPartsEnum.EksamenDel1,
        }
      );

      await ActivityLogger.logActivity(
        OperationEnum.StatusDeliveredDigitalAccessBlocked,
        candidateInfo,
        {
          roleName: "PGS",
          testPartId: TestPartsEnum.EksamenDel1,
        }
      );
    } else {
      if (
        uploadedFiles.some(
          (row) =>
            row.TestPartId === TestPartsEnum.EksamenDel1 ||
            row.TestPartId === TestPartsEnum.EksamenDel1ogDel2
        )
      ) {
        await ActivityLogger.logActivity(
          OperationEnum.StatusLevertDigitaltDel1,
          candidateInfo,
          {
            roleName: "PGS",
            testPartId: TestPartsEnum.EksamenDel1,
          }
        );
      }

      if (
        uploadedFiles.some(
          (row) =>
            row.TestPartId === TestPartsEnum.EksamenDel2 ||
            row.TestPartId === TestPartsEnum.EksamenDel1ogDel2
        )
      ) {
        await ActivityLogger.logActivity(
          OperationEnum.StatusLevertDigitaltDel2,
          candidateInfo,
          {
            roleName: "PGS",
            testPartId: TestPartsEnum.EksamenDel2,
          }
        );

        await ActivityLogger.logActivity(
          OperationEnum.StatusDeliveredDigitalAccessBlockedPart2,
          candidateInfo,
          {
            roleName: "PGS",
            testPartId: TestPartsEnum.EksamenDel2,
          }
        );
      }
    }
  }

  const handleSubmitFiles = async () => {
    const { fileMissingExamPart, fileNotChecked } = await deliverFiles(
      uploadedFiles,
      (file: IUploadedFile) => deliverFile(file),
      selectedRole?.selectedSchoolId
    );

    if (fileMissingExamPart) {
      setIsOpen(true);
    } else if (fileNotChecked) {
      toast({
        variant: "warning",
        title: "Feil ved levering av fil",
        description: "Du må markere at filen er sjekket for å få levert den.",
      });
    } else {
      const dataFromStorage = sessionStorage.getItem("selectedCandidate");

      if (dataFromStorage) {
        const storedDataJson: ICandidateMonitor = JSON.parse(dataFromStorage);
        const onePartExam = storedDataJson.deliveryStatusPart2 === -1;

        // SignalR melding skal kun sendes hvis:
        // - Endelt eksamen (alltid)
        // - Todelt eksamen og det leveres på del 2 (eller del 1 og del 2)
        const shouldSendSignalR = onePartExam || 
          uploadedFiles.some(row => 
            row.TestPartId === TestPartsEnum.EksamenDel2 ||
            row.TestPartId === TestPartsEnum.EksamenDel1ogDel2
          );

        if (shouldSendSignalR) {
          await fetch(`/api/deauthorizeUserObjects`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              candidateNumber: storedDataJson.candidateNumber,
              excludeSessionId: sessionId || "", // Nyeste sesjon skal redirectes (ikke logges ut)
            }),
          });
        }

        //TODO: Logge aktivitet for alle leverte filer monitor
        await logDeliveryActivity(storedDataJson);
      }

      setSelectedTab("leverteFiler");
    }
  };

  return (
    <>
      <Tabs
        defaultValue={selectedTab}
        className="relative mr-auto w-full"
        value={selectedTab}
        onValueChange={setSelectedTab}
      >
        <TabsList className="text-sm md:text-lg flex-wrap h-auto min-h-[2.5rem]">
          <TabsTrigger value="feilFiler" className="text-xs md:text-base px-2 md:px-4 mb-1">
            <div className={`${circularBadgeStyle} bg-red-600`}>
              {errorFilesCount}
            </div>
            Filer med feil
          </TabsTrigger>

          <TabsTrigger value="opplastedeFiler" className="text-xs md:text-base px-2 md:px-4 mb-1">
            <div className={`${circularBadgeStyle} bg-primary`}>
              {uploadedFilesCount}
            </div>
            Opplastede filer
          </TabsTrigger>

          <TabsTrigger value="leverteFiler" className="text-xs md:text-base px-2 md:px-4 mb-1">
            <div className={`bg-green-700 ${circularBadgeStyle}`}>
              {deliveredFilesCount}
            </div>
            Leverte filer
          </TabsTrigger>
        </TabsList>

        <TabsContent value="feilFiler">
          <div className="mt-6">
            <DataTableError columnsFeil={columnsError} data={errorFiles} />
          </div>
        </TabsContent>

        <TabsContent value="opplastedeFiler">
          <div className="mt-6">
            <DataTable
              isUploading={false}
              data={uploadedFiles.filter(
                (file) =>
                  !file.IsRejected &&
                  !file.Delivered &&
                  selectedRole?.selectedSchoolId === file.SchoolId.toString()
              )}
              columns={[]}
            />
            <Button
              variant="default"
              className="mt-6 h-10 rounded-[3px] normal-case font-normal w-full sm:w-44"
              aria-label="Lever alle filer som er klare for levering"
              onClick={handleSubmitFiles}
              disabled={isUploading || uploadedFilesCount === 0}
            >
              {uploadedFiles.some((file) => file.IsSubmitting) ? (
                <>
                  <ImSpinner2
                    className="animate-spin mr-2 text-lg"
                    aria-hidden="true"
                    role="img"
                    aria-label="spinner ikon"
                  />
                  <span>Leverer filer...</span>
                </>
              ) : (
                <>
                  <VscSend
                    className="h-5 w-5 mr-2"
                    aria-hidden="true"
                    role="img"
                    aria-label="send ikon"
                  />
                  <span>Lever alle filer</span>
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="leverteFiler">
          <div className="mt-6">
            <DataTableSubmitted
              columns={[]}
              data={uploadedFiles.filter(
                (file) =>
                  file.Delivered &&
                  selectedRole?.selectedSchoolId === file.SchoolId.toString()
              )}
            />
          </div>
        </TabsContent>
      </Tabs>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Noen filer mangler eksamensdel</AlertDialogTitle>
            <AlertDialogDescription>
              Filer som mangler eksamensdel kan ikke leveres. Filer med gyldig
              eksamensdel har blitt levert.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Ok</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
