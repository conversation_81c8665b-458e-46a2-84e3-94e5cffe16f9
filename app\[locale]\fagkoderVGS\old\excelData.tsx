"use client";
import { columnsFagkode } from "@/components/tables/fagkoder/columns";
import { DataTable } from "@/components/tables/fagkoder/data-table";
import { IFagkoder } from "@/interface/IFagkoder";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import * as XLSX from "xlsx";

export default function ExcelTableViewer() {
  const [data, setData] = useState<IFagkoder[]>([]);

  useEffect(() => {
    const loadExcelData = async () => {
      try {
        const response = await fetch("/PGS-VGS-Oversikt-V-2025-20250127.xlsx");
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const buffer = await response.arrayBuffer();

        const workbook = XLSX.read(buffer, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const sheetData = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], {
          header: 1,
        });

        // Process rows manually
        // Process rows manually
        const parsedData: IFagkoder[] = sheetData.slice(1).map((row: any) => {
          const [
            Fagkode,
            Fagnavn,
            Eksamensdato,
            Todelt,
            Lareplan,
            SisteEksamen,
            LenkeEksamensplan,
          ] = row;

          let formattedEksamensdato;

          // Check if Eksamensdato is a string
          if (typeof Eksamensdato === "string") {
            formattedEksamensdato = Eksamensdato;
          }
          // Check if Eksamensdato is a number (Excel serial date)
          else if (typeof Eksamensdato === "number") {
            formattedEksamensdato = dayjs(
              (Eksamensdato - 25569) * 86400 * 1000
            ).format("DD.MM.YYYY");
          }
          // Handle cases where Eksamensdato is neither string nor number
          else {
            formattedEksamensdato = "Invalid Date";
          }
          // Format Todelt
          let formattedTodelt = "-";
          if (typeof Todelt === "number") {
            const totalSeconds = Todelt * 86400; // Excel time to seconds
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);

            formattedTodelt = `${hours.toString().padStart(2, "0")}:${minutes
              .toString()
              .padStart(2, "0")}`;
          }

          return {
            Fagkode,
            Fagnavn,
            Eksamensdato,
            Todelt,
            formattedTodelt,
            formattedEksamensdato,
            Lareplan,
            SisteEksamen,
            LenkeEksamensplan,
          };
        });

        setData(parsedData);
      } catch (error) {
        console.error("Error loading Excel file:", error);
      }
    };

    loadExcelData();
  }, []);

  return (
    <div>
      <DataTable columns={columnsFagkode} data={data} />
    </div>
  );
}
