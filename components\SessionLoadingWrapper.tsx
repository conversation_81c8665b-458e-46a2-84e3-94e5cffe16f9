"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useSchoolData } from "@/context/SchoolDataContext";
import LoadingSpinner from "./LoadingSpinner";

interface SessionLoadingWrapperProps {
  children: React.ReactNode;
}

const SessionLoadingWrapper: React.FC<SessionLoadingWrapperProps> = ({
  children
}) => {
  const { status } = useSession();
  const { isLoading: schoolDataLoading } = useSchoolData();
  const [showInitialLoading, setShowInitialLoading] = useState(true);
  const [hasShownContent, setHasShownContent] = useState(false);

  // Hide initial loading after a minimum time to prevent flash
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowInitialLoading(false);
    }, 800); // Minimum 800ms loading for smooth experience

    return () => clearTimeout(timer);
  }, []);

  // Track when content has been shown to prevent flickering
  useEffect(() => {
    if (status === "authenticated" && !schoolDataLoading && !showInitialLoading) {
      setHasShownContent(true);
    }
  }, [status, schoolDataLoading, showInitialLoading]);

  // Show loading spinner if:
  // 1. Session is loading
  // 2. School data is loading (initial data fetch)
  // 3. We're still in the initial loading period
  // 4. We haven't shown content yet
  const isLoading = (
    status === "loading" ||
    schoolDataLoading ||
    showInitialLoading ||
    (!hasShownContent && status !== "unauthenticated")
  );

  // Determine loading message based on state
  const getLoadingMessage = () => {
    if (status === "loading") return "Autentiserer...";
    if (schoolDataLoading) return "Laster skoledata...";
    return "Laster inn applikasjonen...";
  };

  if (isLoading) {
    return <LoadingSpinner message={getLoadingMessage()} />;
  }

  return <>{children}</>;
};

export default SessionLoadingWrapper;
