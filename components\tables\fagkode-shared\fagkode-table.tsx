"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createFagkodeColumns } from "./columns";
import { FagkodeDataTable } from "./data-table";
import { IFagkodeShared } from "@/interface/IFagkodeShared";
import { TableSkeletonFagkode } from "@/components/tableSkeleton-fagkode";
import { MissingFilesReport } from "@/components/MissingFilesReport";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { FaExclamationTriangle, FaCheck, FaTimes, FaDownload } from "react-icons/fa";
import { MdCloudDone } from "react-icons/md";

interface FagkodeTableProps {
  showFiles?: boolean;
  showPlagiarism?: boolean;
  showAdditionalColumns?: boolean;
  showBlobCheck?: boolean;
  apiEndpoint: string; // "/api/fagkodeeksamen" or "/api/fagkoderipgs"
  currentYearOnly?: boolean; // Only show current year periods (V and H)
}

interface FagkodeResponse {
  success: boolean;
  data: IFagkodeShared[];
  count: number;
}

interface BlobCheckResult {
  success: boolean;
  results: Array<{ blobName: string; exists: boolean }>;
  summary: {
    total: number;
    existing: number;
    missing: number;
  };
  missingFilesReport: Array<{
    fagkode: string;
    variantkode?: string;
    fagnavn: string;
    eksamensdel: string;
    filename: string;
    blobName: string;
  }>;
}

function getDefaultEksamensperiode(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // getMonth() returns 0-11

  // If month is after July (7), use H-year, otherwise V-year
  return month > 7 ? `H-${year}` : `V-${year}`;
}

function generateEksamensperioder(currentYearOnly: boolean = false): Array<{ value: string; label: string }> {
  const now = new Date();
  const currentYear = now.getFullYear();
  const periods = [];

  if (currentYearOnly) {
    // Only add current year periods (V and H)
    periods.push({ value: `V-${currentYear}`, label: `Vår ${currentYear}` });
    periods.push({ value: `H-${currentYear}`, label: `Høst ${currentYear}` });
  } else {
    // Add one year back and one year forward (original logic)
    for (let year = currentYear - 1; year <= currentYear + 1; year++) {
      periods.push({ value: `V-${year}`, label: `Vår ${year}` });
      periods.push({ value: `H-${year}`, label: `Høst ${year}` });
    }
  }

  // Sort by year, then by season (V first, then H)
  return periods.sort((a, b) => {
    const [seasonA, yearA] = a.value.split("-");
    const [seasonB, yearB] = b.value.split("-");

    // First compare by year
    if (yearA !== yearB) {
      return parseInt(yearA) - parseInt(yearB);
    }

    // If same year, V comes before H
    if (seasonA === "V" && seasonB === "H") return -1;
    if (seasonA === "H" && seasonB === "V") return 1;
    return 0;
  });
}

export default function FagkodeTable({
  showFiles = true,
  showPlagiarism = true,
  showAdditionalColumns = true,
  showBlobCheck = false,
  apiEndpoint,
  currentYearOnly = false,
}: FagkodeTableProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [data, setData] = useState<IFagkodeShared[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriode, setSelectedPeriode] = useState<string>(
    getDefaultEksamensperiode()
  );

  // Blob checking state
  const [blobCheckResult, setBlobCheckResult] =
    useState<BlobCheckResult | null>(null);
  const [checkingBlobs, setCheckingBlobs] = useState(false);
  const [showMissingFilesModal, setShowMissingFilesModal] = useState(false);
  const [blobCheckResults, setBlobCheckResults] = useState<
    Map<string, boolean>
  >(new Map());
  const [filteredData, setFilteredData] = useState<IFagkodeShared[]>([]);

  const perioder = generateEksamensperioder(currentYearOnly);

  // Create columns based on options
  const columns = createFagkodeColumns({
    showFiles,
    showPlagiarism,
    showAdditionalColumns,
    blobCheckResults,
  });

  // Initialize selectedPeriode from URL params on mount
  useEffect(() => {
    const periodeParam = searchParams.get("periode");
    if (periodeParam && perioder.some((p) => p.value === periodeParam)) {
      setSelectedPeriode(periodeParam);
    }
  }, [searchParams, perioder]);

  const fetchData = async (periode: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `${apiEndpoint}?eksamensperiode=${encodeURIComponent(periode)}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: FagkodeResponse = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError("Feil ved henting av data");
      }
    } catch (error) {
      console.error("Error fetching fagkode data:", error);
      setError(error instanceof Error ? error.message : "Ukjent feil");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(selectedPeriode);
  }, [selectedPeriode, apiEndpoint]);

  const handlePeriodeChange = (value: string) => {
    setSelectedPeriode(value);

    // Update URL with new periode
    const params = new URLSearchParams(searchParams.toString());
    params.set("periode", value);
    const newURL = `?${params.toString()}`;
    router.replace(newURL, { scroll: false });
  };

  const exportToCSV = () => {
    const exportData = filteredData.length > 0 ? filteredData : data;

    if (!exportData || exportData.length === 0) {
      alert('Ingen data å eksportere');
      return;
    }

    // CSV headers
    const headers = [
      'Fagkode',
      'Variant',
      'Fagnavn',
      'Eksamensdato',
      'Eksamenstid',
      'Varighet (min)',
      'Todelt Start Del 2',
      'Test Fagkode',
      'Plagiatkontroll',
      'Antall Filer',
      'Eksamensdeler',
      'Fil Detaljer'
    ];

    // Generate CSV rows
    const csvRows = exportData.map(item => {
      const eksamensdato = item.eksamensdato
        ? new Date(item.eksamensdato).toLocaleDateString('nb-NO')
        : '';

      const todeltStart = item.todeltStartDel2
        ? new Date(item.todeltStartDel2).toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })
        : '';

      const totalFiles = item.eksamensdeler?.reduce((total, del) =>
        total + (del.eksamensmateriell?.length || 0), 0) || 0;

      const eksamensdeler = item.eksamensdeler?.map(del => del.eksamensdelType).join('; ') || '';

      const filDetaljer = item.eksamensdeler?.map(del =>
        `${del.eksamensdelType}: ${del.eksamensmateriell?.map(mat => mat.filnavn).join(', ') || 'Ingen filer'}`
      ).join(' | ') || '';

      return [
        `"${item.fagkode}"`,
        `"${item.variantkode || ''}"`,
        `"${item.fagnavn}"`,
        `"${eksamensdato}"`,
        `"${item.eksamenstid || ''}"`,
        `"${item.varighet || ''}"`,
        `"${todeltStart}"`,
        `"${item.erTestFagkode ? 'Ja' : 'Nei'}"`,
        `"${item.harPlagiatkontroll ? 'Ja' : 'Nei'}"`,
        `"${totalFiles}"`,
        `"${eksamensdeler}"`,
        `"${filDetaljer}"`
      ].join(',');
    });

    const csvContent = [headers.join(','), ...csvRows].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    const isFiltered = filteredData.length > 0 && filteredData.length < data.length;
    const filename = `fagkoder-${selectedPeriode}${isFiltered ? '-filtrert' : ''}-${new Date().toISOString().split('T')[0]}.csv`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  };

  const checkBlobExistence = async () => {
    if (!data || data.length === 0) {
      alert("Ingen data å sjekke");
      return;
    }

    setCheckingBlobs(true);

    try {
      // Collect all blob names from the data (including duplicates for different eksamensdeler)
      const blobNames = data.flatMap(
        (item) =>
          item.eksamensdeler?.flatMap((del) =>
            del.eksamensmateriell
              ?.filter((mat) => mat.blobReferanseEksamensmateriell)
              ?.map((mat) => mat.blobReferanseEksamensmateriell!)
          ) || []
      );

      if (blobNames.length === 0) {
        alert("Ingen filer å sjekke");
        return;
      }

      const response = await fetch("/api/admin/check-blob-existence", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          blobNames, // Keep original blob names for accurate summary
          fagkodeData: data, // Send all fagkode data for complete report
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: BlobCheckResult = await response.json();

      if (result.success) {
        setBlobCheckResult(result);

        // Create a map for quick lookup
        const resultMap = new Map<string, boolean>();
        result.results.forEach((r) => resultMap.set(r.blobName, r.exists));
        setBlobCheckResults(resultMap);

        // Auto-open modal if there are missing files
        if (result.missingFilesReport?.length > 0) {
          setShowMissingFilesModal(true);
        }
      } else {
        throw new Error("Blob-sjekk feilet");
      }
    } catch (error) {
      console.error("Error checking blobs:", error);
      setError(
        error instanceof Error ? error.message : "Feil ved sjekking av filer"
      );
    } finally {
      setCheckingBlobs(false);
    }
  };

  if (loading) {
    return (
      <TableSkeletonFagkode
        showFiles={showFiles}
        showPlagiarism={showPlagiarism}
        showAdditionalColumns={showAdditionalColumns}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between flex-wrap gap-4">
        <div className="flex items-center gap-4">
          <label htmlFor="periode-select" className="text-sm font-medium">
            Eksamensperiode:
          </label>
          <Select value={selectedPeriode} onValueChange={handlePeriodeChange}>
            <SelectTrigger
              className="w-48 border-black border-[2px] h-12"
              id="periode-select"
            >
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {perioder.map((periode) => (
                <SelectItem key={periode.value} value={periode.value}>
                  {periode.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Button
            variant="ghost"
            onClick={exportToCSV}
            disabled={data.length === 0 || loading}
            title="Eksporter tabelldata som CSV"
          >
            <FaDownload className="h-4 w-4 mr-2" />
            Eksporter CSV
          </Button>
          {showBlobCheck && (
            <Button
              variant="ghost"
              onClick={checkBlobExistence}
              disabled={checkingBlobs || data.length === 0 || loading}
              title="Sjekk om alle filer finnes i blob storage"
            >
              <MdCloudDone className="h-4 w-4 mr-2" />
              {checkingBlobs
                ? "Sjekker filer..."
                : "Kontroller verifiserte filer"}
            </Button>
          )}
        </div>
      </div>

      {/* Blob check results */}
      {blobCheckResult && (
        <div className="p-4 border rounded bg-gray-50">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div>
              <h3 className="font-semibold mb-2">
                Verifiserte filer kontroll resultat:
              </h3>
              <div className="flex gap-4 text-sm flex-wrap">
                <span className="flex items-center gap-1 text-green-600">
                  <FaCheck className="h-3 w-3" />
                  {blobCheckResult.summary.existing} finnes
                </span>
                <span className="flex items-center gap-1 text-red-600">
                  <FaExclamationTriangle className="h-3 w-3" />
                  {blobCheckResult.summary.missing} mangler
                </span>
                <span className="text-gray-600">
                  (Total: {blobCheckResult.summary.total})
                </span>
              </div>
            </div>
            <div className="flex gap-2">
              {(blobCheckResult.missingFilesReport?.length > 0 ||
                blobCheckResult.summary.missing > 0) && (
                <Button
                  variant="ghost"
                  onClick={() => setShowMissingFilesModal(true)}
                  title="Vis detaljert rapport over manglende filer"
                >
                  <FaExclamationTriangle className="h-4 w-4 mr-2" />
                  Vis manglende filer (
                  {blobCheckResult.missingFilesReport?.length ||
                    blobCheckResult.summary.missing}
                  )
                </Button>
              )}
              <Button
                variant="ghost"
                onClick={() => {
                  setBlobCheckResult(null);
                  setBlobCheckResults(new Map());
                }}
                title="Lukk blob-sjekk resultat"
              >
                <FaTimes className="h-4 w-4 mr-2" />
                Lukk
              </Button>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded">
          <p className="font-medium">Feil:</p>
          <p>{error}</p>
        </div>
      )}

      <FagkodeDataTable
        columns={columns}
        data={data}
        perioder={perioder}
        selectedPeriode={selectedPeriode}
        showFiles={showFiles}
        onFilteredDataChange={setFilteredData}
      />

      {/* Missing Files Modal */}
      {showMissingFilesModal && blobCheckResult && (
        <MissingFilesReport
          missingFiles={blobCheckResult.missingFilesReport || []}
          onClose={() => setShowMissingFilesModal(false)}
        />
      )}
    </div>
  );
}
