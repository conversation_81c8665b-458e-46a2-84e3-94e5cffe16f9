"use client";

import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import "dayjs/locale/nb"; // Import Norwegian locale
import { IActivityLogColumnDef } from "@/interface/IActivityLogColumnDef";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import {
  HiOutlineArrowNarrowDown,
  HiOutlineArrowNarrowUp,
} from "react-icons/hi";
import { HiCheck } from "react-icons/hi2";

dayjs.extend(utc);
dayjs.extend(timezone);

// Function to create activity log columns based on whether it's a one-part exam
export const createActivityLogColumns = (twoPartExam: boolean): ColumnDef<IActivityLogColumnDef>[] => {
  const baseColumns: ColumnDef<IActivityLogColumnDef>[] = [
    {
      accessorKey: "Timestamp",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Tidspunkt
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
      cell: ({ row }) => {
        const timestampString = row.original.TimestampString?.trim();
        if (!timestampString) return <div>-</div>;

        try {
          const date = dayjs.utc(timestampString).tz("Europe/Oslo");

          if (date.isValid()) {
            return <div>{date.format("HH:mm:ss")}</div>;
          }
          return <div>-</div>;
        } catch (error) {
          console.warn("Error formatting timestamp:", error);
          return <div>-</div>;
        }
      },
    },
    {
      accessorKey: "Rolle",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Rolle
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
    },
    {
      accessorKey: "operation_Operasjonstype",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Aktivitet
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
    },
    {
      accessorKey: "operation_BeskrivelseMal",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Beskrivelse
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
    },
    {
      accessorKey: "Filnavn",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Filnavn
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col gap-2">
            {String(row.getValue("Filnavn"))
              .split("<br/>")
              .map((line: string, index: number) => (
                <div key={index} className="whitespace-pre-wrap">
                  {line}
                </div>
              ))}
          </div>
        );
      },
    },
   /*  {
      accessorKey: "Eksamensdel",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Eksamensdel
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
    }, */
  ];

  // Add Del1 and Del2 columns only for two-part exams
  if (twoPartExam) {
    baseColumns.push(
      {
        accessorKey: "Del1",
        header: ({ column }) => {
          const isSorted = column.getIsSorted();
          return (
            <Button
              variant="ghost"
              className="p-0 hover:bg-transparent"
              onClick={() => column.toggleSorting(isSorted === "asc")}
            >
              Del 1
              <div className="ml-2 flex items-center -space-x-[6px]">
                <HiOutlineArrowNarrowUp
                  className="h-[14px] w-[14px]"
                  strokeWidth={isSorted === "asc" ? 4 : 2}
                />
                <HiOutlineArrowNarrowDown
                  className="h-[14px] w-[14px]"
                  strokeWidth={isSorted === "desc" ? 4 : 2}
                />
              </div>
            </Button>
          );
        },
        cell: ({ row }) => {
          const value = row.getValue("Del1");
          if (value) {
            return (
              <div className="flex items-center justify-center">
                <HiCheck className="h-5 w-5 text-green-600" />
              </div>
            );
          }
          return <div></div>;
        },
        sortingFn: (rowA, rowB) => {
          const valueA = rowA.getValue("Del1");
          const valueB = rowB.getValue("Del1");
          
          // Convert to boolean for comparison (truthy values = 1, falsy = 0)
          const boolA = valueA ? 1 : 0;
          const boolB = valueB ? 1 : 0;
          
          return boolA - boolB;
        },
        sortUndefined: "last",
      },
      {
        accessorKey: "Del2",
        header: ({ column }) => {
          const isSorted = column.getIsSorted();
          return (
            <Button
              variant="ghost"
              className="p-0 hover:bg-transparent"
              onClick={() => column.toggleSorting(isSorted === "asc")}
            >
              Del 2
              <div className="ml-2 flex items-center -space-x-[6px]">
                <HiOutlineArrowNarrowUp
                  className="h-[14px] w-[14px]"
                  strokeWidth={isSorted === "asc" ? 4 : 2}
                />
                <HiOutlineArrowNarrowDown
                  className="h-[14px] w-[14px]"
                  strokeWidth={isSorted === "desc" ? 4 : 2}
                />
              </div>
            </Button>
          );
        },
        cell: ({ row }) => {
          const value = row.getValue("Del2");
          if (value) {
            return (
              <div className="flex items-center justify-center">
                <HiCheck className="h-5 w-5 text-green-600" />
              </div>
            );
          }
          return <div></div>;
        },
        sortingFn: (rowA, rowB) => {
          const valueA = rowA.getValue("Del2");
          const valueB = rowB.getValue("Del2");
          
          // Convert to boolean for comparison (truthy values = 1, falsy = 0)
          const boolA = valueA ? 1 : 0;
          const boolB = valueB ? 1 : 0;
          
          return boolA - boolB;
        },
        sortUndefined: "last",
      }
    );
  }

  return baseColumns;
};

// Default export for backward compatibility (shows all columns)
export const activityLogColumns = createActivityLogColumns(true);