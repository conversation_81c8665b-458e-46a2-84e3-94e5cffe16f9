import "reflect-metadata";
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { Fagkodeeksamen } from "./Fagkodeeksamen";

@Entity('Kandidatgruppe')
export class Kandidatgruppe {
    @PrimaryGeneratedColumn({ type: 'int' })
    KandidatgruppeID!: number;

    @Column({ type: 'varchar', length: 255, nullable: false })
    @Index()
    Kandidatgruppekode!: string;

    @Column({ type: 'varchar', length: 255, nullable: true })
    SkoleID?: string;

    @Column({ type: 'int', nullable: true })
    FagkodeeksamensID?: number;

    @Column({ type: 'varchar', length: 255, nullable: true })
    Kandidatgruppenavn?: string;

    @CreateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    CreatedDate!: Date;

    @UpdateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    ModifiedDate!: Date;

    // Relationship to Fagkodeeksamen
    @ManyToOne(() => Fagkodeeksamen)
    @JoinColumn({ name: "FagkodeeksamensID" })
    fagkodeeksamen?: Fagkodeeksamen;
}