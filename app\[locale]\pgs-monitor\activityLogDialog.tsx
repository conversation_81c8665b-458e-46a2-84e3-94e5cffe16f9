"use client";
import React, { useEffect, useState } from "react";
import { ICandidate } from "@/interface/ICandidate";
import { toast } from "@/components/ui/use-toast";
import { AuditLogDataTable } from "@/components/tables/kandidatmonitor/activityLog/activityLogDataTable";
import { createActivityLogColumns } from "@/components/tables/kandidatmonitor/activityLog/activityLogColumns";
import { ActivityLogSkeleton } from "./ActivityLogSkeleton"; // Import skeleton
import dayjs from "dayjs";
import { IActivityLogColumnDef } from "@/interface/IActivityLogColumnDef";

interface CandidateInfoModalProp {
  candidate: ICandidate;
  subjectCode: string;
  examDate?: string;
  twoPartExam: boolean; // Optional prop to indicate if it's a one-part exam
}

export default function ActivityLogDialog({
  candidate,
  subjectCode,
  examDate,
  twoPartExam
}: CandidateInfoModalProp) {
  const [activityLogs, setActivityLogs] = useState<IActivityLogColumnDef[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Add loading state

  useEffect(() => {
    const abortController = new AbortController();

    async function fetchActivityLog() {
      setIsLoading(true);
      const body = { userId: candidate.userId };
      try {
        const response = await fetch(
          `${window.location.origin}/api/getActivityLogV2`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(body),
            signal: abortController.signal,
          }
        );

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const result = await response.json();
        
        if (!abortController.signal.aborted) {
          setActivityLogs(result);
        }
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          // Request was aborted, don't show error
          return;
        }
        console.log(error);
        toast({
          title: "Feil ved henting av aktivitetslogg",
          description: "Noe gikk galt, prøv igjen senere.",
          variant: "destructive",
        });
      } finally {
        if (!abortController.signal.aborted) {
          setIsLoading(false);
        }
      }
    }
    
    fetchActivityLog();

    return () => {
      abortController.abort();
    };
  }, [candidate.userId]);

  // Function to determine if it's a two-part exam based on activity log data
  const isTwoPartExam = (data: IActivityLogColumnDef[]): boolean => {
    if (data.length === 0) return false;

    // Check if any log entry has Del1 or Del2 data (not null/empty)
    return data.some(
      (entry) =>
        (entry.Del1 && entry.Del1.trim()) || (entry.Del2 && entry.Del2.trim())
    );
  };

  if (isLoading) {
    return <ActivityLogSkeleton />;
  }

  return (
    <div className="mt-8">
      <h2 id="candidate-info-title" className="text-xl mb-5">
        Aktivitetslogg for {candidate.candidateName} (
        {candidate.candidateNumber}), {subjectCode},{" "}
        {dayjs(examDate).format("DD.MM.YYYY")}
      </h2>
      <div className="overflow-x-auto">
        <AuditLogDataTable
          columns={createActivityLogColumns(twoPartExam)}
          data={activityLogs}
        />
      </div>
      <div className="flex flex-col gap-8">
        <div role="region" aria-label="Kandidatens besvarelser"></div>
        <p></p>
      </div>
    </div>
  );
}
