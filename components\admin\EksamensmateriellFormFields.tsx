import { memo, useCallback, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { CustomDropdown } from "@/components/ui/CustomDropdown";
import { getExamMaterialCategoryOptions } from "@/interface/IExamMaterialCategory";

interface FormData {
  eksamensmaterielltype: string;
  fagkode: string;
  variant: string;
  eksamensperiode: string;
  eksamensdel: string;
  malForm: string;
  opphavsrett: boolean;
  vedlegg: boolean;
}

interface EksamensmateriellFormFieldsProps {
  formData: FormData;
  handleInputChange: (field: keyof FormData, value: string | boolean) => void;
  isLoading: boolean;
  replacementMode?: boolean;
}

const EXAM_MATERIAL_CATEGORY_OPTIONS = getExamMaterialCategoryOptions();

const EKSAMENSPERIODER = ["V-2025", "H-2025"];

const EKSAMENSDELER = [
  "Eksamen",
  "Forberedelse",
  "Eksamen del 1",
  "Eksamen del 2",
  "Eksamen del 1 og del 2",
];

const MALFORMER = ["Felles", "Bokmål", "Nynorsk"];

interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  disabled?: boolean;
  lockMessage?: string;
  required?: boolean;
  helpText?: string;
  fieldId?: string;
}

const FormField = memo(function FormField({
  label,
  children,
  disabled,
  lockMessage,
  required = false,
  helpText,
  fieldId,
}: FormFieldProps) {
  const helpId = helpText && fieldId ? `${fieldId}-help` : undefined;

  return (
    <div className="space-y-2">
      <Label htmlFor={fieldId} className={disabled ? "text-gray-500" : ""}>
        {label}{required && " *"}
        {lockMessage && <span className="text-xs">({lockMessage})</span>}
      </Label>
      {helpText && (
        <p id={helpId} className="text-sm text-muted-foreground">
          {helpText}
        </p>
      )}
      {children}
    </div>
  );
});

export const EksamensmateriellFormFields = memo(
  function EksamensmateriellFormFields({
    formData,
    handleInputChange,
    isLoading,
    replacementMode = false,
  }: EksamensmateriellFormFieldsProps) {
    const formatPeriode = useCallback(
      (periode: string) =>
        `${periode} (${periode.startsWith("H") ? "Høst" : "Vår"} ${
          periode.split("-")[1]
        })`,
      []
    );

    const examMaterialOptions = useMemo(
      () => getExamMaterialCategoryOptions(),
      []
    );
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fagkode + Variant på samme rad */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="w-full sm:w-[60%]">
            <FormField
              label="Fagkode"
              required={true}
              disabled={replacementMode}
              lockMessage={replacementMode ? "låst ved erstatning" : undefined}
              fieldId="fagkode"
            >
              <Input
                id="fagkode"
                type="text"
                value={formData.fagkode}
                onChange={(e) => handleInputChange("fagkode", e.target.value)}
                disabled={isLoading || replacementMode}
                placeholder="Skriv inn fagkode (eks: FSP5960)"
                autoComplete="off"
                aria-required={true}
                className={`h-10 w-full py-2 text-sm border-0 border-b-2 border-black rounded-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0 ${
                  replacementMode
                    ? "bg-gray-200 cursor-not-allowed"
                    : "bg-udirGray-100"
                }`}
              />
            </FormField>
          </div>
          <div className="w-full sm:w-[40%]">
            <FormField
              label="Variant"
              disabled={replacementMode}
              lockMessage={replacementMode ? "låst ved erstatning" : undefined}
              fieldId="variant"
            >
              <Input
                id="variant"
                type="text"
                value={formData.variant}
                onChange={(e) => handleInputChange("variant", e.target.value)}
                disabled={isLoading || replacementMode}
                placeholder="Skriv inn variant (valgfritt)"
                className={`h-10 w-full py-2 text-sm border-0 border-b-2 border-black rounded-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0 ${
                  replacementMode
                    ? "bg-gray-200 cursor-not-allowed"
                    : "bg-udirGray-100"
                }`}
              />
            </FormField>
          </div>
        </div>

        <FormField
          label="Eksamensperiode"
          required={true}
          disabled={replacementMode}
          lockMessage={replacementMode ? "låst ved erstatning" : undefined}
          fieldId="eksamensperiode"
        >
          <CustomDropdown
            value={formData.eksamensperiode}
            onValueChange={(value) =>
              handleInputChange("eksamensperiode", value)
            }
            options={EKSAMENSPERIODER}
            placeholder="Velg eksamensperiode"
            disabled={isLoading || replacementMode}
            label="Eksamensperiode"
            formatOption={formatPeriode}
            aria-required={true}
            id="eksamensperiode"
          />
        </FormField>

        <FormField
          label="Eksamensmaterielltype"
          required={true}
          fieldId="eksamensmaterielltype"
        >
          <CustomDropdown
            value={formData.eksamensmaterielltype}
            onValueChange={(value) =>
              handleInputChange("eksamensmaterielltype", value)
            }
            options={examMaterialOptions}
            placeholder="Velg eksamensmaterielltype"
            disabled={isLoading}
            label="Eksamensmaterielltype"
            aria-required={true}
            id="eksamensmaterielltype"
          />
        </FormField>

        <FormField
          label="Eksamensdel"
          required={true}
          fieldId="eksamensdel"
        >
          <CustomDropdown
            value={formData.eksamensdel}
            onValueChange={(value) => handleInputChange("eksamensdel", value)}
            options={EKSAMENSDELER}
            placeholder="Velg eksamensdel"
            disabled={isLoading}
            label="Eksamensdel"
            aria-required={true}
            id="eksamensdel"
          />
        </FormField>

        <FormField
          label="Målform"
          required={true}
          fieldId="malform"
        >
          <CustomDropdown
            value={formData.malForm}
            onValueChange={(value) => handleInputChange("malForm", value)}
            options={MALFORMER}
            placeholder="Velg målform"
            disabled={isLoading}
            label="Målform"
            aria-required={true}
            id="malform"
          />
        </FormField>

        {/* Checkbox fields grouped in fieldset */}
        <fieldset className="border border-gray-200 rounded-md p-4">
          <legend className="text-sm font-medium px-2 text-gray-700">
            Tilleggsalternativer
          </legend>
          <div className="flex items-center space-x-8 mt-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="opphavsrett"
                checked={formData.opphavsrett}
                onCheckedChange={(checked) =>
                  handleInputChange("opphavsrett", Boolean(checked))
                }
                disabled={isLoading}
              />
              <Label
                htmlFor="opphavsrett"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Opphavsrett
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="vedlegg"
                checked={formData.vedlegg}
                onCheckedChange={(checked) =>
                  handleInputChange("vedlegg", Boolean(checked))
                }
                disabled={isLoading}
              />
              <Label
                htmlFor="vedlegg"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Vedlegg
              </Label>
            </div>
          </div>
        </fieldset>
      </div>
    );
  }
);
