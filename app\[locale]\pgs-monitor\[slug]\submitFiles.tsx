"use client";

import React, { useEffect, useState } from "react";
import { toast } from "@/components/ui/use-toast";
import { useRole } from "@/context/RoleContext";
import { useSession } from "next-auth/react";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { fileValidator, createRejectedFile, validateFile } from "./fileUtils";
import { FileUploadArea } from "../../../../components/fileUploadArea";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import TabsComponent from "./tabs";
import useFilehandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import { IBatchInfo } from "@/interface/IBatchInfo";
import dayjs from "dayjs";
import { DocumentStatusEnum } from "@/enums/DocumentStatusEnum";
import { IExamPaperPayload } from "@/interface/IExamPaperPayload";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { getCurrentTermCode } from "@/lib/shared/getCurrentTermCode";
import CandidateUploadSkeleton from "./candidateUploadSkeleton";
import { useSearchParams } from "next/navigation";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FileRejection } from "react-dropzone";
import ErrorDisplay from "../ErrorDisplay";

interface ISubmitFilesProps {
  mimeTypes: IAllowedMimeTypes[];
  slug: string;
}

export default function SubmitFiles({ mimeTypes, slug }: ISubmitFilesProps) {
  // State declarations
  const [isUploading, setIsUploading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [acceptedFilesFromDrop, setAcceptedFilesFromDrop] = useState<File[]>(
    []
  );
  const [selectedCandidate, setSelectedCandidate] =
    useState<ICandidateMonitor>();
  const [selectedTab, setSelectedTab] = useState("opplastedeFiler");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const searchParams = useSearchParams();

  // Constants
  const MAX_BATCH_FILE_COUNT = 100;
  const MAX_BATCH_SIZE = 300 * 1024 * 1024; // 300 MB in bytes

  // Hooks
  const { selectedRole } = useRole();
  const { status: authStatus } = useSession();
  const {
    uploadedFiles,
    setUploadedFiles,
    addFile,
    deliverFile,
    candidateNumber,
    setCandidateNumber,
  } = useFilehandlerForMonitor();

  const [batchInfo, setBatchInfo] = useState<IBatchInfo>({
    totalFiles: 0,
    uploadedFiles: 0,
    rejectedFiles: 0,
    totalSize: 0,
  });

  // Initialize candidate and files
  useEffect(() => {
    const initializeCandidate = async () => {
      setIsInitializing(true);
      try {
        // Get candidate from sessionStorage
        const dataFromStorage = sessionStorage.getItem("selectedCandidate");
        if (!dataFromStorage) {
          toast({
            variant: "destructive",
            title: "Ingen kandidat valgt",
            description: "Vennligst velg en kandidat først.",
          });
          return;
        }

        // Parse and set initial candidate data
        const storedDataJson: ICandidateMonitor = JSON.parse(dataFromStorage);
        setCandidateNumber(storedDataJson.candidateNumber);
        setSelectedCandidate(storedDataJson);

        // Fetch updated data if we have necessary info
        if (selectedRole?.selectedSchoolId && authStatus === "authenticated") {
          const body: IExamPaperPayload = {
            schoolId: selectedRole.selectedSchoolId,
            period: getCurrentTermCode(),
          };

          const response = await fetch(
            `${window.location.origin}/api/getCandidatesMonitor`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(body),
            }
          );

          if (!response.ok) {
            throw new Error("Kunne ikke hente kandidatdata");
          }

          const data: IExamPaperInternal[] = await response.json();

          // Find relevant candidate and group
          const filteredGroup = data.find(
            (group) => group.groupCode === storedDataJson.groupCode
          );

          const filteredCandidate = filteredGroup?.candidates.find(
            (can) => can.candidateNumber === storedDataJson.candidateNumber
          );

          if (filteredCandidate) {
            // Update candidate with new documents
            setSelectedCandidate((prevCandidate) => ({
              ...storedDataJson,
              documents: filteredCandidate.documents,
            }));

            // Initialize files
            const existingFiles: IUploadedFile[] =
              filteredCandidate.documents.map((doc) => {
                // Check if file was previously marked as checked
                const storageKey = `fileChecked_${storedDataJson.userId}_${doc.documentCode}`;
                const isChecked = sessionStorage.getItem(storageKey) === "true";
                
                return {
                  FileGuid: doc.documentCode,
                  File: new File([], doc.fileName),
                  UploadedDate: dayjs(doc.timestamp),
                  Name: doc.fileName,
                  SubjectCode: storedDataJson.subjectCode,
                  Size: Number(doc.fileSize),
                  TestPartId: doc.testPartId,
                  Errors: [],
                  IsRejected: false,
                  IsLoading: false,
                  Candididate: storedDataJson.candidateNumber,
                  UploadFinished: true,
                  SchoolId: selectedRole.selectedSchoolId,
                  IsDeleting: false,
                  Groupcode: storedDataJson.groupCode,
                  Delivered: doc.status === DocumentStatusEnum.Delivered,
                  SubmittedDate: dayjs(doc.timestamp),
                  IsSubmitting: false,
                  UploadedBy: doc.uploadedBy,
                  Checked: isChecked,
                };
              });

            setUploadedFiles(existingFiles);
          }
        }
      } catch (error) {
        console.error("Error initializing candidate:", error);
        toast({
          variant: "destructive",
          title: "Kunne ikke laste kandidatdata",
          description: "Vennligst prøv igjen senere eller kontakt support.",
        });
        setErrorMessage("Kunne ikke laste kandidatdata. Prøv igjen senere.");
      } finally {
        setIsInitializing(false);
      }
    };

    initializeCandidate();
  }, [selectedRole?.selectedSchoolId, authStatus]);

  // Handle file drop
  const onDropFiles = async (
    acceptedFiles: File[],
    fileRejections: FileRejection[]
  ) => {
    if (isUploading) {
      return;
    }

    setIsUploading(true);
    setSelectedTab("opplastedeFiler");
    resetBatchInfo(acceptedFiles, fileRejections);

    try {
      // Batch size and count validation
      const batchTotalFiles = acceptedFiles.length + fileRejections.length;
      const batchTotalSize = acceptedFiles.reduce(
        (sum, file) => sum + file.size,
        0
      );

      if (batchTotalFiles > MAX_BATCH_FILE_COUNT) {
        toast({
          variant: "destructive",
          title: "For mange filer",
          description: `Du kan ikke laste opp mer enn ${MAX_BATCH_FILE_COUNT} filer om gangen.`,
        });
        return;
      }

      if (batchTotalSize > MAX_BATCH_SIZE) {
        toast({
          variant: "destructive",
          title: "Størrelse overskredet",
          description: `Total filstørrelse for hver opplasting kan ikke overstige ${
            MAX_BATCH_SIZE / (1024 * 1024)
          } MB.`,
        });
        return;
      }

      // Handle rejected files first
      for (const rejection of fileRejections) {
        const rejectedFile = await createRejectedFile(
          rejection.file,
          rejection.errors.map((error) => error.message)
        );
        await addFile(rejectedFile);
      }

      // Process accepted files sequentially
      let allFilesValid = true;
      let successCount = 0;
      let failCount = 0;

      const existingFiles = uploadedFiles.filter(
        (file) => file.SchoolId === selectedRole?.selectedSchoolId
      );

      const existingFileNames = new Set(
        existingFiles
          .filter((f) => !f.IsRejected)
          .map((f) => f.Name.replace(/\.[^.]+$/, "").toLowerCase())
      );

      // Process files one at a time
      for (const file of acceptedFiles) {
        try {
          const result = await validateFile(
            file,
            existingFileNames,
            existingFiles.length,
            existingFiles.reduce((sum, f) => sum + f.Size, 0),
            mimeTypes,
            selectedRole?.selectedSchoolId ?? "",
            candidateNumber
          );

          if (!result.isValid) {
            allFilesValid = false;
            failCount++;
          } else {
            successCount++;
          }

          await addFile(result.file);         

          // Update batch info after each file
          updateBatchInfo({
            totalFiles: batchTotalFiles,
            uploadedFiles: successCount,
            rejectedFiles: failCount + fileRejections.length,
            totalSize: batchTotalSize,
          });
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
          const errorFile = await createRejectedFile(file, [
            (error as Error).message || "Ukjent feil under filbehandling",
          ]);

          await addFile(errorFile);
          allFilesValid = false;
          failCount++;

          updateBatchInfo({
            totalFiles: batchTotalFiles,
            uploadedFiles: successCount,
            rejectedFiles: failCount + fileRejections.length,
            totalSize: batchTotalSize,
          });
        }
      }

      // Switch to error tab if there are any errors
      if (!allFilesValid || fileRejections.length > 0) {
        setSelectedTab("feilFiler");
      }
    } catch (error) {
      console.error("Error processing files:", error);
      toast({
        variant: "destructive",
        title: "En feil er oppstått under opplasting av filer",
        description:
          "Vennligst prøv igjen senere. Dersom problemet vedvarer, kontakt brukerstøtte.",
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Reset batch info for new uploads
  const resetBatchInfo = (acceptedFiles: File[], fileRejections: any[]) => {
    const totalFiles = acceptedFiles.length + fileRejections.length;
    const totalSize = acceptedFiles.reduce((sum, file) => sum + file.size, 0);

    setBatchInfo({
      totalFiles,
      uploadedFiles: 0,
      rejectedFiles: 0,
      totalSize,
    });
  };

  // Update batch info
  const updateBatchInfo = (newInfo: IBatchInfo) => {
    setBatchInfo(newInfo);
  };

  const getPaperDeliveryMessage = () => {
    const status1 = Number(searchParams.get("part1"));
    const status2 = Number(searchParams.get("part2"));
    const messages = [];

    if (status1 === CandidateStatusEnum.LevertManuelt) {
      messages.push("Del 1");
    }

    if (status2 === CandidateStatusEnum.LevertManuelt) {
      messages.push("Del 2");
    }

    if (messages.length === 0) return null;

    return (
      <div className="text-red-600 font-medium mt-2">
        <strong>OBS:</strong> {messages.join(" og ")} har status{" "}
        <i>Sendes i posten</i>
      </div>
    );
  };

  return (
    <div className="flex flex-col gap-6">
      {isInitializing ? (
        <CandidateUploadSkeleton />
      ) : (
        <>
          {errorMessage &&
            ErrorDisplay({
              errors: [{ message: errorMessage, type: "Error" }],
            })}

          <div
            className={
              errorMessage
                ? "flex flex-col gap-1 bg-stalbla rounded-md p-4 w-full lg:w-2/5 opacity-50 pointer-events-none"
                : "flex flex-col gap-1 bg-stalbla rounded-md p-4 w-full lg:w-2/5"
            }
          >
            <span className="text-lg">{`Last opp filer for ${selectedCandidate?.candidateName}`}</span>
            <div className="py-2 text-sm">
              <div className="flex items-center gap-2">
                <span>Kandidatnummer:</span>
                <span className="font-semibold">
                  {errorMessage ? "" : selectedCandidate?.candidateNumber}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span>Fagkode:</span>
                <span className="font-semibold">
                  {errorMessage ? "" : selectedCandidate?.subjectCode}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span>Fagnavn:</span>
                <span className="font-semibold">
                  {errorMessage ? "" : selectedCandidate?.subjectName}
                </span>
              </div>
              {getPaperDeliveryMessage()}
            </div>
          </div>
          <div>
            <Dialog>
              <DialogTrigger id="radix-:r0:" className="link">
                Se hvilke filtyper som godkjennes
              </DialogTrigger>
              <DialogContent className="sm:max-w-md bg-white p-6">
                <DialogHeader>
                  <DialogDescription>
                    {Array.from(
                      new Set(
                        mimeTypes.map((mimeType) =>
                          mimeType.FileExtension.replace(".", "")
                        )
                      )
                    ).join(", ")}
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          </div>

          <div
            className={
              errorMessage
                ? "flex flex-col gap-6 opacity-50 pointer-events-none"
                : "flex flex-col gap-6"
            }
          >
            <FileUploadArea
              onDrop={onDropFiles}
              isLoading={isUploading}
              fileValidator={(file) =>
                fileValidator(file, mimeTypes, uploadedFiles, selectedRole)
              }
              totalFilesInBatch={batchInfo.totalFiles}
              numUploadedFiles={batchInfo.uploadedFiles}
              numFilesRejected={batchInfo.rejectedFiles}
              totalSize={batchInfo.totalSize}
            />

            <TabsComponent />
          </div>
        </>
      )}
    </div>
  );
}
