"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { TestPartsEnum } from "@/enums/TestPart";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { ColumnDef, Table } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { IoWarning } from "react-icons/io5";
import useFileHandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import dayjs from "dayjs";
import { FiEdit2 } from "react-icons/fi";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { set } from "date-fns";
import { useState } from "react";
import { ImSpinner2 } from "react-icons/im";
import { GoTrash } from "react-icons/go";

function formatFileSize(bytes: number) {
  const KB = 1024;
  const MB = 1024 * KB;
  if (bytes < MB) {
    return `${(bytes / KB).toFixed(0)} kB`;
  } else {
    return `${(bytes / MB).toFixed(2)} MB`;
  }
}

function formatTestPartId(testpartId: TestPartsEnum) {
  switch (testpartId) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og 2";
    default:
      return (
        <div className="flex items-center gap-1 text-red-600">
          <IoWarning className="text-lg" />
          {"Angi eksamensdel"}
        </div>
      );
  }
}

function sumFileSize(table: Table<IUploadedFile>) {
  let totalSize = 0;
  const rowCount = table.getRowCount();
  for (let i = 0; i < rowCount; i++) {
    const row = table.getRow(i.toString()); // Convert i to a string
    const rowData = row.original;
    totalSize += rowData.Size;
  }
  return totalSize;
}

function minimizeSuffix(filename: string) {
  const parts = filename.split(".");
  if (parts.length > 1) {
    const suffix = parts.pop();
    return parts.join(".") + "." + suffix?.toLowerCase();
  }

  return filename;
}


export const getColumns = ({
  handleFileDelete,
  filesToDelete,
}: {
  handleFileDelete: (file: IUploadedFile) => Promise<void>;
  filesToDelete: Set<string>;
}): ColumnDef<IUploadedFile>[] => {
return [
  {
    accessorKey: "Name",
    header: ({ table, column }) => {
      const rowCount = table.getRowCount();
      return (
        <div className="text-left w-full">
          {" "}
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent justify-start w-full"
            onClick={() => {
              column.toggleSorting(column.getIsSorted() === "asc");
            }}
          >
            <div className="capitalize mr-2">Filer ({rowCount})</div>{" "}
            <ArrowUpDown
              className="h-4 w-4"
              role="img"
              aria-label="pil opp ned ikon"
            />
          </Button>
        </div>
      );
    },
    cell: ({ row }) => {
      const file = row.original.File;
      const fileName = file?.name || row.original.Name || "";
      const { downloadFile } = useFileHandlerForMonitor();

      return (
        <div className="text-left">
          <Button
            variant="link"
            className="mt-1 text-current  text-left p-0"
            onClick={() =>
              downloadFile(row.original.FileGuid, row.original.Name)
            }
          >
            {minimizeSuffix(fileName)}
          </Button>
        </div>
      );
    },
  },

  {
    accessorKey: "Size",
    header: ({ table, column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="">Størrelse {formatFileSize(sumFileSize(table))}</div>
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="pil opp ned ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="">{formatFileSize(row.getValue("Size"))}</div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "SubmittedDate",
    header: ({ table, column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize">Opplastet</div>
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="pil opp ned ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date: string = row.getValue("SubmittedDate");
      // Sjekk om date er en gyldig dato
      const formattedDate = dayjs(date).isValid()
        ? dayjs(date).format("HH:mm")
        : "Ugyldig dato";
      return <div className="capitalize">{formattedDate}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "UploadedBy",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Opplastet av{" "}
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-left">{row.original.UploadedBy}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "TestPartId",
    header: ({ table, column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize">Eksamensdel</div>
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="pil opp ned ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="capitalize">
        {formatTestPartId(row.getValue("TestPartId"))}
      </div>
    ),
    enableSorting: true,
  },
  {
        accessorKey: "Delete",
        header: () => <div></div>,
        cell: ({ row }) => (
          <Button
            variant="outline"
            className="flex items-center gap-1"
            disabled={
              row.original.IsDeleting ||
              row.original.IsSubmitting ||
              row.original.IsLoading ||
              filesToDelete.has(row.original.FileGuid) 
            }
            onClick={() => {
              handleFileDelete(row.original);
            }}
          >
            {row.original.IsDeleting ||
            filesToDelete.has(row.original.FileGuid) ? (
              <ImSpinner2
                className="animate-spin text-lg"
                role="img"
                aria-label="spinner"
              />
            ) : (
              <GoTrash role="img" aria-label="slett fil" className="text-lg" />
            )}
            <span>
              {row.original.IsDeleting || filesToDelete.has(row.original.FileGuid)
                ? "Sletter..."
                : "Slett"}
            </span>
          </Button>
        ),
      },/*
  {
    accessorKey: "edit",
    header: ({ table, column }) => {
      return <></>;
    },
    cell: ({ row }) => {
      const [warning, setWarning] = useState(false);
      const { markFileAsNotDelivered } = useFileHandlerForMonitor();
      return (
        <div className="capitalize">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => setWarning(true)}
          >
            <FiEdit2 role="img" aria-label="rediger ikon" />
            Rediger
          </Button>

          <Dialog open={warning} onOpenChange={setWarning}>
            <DialogContent className="sm:max-w-[50%] sm:max-h-[90vh] overflow-auto">
              <div className="flex flex-col ">
                <p>
                  {" "}
                  Du er i ferd med å endre en innlevert besvarelsesfil – bekreft
                  at du ønsker dette.
                </p>
                <p>
                  {" "}
                  Besvarelsesfilen vil i så fall bli flyttet til fanen
                  "Opplastede filer".
                </p>
              </div>
              <div className="flex  gap-4">
                <Button
                  variant="outline"
                  onClick={() => setWarning(false)}
                  className="w-24"
                >
                  Avbryt
                </Button>
                <Button
                  onClick={() => {
                    markFileAsNotDelivered(row.original.FileGuid);
                    setWarning(false);
                  }}
                  className="w-24"
                >
                  Bekreft
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      );
    },
    enableSorting: true,
  },*/
]};
