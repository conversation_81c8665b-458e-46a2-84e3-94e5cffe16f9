"use client";

import React, {
  createContext,
  useContext,
  ReactNode,
  useMemo,
  useState,
  useEffect,
  useCallback,
} from "react";
import { getSession } from "next-auth/react";
import { logException } from "@/lib/client/appInsightsClient";
import { ISchools } from "@/interface/ISchools";
import { ISession } from "@/interface/ISession";
import useSWR from "swr";


// Define the shape of the context data
interface SchoolDataContextType {
  schools: ISchools[];
  pasxSchools: IPasxSchool[];
  enhetsservice: IAuthority[];
  skoleansvarlig: IMunicipality[];
  combinedSchoolData: IPasxSchool[];
  isLoading: boolean;
  error: Error | null;
}

// Create the context with undefined as the default value
const SchoolDataContext = createContext<SchoolDataContextType | undefined>(
  undefined
);

// Custom hook to use the SchoolDataContext
export const useSchoolData = () => {
  const context = useContext(SchoolDataContext);
  if (context === undefined) {
    throw new Error("useSchoolData must be used within a SchoolDataProvider");
  }
  return context;
};

// Optimized fetcher function with better error handling
const fetcher = async (url: string) => {
  const res = await fetch(url, {
    cache: "force-cache",
    next: { revalidate: 600 }, // 10 minutes cache
  });
  if (!res.ok) {
    const error = new Error(`API Error: ${res.status}`);
    error.message = await res.text();
    throw error;
  }
  return res.json();
};

// SWR configuration for different data types
const swrConfig = {
  revalidateOnFocus: false,
  revalidateOnReconnect: false,
  dedupingInterval: 5 * 60 * 1000, // 5 minutes
  errorRetryCount: 3,
  errorRetryInterval: 1000,
};

const fastSwrConfig = {
  ...swrConfig,
  dedupingInterval: 2 * 60 * 1000, // 2 minutes for frequently changing data
};

const slowSwrConfig = {
  ...swrConfig,
  dedupingInterval: 15 * 60 * 1000, // 15 minutes for rarely changing data
};

interface SchoolDataProviderProps {
  children: ReactNode;
}

// Main SchoolDataProvider component
export const SchoolDataProvider: React.FC<SchoolDataProviderProps> = ({
  children,
}) => {
  // State for session data
  const [session, setSession] = useState<ISession | null>(null);

  // Determine if PASX data should be fetched based on user role
  const shouldFetchPasxData = useMemo(() => {
    if (!session?.user.role) return false;

    //TODO: Update with correct role values
    return session.user.role.some(
      (role: string) =>
        role.startsWith("urn:udir:eksamen:fk") ||
        role.startsWith("urn:udir:eksamen:sf") ||
        role.startsWith("urn:udir:eksamen:ko") ||
        role.startsWith("urn:udir:eksamen:fm") ||
        role.startsWith("urn:udir:pgsa:administrator") ||
        role.startsWith("urn:udir:eksamen:vea") ||
        role.startsWith("urn:udir:eksamen:sa")
    );
  }, [session]);

  // Fetch session data once on mount
  useEffect(() => {
    const fetchSession = async () => {
      try {
        const sessionData = (await getSession()) as ISession | null;
        setSession(sessionData);
      } catch (err) {
        logException(err as Error, { action: "SchoolDataContext-fetchSession" });
      }
    };
    fetchSession();
  }, []);

  // SWR hooks for data fetching with caching
  const { data: schools = [], error: schoolsError, isLoading: schoolsLoading } = useSWR<ISchools[]>(
    '/api/mySchools',
    fetcher,
    fastSwrConfig
  );

  const { data: pasxSchools = [], error: pasxError, isLoading: pasxLoading } = useSWR<IPasxSchool[]>(
    shouldFetchPasxData ? '/api/pasxSchools' : null,
    fetcher,
    slowSwrConfig
  );

  const { data: enhetsservice = [], error: enhetsError, isLoading: enhetsLoading } = useSWR<IAuthority[]>(
    shouldFetchPasxData ? '/api/authority' : null,
    fetcher,
    slowSwrConfig
  );

  const { data: skoleansvarlig = [], error: skoleansvarligError, isLoading: skoleansvarligLoading } = useSWR<IMunicipality[]>(
    shouldFetchPasxData ? '/api/municipality' : null,
    fetcher,
    slowSwrConfig
  );

  // Combine all errors and loading states
  const error = schoolsError || pasxError || enhetsError || skoleansvarligError;
  const isLoading = schoolsLoading || (shouldFetchPasxData && (pasxLoading || enhetsLoading || skoleansvarligLoading));

  // Log any errors that occur
  useEffect(() => {
    if (error) {
      logException(error, { action: "SchoolDataContext-SWR-Error" });
    }
  }, [error]);


  // Optimized school data combination with memoization
  const combinedSchoolData = useMemo(() => {
    if (!schools?.length) return [];

    const schoolMap = new Map<string, IPasxSchool>();

    // Populate the map with PASX school data if available
    if (pasxSchools?.length) {
      pasxSchools.forEach((school) => {
        schoolMap.set(school.organisasjonsnummer, school);
      });
    }

    // Update or add schools from the main school list
    schools.forEach((school) => {
      if (!schoolMap.has(school.schoolId)) {
        schoolMap.set(school.schoolId, {
          navn: school.schoolName,
          organisasjonsnummer: school.schoolId,
          besoksadresse: {
            postnummer: "",
            poststed: "",
            gateadresse: "",
            land: "",
          },
          erVideregaendeskole: false,
          skoleansvarligOrganisasjonsnummer: "",
          ansvarligFylkesmannOrganisasjonsnummer: "",
          erGrunnskole: false,
          erPrivatSkole: false,
          erAktiv: true,
        });
      }
    });

    // Convert map to array and sort using Norwegian locale
    const sortedSchools = Array.from(schoolMap.values()).sort((a, b) => {
      const collator = new Intl.Collator("nb-NO", { sensitivity: "base" });
      return collator.compare(a.navn, b.navn);
    });

    return sortedSchools;
  }, [schools, pasxSchools]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo<SchoolDataContextType>(
    () => ({
      schools,
      pasxSchools,
      enhetsservice,
      skoleansvarlig,
      combinedSchoolData,
      isLoading,
      error,
    }),
    [
      schools,
      pasxSchools,
      enhetsservice,
      skoleansvarlig,
      combinedSchoolData,
      isLoading,
      error,
    ]
  );

  // Provide the context value to children components
  return (
    <SchoolDataContext.Provider value={contextValue}>
      {children}
    </SchoolDataContext.Provider>
  );
};
