import "reflect-metadata";
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from "typeorm";

@Entity('Fagkodeeksamen')
@Index(['Fagkode', 'Variantkode', 'Eksamensperiode'], { unique: true })
export class Fagkodeeksamen {
    @PrimaryGeneratedColumn({ type: 'int' })
    FagkodeeksamensID!: number;

    @Column({ type: 'varchar', length: 10, nullable: false })
    Fagkode!: string;

    @Column({ type: 'varchar', length: 10, nullable: true })
    Variantkode?: string;

    @Column({ type: 'varchar', length: 50, nullable: false })
    Eksamensperiode!: string;

    @Column({ type: 'varchar', length: 255, nullable: false })
    Fagnavn!: string;

    @Column({ type: 'int', nullable: true })
    Varighet?: number | null;

    @Column({ type: 'datetime2', nullable: true })
    Eksamensdato?: Date | null;

    @Column({ type: 'varchar', length: 50, nullable: true })
    Eksamenstid?: string | null;

    @Column({ type: 'bit', default: false })
    ErTestFagkode!: boolean;

    @Column({ type: 'varchar', length: 255, nullable: true })
    Oppgaveansvar?: string | null;

    @Column({ type: 'nvarchar', length: 10, nullable: true })
    Opplaeringsniva?: string | null;

    @CreateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    CreatedDate!: Date;

    @UpdateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    ModifiedDate!: Date;

    // Lazy import to avoid circular dependency
    @OneToMany("Eksamensdel", (eksamensdel: any) => eksamensdel.fagkodeeksamen)
    eksamensdeler!: any[];

}
