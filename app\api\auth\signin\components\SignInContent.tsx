"use client";

import React, { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn, useSession } from "next-auth/react";
import { ImSpinner2 } from "react-icons/im";
import { Card, CardContent } from "@/components/ui/card";

export default function SignInContent() {
  const router = useRouter();
  const { status } = useSession();
  const searchParams = useSearchParams();

  useEffect(() => {
    async function signInWithProvider() {
      if (status === "unauthenticated") {
        await signIn("UIDP");
      } else if (status === "authenticated") {
        const state = searchParams.get("state");
        if (state) {
          router.push(decodeURIComponent(state));
        } else {
          router.push("/");
        }
      }
    }
    signInWithProvider();
  }, [status, router, searchParams]);

  return (
    <div className="fixed inset-0 bg-background flex items-center justify-center p-4">
      <Card className="w-auto min-w-[320px] max-w-md shadow-xl border-2">
        <CardContent className="flex flex-col items-center justify-center p-10 space-y-6">
          <div className="relative">
            <ImSpinner2
              className="animate-spin text-7xl text-primary drop-shadow-sm"
              aria-hidden="true"
              role="img"
              aria-label="loading spinner"
            />
            {/* Subtle pulse effect */}
            <div className="absolute inset-0 animate-ping">
              <ImSpinner2
                className="text-7xl text-primary/20"
                aria-hidden="true"
              />
            </div>
          </div>
          <div className="text-center space-y-2">
            <p className="text-xl font-semibold text-foreground">
              Logger inn...
            </p>
            <p className="text-sm text-muted-foreground">
              Vennligst vent mens vi autentiserer deg
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
