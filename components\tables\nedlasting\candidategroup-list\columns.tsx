"use client";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import {
  HiOutlineArrowNarrowDown,
  HiOutlineArrowNarrowUp,
} from "react-icons/hi";

export const columns: ColumnDef<IExamPaperInternal>[] = [
  /* {
    accessorKey: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },*/
  {
    accessorKey: "groupName",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      const getSortAriaLabel = () => {
        if (isSorted === "asc") return "Sortert stigende. Klikk for å sortere synkende.";
        if (isSorted === "desc") return "Sortert synkende. Klikk for å sortere stigende.";
        return "Ikke sortert. Klikk for å sortere stigende.";
      };

      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
          aria-label={`Sortér etter gruppe. ${getSortAriaLabel()}`}
          aria-sort={isSorted === "asc" ? "ascending" : isSorted === "desc" ? "descending" : "none"}
          role="columnheader"
        >
          Gruppe
          <div className="ml-2 flex items-center -space-x-[6px]" aria-hidden="true">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => {
      return <div className="capitalize font-medium" role="rowheader">{row.original.groupName}</div>;
    },
  },
  {
    accessorKey: "subjectCode",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      const getSortAriaLabel = () => {
        if (isSorted === "asc") return "Sortert stigende. Klikk for å sortere synkende.";
        if (isSorted === "desc") return "Sortert synkende. Klikk for å sortere stigende.";
        return "Ikke sortert. Klikk for å sortere stigende.";
      };

      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
          aria-label={`Sortér etter fagkode. ${getSortAriaLabel()}`}
          aria-sort={isSorted === "asc" ? "ascending" : isSorted === "desc" ? "descending" : "none"}
          role="columnheader"
        >
          Fagkode{" "}
          <div className="ml-2 flex items-center -space-x-[6px]" aria-hidden="true">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "subjectName",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      const getSortAriaLabel = () => {
        if (isSorted === "asc") return "Sortert stigende. Klikk for å sortere synkende.";
        if (isSorted === "desc") return "Sortert synkende. Klikk for å sortere stigende.";
        return "Ikke sortert. Klikk for å sortere stigende.";
      };

      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
          aria-label={`Sortér etter fagnavn. ${getSortAriaLabel()}`}
          aria-sort={isSorted === "asc" ? "ascending" : isSorted === "desc" ? "descending" : "none"}
          role="columnheader"
        >
          Fagnavn
          <div className="ml-2 flex items-center -space-x-[6px]" aria-hidden="true">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
  },
  /* {
    accessorKey: "variant",
    header: "Variant",
    enableSorting: true,
  },
  {
    accessorKey: "Period",
    header: "Periode",
    enableSorting: true,
  },*/
  {
    id: "totalCandidatesWithDocuments",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      const getSortAriaLabel = () => {
        if (isSorted === "asc") return "Sortert stigende. Klikk for å sortere synkende.";
        if (isSorted === "desc") return "Sortert synkende. Klikk for å sortere stigende.";
        return "Ikke sortert. Klikk for å sortere stigende.";
      };

      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
          aria-label={`Sortér etter antall besvarelser. ${getSortAriaLabel()}`}
          aria-sort={isSorted === "asc" ? "ascending" : isSorted === "desc" ? "descending" : "none"}
          role="columnheader"
        >
          Besvarelser
          <div className="ml-2 flex items-center -space-x-[6px]" aria-hidden="true">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    accessorFn: (row) => {
      // Calculate the number of candidates with documents
      return row.candidates.filter(candidate => candidate.documents.length > 0).length;
    },
    enableSorting: true,
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return <div>{value}</div>;
    },
  },
];

export default columns;
