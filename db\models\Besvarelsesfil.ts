import "reflect-metadata";
import {
  Entity,
  Column,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  PrimaryGeneratedColumn,
  OneToMany,
} from "typeorm";

@Entity("Besvarelsesfil")
export class Besvarelsesfil {
  @PrimaryGeneratedColumn({ type: "int" })
  BesvarelsesfilID!: number;

  @Column({ type: "varchar", length: 255, nullable: true })
  Besvarelseskode?: string;

  @Column({ type: "bigint", nullable: true, transformer: {
    to: (value: string | number | undefined) => value,
    from: (value: string | undefined) => value
  }})
  FileSize?: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  MimeType?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  OriginalNavn?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  StandardNavn?: string;

  @Column({ type: "varchar", length: 50, nullable: true })
  BesvarelsesfilStatus?: string;

  @Column({ type: "varchar", length: 50, nullable: true })
  LasteOppVia?: string;

  @Column({ type: "varchar", length: 50, nullable: true })
  LevertVia?: string;

  @Column({ type: "datetime2", nullable: true })
  LasteOppTimeStamp?: Date;

  @Column({ type: "datetime2", nullable: true })
  LevertTimeStamp?: Date;

  @Column({ type: "varchar", length: 255, nullable: true })
  LasteOppAv?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  LevertAv?: string;

  @CreateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  CreatedDate!: Date;

  @UpdateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  ModifiedDate!: Date;

  @Column({ type: "varchar", length: 255, nullable: true })
  CreatedBy?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  ModifiedBy?: string;

  // Lazy relationship to BesvarelsesDelBesvarelsesfil to avoid circular dependency
  @OneToMany(
    "BesvarelsesDelBesvarelsesfil",
    (junction: any) => junction.besvarelsesfil
  )
  besvarelsesDelBesvarelsesfiler?: any[];
}
