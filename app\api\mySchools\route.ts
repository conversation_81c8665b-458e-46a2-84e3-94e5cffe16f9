import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { ISession } from "@/interface/ISession";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import { generateAccessToken } from "@/lib/server/getAccessTokenForEksamen";

const telemetryClient = getAppInsightsServer();

const EksamenApiUrl = process.env.PGSA_EKSAMENAPI_URL;

export const dynamic = "force-dynamic";

export async function GET() {
  let response: Response | null = null;
  let responseText: string = "";
  let session: ISession | null = null;

  try {
    // Authentication check
    session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session?.user.userInfo.userId;

    if (!userId) {
      return NextResponse.json({ error: "UserId is missing" }, { status: 400 });
    }

    if (!EksamenApiUrl) {
      return NextResponse.json(
        { error: "API URL not configured" },
        { status: 500 }
      );
    }

    const accessToken = await generateAccessToken();

    const apiUrl = `${EksamenApiUrl}/schools/${userId}`;

    response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      responseText = await response.text();
      console.error(
        `Failed to fetch data from mySchools, StatusCode: ${response.status}, Message: ${responseText}`
      );
      throw new Error(
        `Failed to fetch data from external API: ${response.status} - ${responseText}`
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "GET-MySchools",
        statuscode: response ? response.status : 0,
        response: responseText || "Tom respons",
        userId: session?.user?.userInfo?.userId || "Unknown",
        apiUrl: EksamenApiUrl || "Not configured",
      },
    });

    return NextResponse.json(
      {
        error: "Internal Server Error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
