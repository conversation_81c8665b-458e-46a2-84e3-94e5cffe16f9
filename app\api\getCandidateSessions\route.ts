import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";
import { executeBatch, getSetMembers } from "@/app/lib/redisHelper";

const REDIS_FIELDS = [
  "candidateNumber",
  "candidateGroupCode",
  "userSessionId",
  "isAuthorized",
  "timeForSession",
  "ipAddress",
  "activeIp",
  "userId",
] as const;

function transformRedisData(hashValues: string[]): Partial<IRedisObject> {
  const result: Partial<IRedisObject> = {};
  REDIS_FIELDS.forEach((field, index) => {
    const value = hashValues[index];
    if (value !== null) {
      if (field === "isAuthorized") {
        result[field] = value === "true";
      } else {
        result[field] = value;
      }
    }
  });
  return result;
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }

  const candidateNumbers = await request.json();

  if (!Array.isArray(candidateNumbers)) {
    return Response.json(
      { message: "Array of candidateNumbers is required" },
      { status: 400 }
    );
  }

  if (candidateNumbers.length === 0) {
    return Response.json([], { status: 200 });
  }

  try {
    // 1. Create SMEMBERS commands for all candidate numbers
    const smembersCommands = candidateNumbers.map((cn) => ({
      command: "smembers" as const,
      args: [`candidateSessions:${cn}`],
    }));

    // 2. Execute SMEMBERS commands in a pipeline
    const resultsFromSmemberPipeline = await executeBatch<string[]>(
      smembersCommands
    );

    const allKeysToFetch: string[] = [];
    const keyToCandidateNumberMap: Record<string, string> = {};

    resultsFromSmemberPipeline.forEach((sessionIds, index) => {
      if (sessionIds && sessionIds.length > 0) {
        const candidateNumber = candidateNumbers[index]; // Get corresponding candidateNumber
        for (const sessionId of sessionIds) {
          const key = `candidate:${candidateNumber}:${sessionId}`;
          allKeysToFetch.push(key);
          keyToCandidateNumberMap[key] = candidateNumber; // Store for later use if needed
        }
      }
    });

    if (allKeysToFetch.length === 0) {
      return Response.json([], { status: 200 });
    }

    // 3. Create HMGET commands for all resolved session keys
    const hmgetCommands = allKeysToFetch.map((key) => ({
      command: "hmget" as const,
      args: [key, ...REDIS_FIELDS],
    }));

    // 4. Execute HMGET commands in a pipeline
    const hashResults = await executeBatch<string[]>(hmgetCommands);

    const transformedResults = hashResults
      .map((fieldValues, index) => {
        if (fieldValues && fieldValues.some((v) => v !== null)) {
          try {
            return transformRedisData(fieldValues);
          } catch (error) {
            console.error(
              `Error transforming Redis hash values for key ${allKeysToFetch[index]}:`,
              error
            );
            return null;
          }
        }
        return null;
      })
      .filter((item) => item !== null);

    return Response.json(transformedResults, { status: 200 });
  } catch (error) {
    console.error("Error fetching batch data:", error);
    return Response.json(
      {
        message: "Error fetching data",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
