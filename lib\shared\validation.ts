// Validation utilities for security and data integrity

// SQL injection patterns to block
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
  /(--|\/\*|\*\/|;|'|"|`)/,
  /(\bOR\b|\bAND\b).+?(\=|\<|\>)/i,
  /(\bunion\b|\bselect\b).+?(\bfrom\b)/i
];

// XSS patterns to block
const XSS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /<img[^>]*src[^>]*>/gi
];

// Path traversal patterns
const PATH_TRAVERSAL_PATTERNS = [
  /\.\.\//g,
  /\.\.%2f/gi,
  /\.\.%5c/gi,
  /%2e%2e%2f/gi,
  /%2e%2e%5c/gi
];

/**
 * Sanitize string input to prevent various injection attacks
 */
export function sanitizeString(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Remove null bytes
  let sanitized = input.replace(/\0/g, '');
  
  // Trim whitespace
  sanitized = sanitized.trim();
  
  // Limit length to prevent buffer overflow attacks
  if (sanitized.length > 1000) {
    sanitized = sanitized.substring(0, 1000);
  }
  
  return sanitized;
}

/**
 * Validate fagkode format (only letters and numbers allowed)
 */
export function validateFagkode(fagkode: string): { isValid: boolean; error?: string } {
  const sanitized = sanitizeString(fagkode);
  
  if (!sanitized) {
    return { isValid: false, error: "Fagkode er påkrevd" };
  }
  
  // Only allow letters and numbers, no length restriction
  const validPattern = /^[A-Za-z0-9]+$/;
  if (!validPattern.test(sanitized)) {
    return { isValid: false, error: "Fagkode kan kun inneholde bokstaver og tall" };
  }
  
  return { isValid: true };
}

/**
 * Validate variant format
 */
export function validateVariant(variant: string): { isValid: boolean; error?: string } {
  if (!variant) {
    return { isValid: true }; // Variant is optional
  }
  
  const sanitized = sanitizeString(variant);
  
  if (sanitized.length > 50) {
    return { isValid: false, error: "Variant kan ikke være lengre enn 50 tegn" };
  }
  
  // Only allow alphanumeric, spaces, underscore, and hyphen
  const validPattern = /^[A-Za-z0-9 _-]+$/;
  if (!validPattern.test(sanitized)) {
    return { isValid: false, error: "Variant kan kun inneholde bokstaver, tall, mellomrom, underscore og bindestrek" };
  }
  
  return { isValid: true };
}

/**
 * Check for SQL injection patterns
 */
export function containsSQLInjection(input: string): boolean {
  const sanitized = sanitizeString(input);
  return SQL_INJECTION_PATTERNS.some(pattern => pattern.test(sanitized));
}

/**
 * Check for XSS patterns
 */
export function containsXSS(input: string): boolean {
  const sanitized = sanitizeString(input);
  return XSS_PATTERNS.some(pattern => pattern.test(sanitized));
}

/**
 * Check for path traversal patterns
 */
export function containsPathTraversal(input: string): boolean {
  const sanitized = sanitizeString(input);
  return PATH_TRAVERSAL_PATTERNS.some(pattern => pattern.test(sanitized));
}

/**
 * Validate exam period format (V-YYYY or H-YYYY)
 */
export function validateEksamensperiode(periode: string): { isValid: boolean; error?: string } {
  const sanitized = sanitizeString(periode);
  
  if (!sanitized) {
    return { isValid: false, error: "Eksamensperiode er påkrevd" };
  }
  
  const validPattern = /^(V|H)-20\d{2}$/;
  if (!validPattern.test(sanitized)) {
    return { isValid: false, error: "Eksamensperiode må være i format V-ÅÅÅÅ eller H-ÅÅÅÅ" };
  }
  
  return { isValid: true };
}

/**
 * Validate that a value is one of the allowed options
 */
export function validateAllowedValue(value: string, allowedValues: string[], fieldName: string): { isValid: boolean; error?: string } {
  const sanitized = sanitizeString(value);
  
  if (!sanitized) {
    return { isValid: false, error: `${fieldName} er påkrevd` };
  }
  
  if (!allowedValues.includes(sanitized)) {
    return { isValid: false, error: `Ugyldig verdi for ${fieldName}` };
  }
  
  return { isValid: true };
}

/**
 * Comprehensive security validation for form data
 */
export function validateEksamensmateriellForm(formData: {
  fagkode: string;
  variant: string;
  eksamensperiode: string;
  eksamensmaterielltype: string;
  eksamensdel: string;
  malForm: string;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check for injection attacks in all fields
  Object.entries(formData).forEach(([key, value]) => {
    if (containsSQLInjection(value)) {
      errors.push(`Potensielt farlig innhold oppdaget i ${key}`);
    }
    if (containsXSS(value)) {
      errors.push(`Potensielt skadelig skript oppdaget i ${key}`);
    }
    if (containsPathTraversal(value)) {
      errors.push(`Ugyldig sti oppdaget i ${key}`);
    }
  });
  
  // Field-specific validations
  const fagkodeValidation = validateFagkode(formData.fagkode);
  if (!fagkodeValidation.isValid) {
    errors.push(fagkodeValidation.error!);
  }
  
  const variantValidation = validateVariant(formData.variant);
  if (!variantValidation.isValid) {
    errors.push(variantValidation.error!);
  }
  
  const periodeValidation = validateEksamensperiode(formData.eksamensperiode);
  if (!periodeValidation.isValid) {
    errors.push(periodeValidation.error!);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate filename for security
 */
export function validateFilename(filename: string): { isValid: boolean; error?: string } {
  const sanitized = sanitizeString(filename);
  
  if (!sanitized) {
    return { isValid: false, error: "Filnavn er påkrevd" };
  }
  
  if (sanitized.length > 255) {
    return { isValid: false, error: "Filnavn kan ikke være lengre enn 255 tegn" };
  }
  
  // Check for dangerous patterns
  if (containsPathTraversal(sanitized)) {
    return { isValid: false, error: "Filnavn inneholder ugyldige tegn" };
  }
  
  // Block executable file extensions
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar', '.php', '.asp', '.jsp'];
  const fileExtension = sanitized.toLowerCase().substring(sanitized.lastIndexOf('.'));
  
  if (dangerousExtensions.includes(fileExtension)) {
    return { isValid: false, error: "Filtype er ikke tillatt" };
  }
  
  return { isValid: true };
}