import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/authOptions';
import { getDbConnection } from '@/db/connection';
import { ISession } from '@/interface/ISession';

interface DatabaseEksamensmateriell {
  eksamensmateriellID: number;
  blobReferanseEksamensmateriell?: string;
  eksamensmateriellkategori?: string;
  eksamensmateriellMalform?: string;
  fileSize?: number;
  mimeType?: string;
  filnavn?: string;
  opphavsrett?: string;
  vedlegg?: boolean;
  createdDate?: Date | string;
  modifiedDate?: Date | string;
}

interface DatabaseEksamensdel {
  eksamensdelID: number;
  eksamensdelType?: string;
  gjennomforingStart?: Date;
  gjennomforingStopp?: Date;
  gjennomforingsystem?: string;
  eksamensveiledning?: string;
  erPlagiatkontroll: boolean;
  eksamensmateriell: DatabaseEksamensmateriell[];
}

interface DatabaseFagkodeeksamen {
  fagkodeeksamensID: number;
  fagkode: string;
  variantkode?: string;
  fagnavn: string;
  eksamensdato?: Date;
  eksamenstid?: string;
  varighet?: number;
  erTestFagkode: boolean;
  oppgaveansvar?: string;
  eksamensdeler: Map<number, DatabaseEksamensdel>;
}

export async function GET(request: NextRequest) {
  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Ikke autorisert' }, { status: 401 });
    }

    // Get eksamensperiode from query parameters
    const { searchParams } = new URL(request.url);
    const eksamensperiode = searchParams.get('eksamensperiode');

    if (!eksamensperiode) {
      return NextResponse.json({ error: 'Eksamensperiode er påkrevd' }, { status: 400 });
    }

    // Get database connection
    const AppDataSource = await getDbConnection();

    // Execute stored procedure
    const rawResults = await AppDataSource.query(
      'EXEC GetFagkodeeksamensWithDeler @Eksamensperiode = @0',
      [eksamensperiode]
    );

    // Group results by fagkodeeksamen and transform data
    const fagkodeMap = new Map<number, DatabaseFagkodeeksamen>();

    for (const row of rawResults) {
      const fagkodeId = row.FagkodeeksamensID;

      if (!fagkodeMap.has(fagkodeId)) {
        fagkodeMap.set(fagkodeId, {
          fagkodeeksamensID: row.FagkodeeksamensID,
          fagkode: row.Fagkode,
          variantkode: row.Variantkode,
          fagnavn: row.Fagnavn,
          eksamensdato: row.Eksamensdato,
          eksamenstid: row.Eksamenstid,
          varighet: row.Varighet,
          erTestFagkode: row.ErTestFagkode,
          oppgaveansvar: row.Oppgaveansvar,
          eksamensdeler: new Map() // Use Map to track eksamensdeler by ID
        });
      }
      
      // Add or get eksamensdel if it exists
      if (row.EksamensdelID) {
        const fagkodeData = fagkodeMap.get(fagkodeId)!;
        const eksamensdeler = fagkodeData.eksamensdeler;
        
        if (!eksamensdeler.has(row.EksamensdelID)) {
          eksamensdeler.set(row.EksamensdelID, {
            eksamensdelID: row.EksamensdelID,
            eksamensdelType: row.EksamensdelType,
            gjennomforingStart: row.GjennomforingStart,
            gjennomforingStopp: row.GjennomforingStopp,
            gjennomforingsystem: row.Gjennomforingsystem,
            eksamensveiledning: row.Eksamensveiledning,
            erPlagiatkontroll: row.ErPlagiatkontroll,
            eksamensmateriell: []
          });
        }
        
        // Add eksamensmateriell if it exists (LEFT JOIN can return null eksamensmateriell)
        if (row.EksamensmateriellID) {
          const eksamensdel = eksamensdeler.get(row.EksamensdelID)!;

          // Format dates as ISO strings while preserving them as UTC
          // SQL Server returns dates without timezone, so we treat them as UTC
          const createdDate = row.CreatedDate
            ? new Date(row.CreatedDate.getTime() - row.CreatedDate.getTimezoneOffset() * 60000).toISOString()
            : undefined;
          const modifiedDate = row.ModifiedDate
            ? new Date(row.ModifiedDate.getTime() - row.ModifiedDate.getTimezoneOffset() * 60000).toISOString()
            : undefined;

          eksamensdel.eksamensmateriell.push({
            eksamensmateriellID: row.EksamensmateriellID,
            blobReferanseEksamensmateriell: row.BlobReferanseEksamensmateriell,
            eksamensmateriellkategori: row.Eksamensmateriellkategori,
            eksamensmateriellMalform: row.EksamensmateriellMalform,
            fileSize: row.FileSize,
            mimeType: row.MimeType,
            filnavn: row.Filnavn,
            opphavsrett: row.Opphavsrett,
            vedlegg: row.Vedlegg,
            createdDate: createdDate,
            modifiedDate: modifiedDate
          });
        }
      }
    }

    // Transform data for frontend
    const transformedData = Array.from(fagkodeMap.values()).map(fagkode => {
      // Convert Map to array for processing
      const eksamensdelerArray: DatabaseEksamensdel[] = Array.from(fagkode.eksamensdeler.values());
      
      // Find del 2 based on EksamensdelType
      let todeltStartDel2 = null;
      let harPlagiatkontroll = false;
      let korrektEksamensdato = fagkode.eksamensdato ? `${fagkode.eksamensdato.getFullYear()}-${(fagkode.eksamensdato.getMonth() + 1).toString().padStart(2, '0')}-${fagkode.eksamensdato.getDate().toString().padStart(2, '0')}` : null; // Default from fagkodeeksamen table
      
      if (eksamensdelerArray?.length > 0) {
        // Filter out "Forberedelse" for all checks
        const valideDeler = eksamensdelerArray.filter((del) => 
          del.eksamensdelType && !del.eksamensdelType.toLowerCase().includes('forberedelse')
        );
        
        // If we have valid eksamensdeler, use the date from the first valid one
        if (valideDeler.length > 0 && valideDeler[0].gjennomforingStart) {
          const validDato = valideDeler[0].gjennomforingStart;
          korrektEksamensdato = `${validDato.getFullYear()}-${(validDato.getMonth() + 1).toString().padStart(2, '0')}-${validDato.getDate().toString().padStart(2, '0')}`;
        }
        
        // Look for any valid eksamensdel that contains "del2"
        const del2 = valideDeler.find((del) => 
          del.eksamensdelType && del.eksamensdelType.toLowerCase().includes('del2')
        );
        
        if (del2?.gjennomforingStart) {
          todeltStartDel2 = new Date(del2.gjennomforingStart).toISOString();
        }
        
        // Check if any valid eksamensdel has plagiat control enabled
        harPlagiatkontroll = valideDeler.some((del) => 
          del.erPlagiatkontroll === true
        );
      }

      return {
        fagkodeeksamensID: fagkode.fagkodeeksamensID,
        fagkode: fagkode.fagkode,
        variantkode: fagkode.variantkode,
        fagnavn: fagkode.fagnavn,
        eksamensdato: korrektEksamensdato,
        todeltStartDel2: todeltStartDel2,
        eksamenstid: fagkode.eksamenstid,
        varighet: fagkode.varighet,
        erTestFagkode: fagkode.erTestFagkode,
        oppgaveansvar: fagkode.oppgaveansvar,
        harPlagiatkontroll: harPlagiatkontroll,
        eksamensdeler: eksamensdelerArray
      };
    });

    return NextResponse.json({
      success: true,
      data: transformedData,
      count: transformedData.length
    });

  } catch (error) {
    console.error('Error fetching fagkodeeksamen:', error);
    return NextResponse.json({
      error: 'En feil oppstod ved henting av fagkodeeksamen data',
      details: error instanceof Error ? error.message : 'Ukjent feil'
    }, { status: 500 });
  }
}