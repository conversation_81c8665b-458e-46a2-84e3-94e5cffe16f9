"use client";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { IFagkodeShared } from "@/interface/IFagkodeShared";
import {
  FaExternalLinkAlt,
  FaCheck,
  FaExclamationTriangle,
} from "react-icons/fa";
import {
  HiOutlineArrowNarrowUp,
  HiOutlineArrowNarrowDown,
} from "react-icons/hi";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

const formatFileSize = (sizeInBytes: number | undefined): string => {
  if (!sizeInBytes) return "";

  const units = ["B", "KB", "MB", "GB"];
  let size = sizeInBytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
};

const formatDate = (date: Date | undefined): string => {
  if (!date) return "";
  return dayjs(date).format("DD.MM.YYYY");
};

const formatDateWithTime = (date: Date | string | undefined): string => {
  if (!date) return "";
  // Ensure date string is treated as UTC by appending 'Z' if not present
  const dateStr = typeof date === 'string' && !date.endsWith('Z') ? date + 'Z' : date;
  // Parse as UTC and convert to Oslo timezone
  return dayjs.utc(dateStr).tz("Europe/Oslo").format("DD.MM.YYYY [kl.] HH:mm");
};

const formatFileDates = (
  createdDate: Date | string | undefined,
  modifiedDate: Date | string | undefined
): JSX.Element | string => {
  if (!createdDate) return "";

  const created = formatDateWithTime(createdDate);

  // Only show modified date if it exists and is different from created date
  if (modifiedDate && dayjs.utc(modifiedDate).isAfter(dayjs.utc(createdDate))) {
    const modified = formatDateWithTime(modifiedDate);
    return (
      <div className="text-xs text-gray-500">
        <div>Verifisert: {created}</div>
        <div>Endret: {modified}</div>
      </div>
    );
  }

  return (
    <div className="text-xs text-gray-500">
      <div>Verifisert: {created}</div>
    </div>
  );
};

const formatEksamensdelName = (eksamensdel: string): string => {
  // Format EksamenDel1 -> "Eksamen del 1", EksamenDel2 -> "Eksamen del 2"
  if (eksamensdel.match(/^EksamenDel\d+$/i)) {
    return eksamensdel.replace(/^EksamenDel(\d+)$/i, "Eksamen del $1");
  }
  // Return other names as-is
  return eksamensdel;
};

interface ColumnsOptions {
  showFiles?: boolean;
  showPlagiarism?: boolean;
  showAdditionalColumns?: boolean;
  blobCheckResults?: Map<string, boolean>;
}

export const createFagkodeColumns = (
  options: ColumnsOptions = {}
): ColumnDef<IFagkodeShared>[] => {
  const {
    showFiles = true,
    showPlagiarism = true,
    showAdditionalColumns = true,
    blobCheckResults,
  } = options;

  const baseColumns: ColumnDef<IFagkodeShared>[] = [
    {
      accessorKey: "fagkode",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Fagkode
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
      enableSorting: true,
      cell: ({ row }) => (
        <div tabIndex={0} className="font-medium">
          {row.original.fagkode}
        </div>
      ),
    },
    {
      accessorKey: "variantkode",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Variant
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
      enableSorting: true,
      cell: ({ row }) => (
        <div tabIndex={0} className="">
          {row.original.variantkode || "-"}
        </div>
      ),
    },
    {
      accessorKey: "fagnavn",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Fagnavn
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
      enableSorting: true,
      cell: ({ row }) => (
        <div tabIndex={0} className="">
          {row.original.fagnavn}
        </div>
      ),
    },
    {
      accessorKey: "eksamensdato",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Eksamensdato
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
      enableSorting: true,
      cell: ({ row }) => {
        const formatDate = (dateString: string) => {
          // Extract date part directly from string "2025-07-30T00:00:00.0000000"
          const datePart = dateString.split("T")[0]; // "2025-07-30"
          const [year, month, day] = datePart.split("-");
          return `${day}.${month}.${year}`;
        };

        return (
          <div className="">
            {row.original.eksamensdato
              ? formatDate(row.original.eksamensdato)
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "todeltStartDel2",
      header: () => {
        return <div className="text-left font-medium">Todelt: Start del 2</div>;
      },
      enableSorting: false,
      cell: ({ row }) => (
        <div className="">
          {row.original.todeltStartDel2
            ? dayjs(row.original.todeltStartDel2).format("HH:mm")
            : "-"}
        </div>
      ),
    },
  ];

  // Add Eksamenstid column for simplified view or always
  if (!showFiles || !showAdditionalColumns) {
    baseColumns.push({
      accessorKey: "eksamenstid",
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Eksamenstid
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        );
      },
      enableSorting: true,
      cell: ({ row }) => (
        <div className="">{row.original.eksamenstid || "-"}</div>
      ),
    });
  }

  // Add plagiarism column if enabled
  if (showPlagiarism) {
    baseColumns.push({
      accessorKey: "harPlagiatkontroll",
      header: () => {
        return <div className="text-left font-medium">Plagiatkontroll</div>;
      },
      enableSorting: false,
      cell: ({ row }) => (
        <div className="flex justify-center">
          {row.original.harPlagiatkontroll && (
            <FaCheck className="h-4 w-4 text-green-600" />
          )}
        </div>
      ),
    });
  }

  // Add files column if enabled
  if (showFiles) {
    baseColumns.push({
      accessorKey: "filer",
      header: () => {
        return <div className="text-left font-medium">Filer</div>;
      },
      enableSorting: false,
      cell: ({ row }) => {
        const eksamensdeler = row.original.eksamensdeler || [];

        // Group files by eksamensdel
        const filesByEksamensdel: {
          [key: string]: Array<{
            filnavn: string;
            blobReferanseEksamensmateriell: string;
            eksamensmateriellkategori?: string;
            eksamensmateriellMalform?: string;
            fileSize?: number;
            createdDate?: Date;
            modifiedDate?: Date;
          }>;
        } = {};

        let totalFiles = 0;
        let missingFiles = 0;

        eksamensdeler.forEach((del: any) => {
          if (del.eksamensmateriell && del.eksamensmateriell.length > 0) {
            const eksamensdel = del.eksamensdelType || "Ukjent";
            const files = del.eksamensmateriell
              .filter(
                (mat: any) => mat.filnavn && mat.blobReferanseEksamensmateriell
              )
              .map((mat: any) => ({
                filnavn: mat.filnavn,
                blobReferanseEksamensmateriell:
                  mat.blobReferanseEksamensmateriell,
                eksamensmateriellkategori: mat.eksamensmateriellkategori,
                eksamensmateriellMalform: mat.eksamensmateriellMalform,
                fileSize: mat.fileSize,
                createdDate: mat.createdDate,
                modifiedDate: mat.modifiedDate,
              }));

            if (files.length > 0) {
              filesByEksamensdel[eksamensdel] = files;
              totalFiles += files.length;

              // Count missing files if blob check results are available
              if (blobCheckResults) {
                missingFiles += files.filter(
                  (file: { blobReferanseEksamensmateriell: string }) =>
                    blobCheckResults.get(
                      file.blobReferanseEksamensmateriell
                    ) === false
                ).length;
              }
            }
          }
        });

        if (totalFiles === 0) {
          return (
            <div className="flex items-center gap-1 text-sm text-yellow-700 whitespace-nowrap">
              <FaExclamationTriangle className="h-3 w-3" />
              <span>ingen filer</span>
            </div>
          );
        }

        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="link" className="h-auto p-0 text-sm">
                <div className="flex items-center gap-1">
                  <span>
                    {totalFiles === 1 ? "1 fil" : `${totalFiles} filer`}
                  </span>
                  {missingFiles > 0 && (
                    <FaExclamationTriangle
                      className="h-3 w-3 text-red-600"
                      title={`${missingFiles} filer mangler i blob`}
                    />
                  )}
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-3">
                <div className="space-y-4 max-h-60 overflow-y-auto">
                  {Object.entries(filesByEksamensdel)
                    .sort(([a], [b]) => {
                      // Define the order: Forberedelse, Eksamen, Eksamen del 1, Eksamen del 2
                      const order = ['Forberedelse', 'Eksamen', 'Eksamen del 1', 'Eksamen del 2'];
                      const indexA = order.indexOf(a);
                      const indexB = order.indexOf(b);

                      // If both are in the order array, sort by index
                      if (indexA !== -1 && indexB !== -1) {
                        return indexA - indexB;
                      }

                      // If only one is in the order array, prioritize it
                      if (indexA !== -1) return -1;
                      if (indexB !== -1) return 1;

                      // If neither is in the order array, sort alphabetically
                      return a.localeCompare(b);
                    })
                    .map(([eksamensdel, files]) => (
                      <div key={eksamensdel} className="space-y-2">
                        <div className="text-sm text-gray-700 uppercase border-b border-gray-200 pb-1">
                          {formatEksamensdelName(eksamensdel)}
                        </div>
                        <div className="space-y-1">
                          {files.map((file, index) => {
                            const exists = blobCheckResults
                              ? blobCheckResults.get(
                                  file.blobReferanseEksamensmateriell
                                ) !== false
                              : true;

                            return (
                              <a
                                key={index}
                                href={
                                  exists
                                    ? `/api/downloadEksamensmateriell?blobName=${encodeURIComponent(
                                        file.blobReferanseEksamensmateriell
                                      )}&filename=${encodeURIComponent(
                                        file.filnavn
                                      )}`
                                    : "#"
                                }
                                target={exists ? "_blank" : undefined}
                                rel={exists ? "noopener noreferrer" : undefined}
                                className={`block p-2 rounded transition-colors ${
                                  exists
                                    ? "bg-gray-50 hover:bg-gray-100 cursor-pointer"
                                    : "bg-red-50 border border-red-200 cursor-not-allowed"
                                }`}
                                title={
                                  exists
                                    ? `Last ned: ${file.filnavn}`
                                    : `Fil ikke funnet i blob: ${file.filnavn}`
                                }
                              >
                                <div className="min-w-0">
                                  <div className="flex items-center gap-2">
                                    <div className="text-sm font-medium truncate">
                                      {file.filnavn}
                                    </div>
                                    {!exists && (
                                      <FaExclamationTriangle className="h-3 w-3 text-red-600 flex-shrink-0" />
                                    )}
                                  </div>
                                  <div className="text-xs text-gray-600 mt-1">
                                    {file.eksamensmateriellkategori && (
                                      <span>
                                        {file.eksamensmateriellkategori}
                                      </span>
                                    )}
                                    {file.eksamensmateriellMalform && (
                                      <span
                                        className={
                                          file.eksamensmateriellkategori
                                            ? "ml-2"
                                            : ""
                                        }
                                      >
                                        {file.eksamensmateriellkategori
                                          ? "• "
                                          : ""}
                                        {file.eksamensmateriellMalform}
                                      </span>
                                    )}
                                    {file.fileSize && (
                                      <span
                                        className={
                                          file.eksamensmateriellkategori ||
                                          file.eksamensmateriellMalform
                                            ? "ml-2"
                                            : ""
                                        }
                                      >
                                        {file.eksamensmateriellkategori ||
                                        file.eksamensmateriellMalform
                                          ? "• "
                                          : ""}
                                        {formatFileSize(file.fileSize)}
                                      </span>
                                    )}
                                  </div>
                                  {(file.createdDate || file.modifiedDate) && (
                                    <div className="mt-1">
                                      {formatFileDates(
                                        file.createdDate,
                                        file.modifiedDate
                                      )}
                                    </div>
                                  )}
                                </div>
                              </a>
                            );
                          })}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        );
      },
    });
  }

  // Add additional columns if enabled
  if (showAdditionalColumns) {
    baseColumns.push(
      {
        accessorKey: "lareplan",
        header: () => {
          return <div className="text-left font-medium" role="columnheader">Læreplan</div>;
        },
        enableSorting: false,
        cell: ({ row }) => {
          const eksamensdeler = row.original.eksamensdeler || [];

          // Check if any eksamensdel contains "Eksamen" (exact match for LK06)
          const hasEksamen = eksamensdeler.some(
            (del: any) => del.eksamensdelType?.toLowerCase() === "eksamen"
          );

          // Check if any eksamensdel contains "del1" or "del2" (for LK20)
          const hasDel1Del2 = eksamensdeler.some((del: any) => {
            const type = del.eksamensdelType?.toLowerCase() || "";
            return type.includes("del1") || type.includes("del2");
          });

          if (hasEksamen) {
            return <div className="text-sm">LK06</div>;
          } else if (hasDel1Del2) {
            return <div className="text-sm">LK20</div>;
          } else {
            return <div className="text-sm">-</div>;
          }
        },
      },
      {
        accessorKey: "sisteEksamen",
        header: () => {
          return <div className="text-left font-medium" role="columnheader">Siste eksamen</div>;
        },
        enableSorting: false,
        cell: ({ row }) => {
          const eksamensdeler = row.original.eksamensdeler || [];

          // Check if any eksamensdel contains "Eksamen" (LK06 fagkoder)
          const hasEksamen = eksamensdeler.some(
            (del: any) => del.eksamensdelType?.toLowerCase() === "eksamen"
          );

          if (hasEksamen) {
            return <div className="text-sm">Høst 2025</div>;
          } else {
            return <div className="text-sm">-</div>;
          }
        },
      }
    );
  }

  // Always add external link column at the end
  baseColumns.push({
    accessorKey: "lenkeEksamensplan",
    header: () => {
      return <div className="text-left font-medium" role="columnheader">Lenke eksamensplan</div>;
    },
    enableSorting: false,
    cell: ({ row }) => (
      <div className="">
        <a
          href={`https://eksamensplan.udir.no/eksamen/${row.original.fagkode}`}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center text-gray-700 hover:text-gray-900"
          title={`Åpne eksamensplan for ${row.original.fagkode}`}
        >
          <FaExternalLinkAlt className="h-3.5 w-3.5" />
        </a>
      </div>
    ),
  });

  return baseColumns;
};
