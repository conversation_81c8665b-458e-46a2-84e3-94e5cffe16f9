"use server";

import { getAccessToken } from "./getAccessToken";

export async function generateAccessToken(): Promise<string> {
  const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
  const clientSecret: string =
    process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
  const scope: string = process.env.UDIR_PGS_ADMIN_RESOURCE_SCOPES || "";
  const accesstokenKey: string = "PGSE:PGSA:EksamenAccessToken";

  const accessToken = await getAccessToken(
    clientId,
    clientSecret,
    scope,
    accesstokenKey
  );

  return accessToken;
}
