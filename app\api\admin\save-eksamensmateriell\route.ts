import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../../auth/authOptions";
import {
  EksamensmateriellService,
  IEksamensmateriellData,
} from "@/db/services/eksamensmateriellService";
import {
  validateEksamensmateriellForm,
  validateFilename,
  sanitizeString,
} from "@/lib/shared/validation";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import { generateAccessToken } from "@/lib/server/getAccessTokenForEksamen";

const telemetryClient = getAppInsightsServer();
const pgsaBaseUrl = process.env.PGSA_VERIFICATION_API_URL;
const pgsaCode = process.env.PGSA_VERIFICATION_API_URL_CODE;

// Helper function for PGSA API call
async function sendPgsaNotification(data: IEksamensmateriellData) {
  if (!pgsaBaseUrl || !pgsaCode) {
    console.warn(
      "PGSA_VERIFICATION_API_URL or PGSA_VERIFICATION_API_URL_CODE not configured, skipping PGSA notification call"
    );
    return;
  }

  const pgsaApiUrl = `${pgsaBaseUrl}/api/examdata?code=${pgsaCode}`;
  console.log("PGSA API URL:", pgsaApiUrl);
  try {
    // Get exam metadata from database to determine TestType and SubjectName
    const examMetadata = await EksamensmateriellService.getExamMetadata(
      data.fagkode,
      data.variant,
      data.eksamensperiode
    );
    const testType = EksamensmateriellService.mapOpplaeringsniveToTestType(
      examMetadata.opplaeringsniva
    );
    const subjectName =
      examMetadata.fagnavn || `${data.fagkode} ${data.eksamensdel}`;

    const payload = {
      DocumentCode: data.blobReferanse,
      OrgFileName: data.originalFilename,
      TestType: testType,
      SubjectCode: data.fagkode,
      LanguageVariant: data.eksamensmateriellMalform,
      TestPeriod: data.eksamensperiode,
      Mime: data.mimeType,
      FileExtension: "." + data.originalFilename.split(".").pop(),
      SubjectName: subjectName,
      CopyrightProtected: data.opphavsrett ? 1 : 0,
      FileSize: data.fileSize.toString(),
      VariantCode: data.variant || "",
    };

    const response = await fetch(pgsaApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`PGSA API kall feilet med status: ${response.status}`);
    }
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "pgsaNotificationCall",
        documentCode: data.blobReferanse,
        error: error instanceof Error ? error.message : "Unknown error",
      },
    });

    console.error(
      "PGSA notification call failed for",
      data.blobReferanse,
      ":",
      error
    );
    // Throw error to notify client of PGSA notification failure
    throw new Error(
      `PGSA-notifikasjon feilet: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`
    );
  }
}

// Helper function for external verification API call
async function sendExternalVerification(data: IEksamensmateriellData) {
  const externalApiUrl = process.env.PAS_VERIFICATION_API_URL;

  if (!externalApiUrl) {
    console.warn(
      "PAS_VERIFICATION_API_URL not configured, skipping external verification call"
    );
    return;
  }

  try {
    // Generate access token for PAS verification API
    const token = await generateAccessToken();

    // Prepare fagkode with variant if present (uppercase)
    const fagkodeWithVariant = data.variant
      ? `${data.fagkode.toUpperCase()}-${data.variant.toUpperCase()}`
      : data.fagkode.toUpperCase();

    const payload = {
      documentGuid: data.blobReferanse,
      eksamensdel: data.eksamensdel,
      eksamensmaterialtype: data.eksamensmateriellkategori,
      eksamensperiode: data.eksamensperiode,
      fagkoder: [fagkodeWithVariant],
      malform: data.eksamensmateriellMalform,
      orginaltFilnavn: data.originalFilename,
      opphavsrett: data.opphavsrett || false,
      vedlegg: data.vedlegg || false,
    };

    const response = await fetch(externalApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(
        `Ekstern verifiseringskall feilet med status: ${response.status}`
      );
    }

    telemetryClient?.trackEvent({
      name: "ExternalVerificationCallSuccessful",
      properties: {
        documentGuid: data.blobReferanse,
        eksamensdel: data.eksamensdel,
        eksamensmaterialtype: data.eksamensmateriellkategori,
      },
    });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "externalVerificationCall",
        documentGuid: data.blobReferanse,
        error: error instanceof Error ? error.message : "Unknown error",
      },
    });

    console.error(
      "External verification call failed for",
      data.blobReferanse,
      ":",
      error
    );
    // Throw error to notify client of PAS verification failure
    throw new Error(
      `PAS-verifisering feilet: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`
    );
  }
}

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const userRoles = Array.isArray(session.user.role)
      ? session.user.role
      : [session.user.role];

    const hasAdminRole = userRoles.some(
      (role) => role === "urn:udir:pgsa:administrator"
    );

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const {
      eksamensmateriellData,
    }: { eksamensmateriellData: IEksamensmateriellData[] } = body;

    if (!eksamensmateriellData || !Array.isArray(eksamensmateriellData)) {
      return NextResponse.json(
        {
          error: "Invalid request body. Expected eksamensmateriellData array.",
        },
        { status: 400 }
      );
    }

    // Validate each record for security
    for (const data of eksamensmateriellData) {
      // Validate GUID format for blob reference (eksamensmateriellfilID is now auto-generated)
      const guidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!guidPattern.test(data.blobReferanse)) {
        return NextResponse.json(
          { error: "Invalid GUID format for blob reference" },
          { status: 400 }
        );
      }

      // Validate filename security
      const filenameValidation = validateFilename(data.originalFilename);
      if (!filenameValidation.isValid) {
        return NextResponse.json(
          { error: `Invalid filename: ${filenameValidation.error}` },
          { status: 400 }
        );
      }

      // Validate file size bounds
      if (data.fileSize < 5 || data.fileSize > 200 * 1024 * 1024) {
        return NextResponse.json(
          { error: "Invalid file size" },
          { status: 400 }
        );
      }

      // Sanitize string fields
      data.eksamensmateriellkategori = sanitizeString(
        data.eksamensmateriellkategori
      );
      data.eksamensmateriellMalform = sanitizeString(
        data.eksamensmateriellMalform
      );
      data.originalFilename = sanitizeString(data.originalFilename);
      data.mimeType = sanitizeString(data.mimeType);

      // Sanitize new fields for Eksamensdel relationship
      if (data.fagkode) data.fagkode = sanitizeString(data.fagkode);
      if (data.variant) data.variant = sanitizeString(data.variant);
      if (data.eksamensperiode)
        data.eksamensperiode = sanitizeString(data.eksamensperiode);
      if (data.eksamensdel) data.eksamensdel = sanitizeString(data.eksamensdel);

      // Validate required fields for Eksamensdel relationship
      if (!data.fagkode || !data.eksamensperiode || !data.eksamensdel) {
        return NextResponse.json(
          {
            error:
              "Manglende påkrevde felter: fagkode, eksamensperiode, eksamensdel",
          },
          { status: 400 }
        );
      }

      // Validate mime type format
      if (
        data.mimeType &&
        !/^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*$/.test(
          data.mimeType
        )
      ) {
        return NextResponse.json(
          { error: "Invalid MIME type format" },
          { status: 400 }
        );
      }
    }

    // Save each exam material record
    const results = [];
    for (const data of eksamensmateriellData) {
      try {
        await EksamensmateriellService.saveEksamensmateriell(data);
        results.push({
          blobReferanse: data.blobReferanse,
          success: true,
        });

        // Send external verification call after successful save
        await sendExternalVerification(data);

        // Send PGSA notification call after successful verification
        await sendPgsaNotification(data);
      } catch (error) {
        console.error(
          `Failed to process eksamensmateriell with blob ${data.blobReferanse}:`,
          error
        );

        // Use the actual error message from the service or PAS verification
        const errorMessage =
          error instanceof Error ? error.message : "Behandling av fil feilet";

        results.push({
          blobReferanse: data.blobReferanse,
          success: false,
          error: errorMessage,
        });
      }
    }

    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.filter((r) => !r.success).length;

    // If all files failed, return error response with detailed message
    if (failureCount > 0 && successCount === 0) {
      const firstError =
        results.find((r) => !r.success)?.error || "Unknown error";
      return NextResponse.json(
        {
          error: firstError,
          details: `All ${failureCount} files failed to save`,
        },
        { status: 400 }
      );
    }

    // If some files failed, include error details in success response
    if (failureCount > 0) {
      const failedResults = results.filter((r) => !r.success);
      const errorMessages = failedResults.map((r) => r.error).join(", ");

      return NextResponse.json({
        success: false,
        message: `${successCount} files saved successfully, but ${failureCount} failed`,
        error: errorMessages,
        results,
        savedCount: successCount,
        failedCount: failureCount,
      });
    }

    // All files succeeded
    return NextResponse.json({
      success: true,
      message: `Lagret ${successCount} eksamensmateriell(er)`,
      results,
      savedCount: successCount,
      failedCount: failureCount,
    });
  } catch (error) {
    console.error("Error saving eksamensmateriell:", error);

    return NextResponse.json({ error: "Intern serverfeil" }, { status: 500 });
  }
}
