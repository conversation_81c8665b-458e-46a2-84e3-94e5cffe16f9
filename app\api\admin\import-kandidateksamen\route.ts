import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/authOptions';
import { KandidateksamenService } from '@/db/services/kandidateksamenService';
import { ISession } from '@/interface/ISession';
import { getAppInsightsServer } from '@/lib/server/appInsightsServer';

// Initialize Application Insights for logging and monitoring
const telemetryClient = getAppInsightsServer();

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let eksamensperiode: string | undefined;

  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidateksamen_Unauthorized',
        properties: {
          timestamp: new Date().toISOString(),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      });
      return NextResponse.json({ error: 'Ikke autorisert' }, { status: 401 });
    }

    // Authorization check - verify administrator role
    const userRoles = Array.isArray(session.user.role) ? session.user.role : [session.user.role];
    const hasAdminRole = userRoles.some(role => role.includes('urn:udir:pgsa:administrator'));

    if (!hasAdminRole) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidateksamen_Forbidden',
        properties: {
          userId: session.user.userInfo.name,
          userRoles: userRoles.join(','),
          timestamp: new Date().toISOString()
        }
      });
      return NextResponse.json({ error: 'Ikke tilgang' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    eksamensperiode = body.eksamensperiode;

    if (!eksamensperiode || typeof eksamensperiode !== 'string') {
      return NextResponse.json({ error: 'Eksamensperiode er påkrevd og må være en tekststreng' }, { status: 400 });
    }

    // Trim whitespace and validate examination period format (e.g., H-2025, V-2026)
    eksamensperiode = eksamensperiode.trim();
    const eksamensperiodeRegex = /^[HV]-\d{4}$/;

    if (!eksamensperiodeRegex.test(eksamensperiode)) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidateksamen_InvalidFormat',
        properties: {
          userId: session.user.userInfo.name,
          eksamensperiode,
          timestamp: new Date().toISOString()
        }
      });
      return NextResponse.json({
        error: 'Ugyldig eksamensperiode format. Kun H-ÅÅÅÅ eller V-ÅÅÅÅ er tillatt (f.eks. H-2025, V-2026)'
      }, { status: 400 });
    }

    // Additional validation: Check if year is reasonable (3 years back to 1 year forward)
    const [season, yearStr] = eksamensperiode.split('-');
    const year = parseInt(yearStr, 10);
    const currentYear = new Date().getFullYear();
    const minYear = currentYear - 3;
    const maxYear = currentYear + 1;

    if (year < minYear || year > maxYear) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidateksamen_InvalidYear',
        properties: {
          userId: session.user.userInfo.name,
          eksamensperiode,
          year: year.toString(),
          timestamp: new Date().toISOString()
        }
      });
      return NextResponse.json({
        error: `Ugyldig årstall. Årstall må være mellom ${minYear} og ${maxYear}`
      }, { status: 400 });
    }

    // Log start of import operation
    telemetryClient?.trackEvent({
      name: 'ImportKandidateksamen_Started',
      properties: {
        userId: session.user.userInfo.name,
        eksamensperiode,
        timestamp: new Date().toISOString()
      }
    });

    // Execute import with proper error handling
    const kandidateksamenService = new KandidateksamenService();
    const result = await kandidateksamenService.importKandidateksamen(eksamensperiode);

    const duration = Date.now() - startTime;

    // Log successful completion
    telemetryClient?.trackEvent({
      name: 'ImportKandidateksamen_Completed',
      properties: {
        userId: session.user.userInfo.name,
        eksamensperiode,
        importedCount: result.success.toString(),
        errorCount: result.errors.toString(),
        deletedCount: result.deleted.toString(),
        duration: duration.toString(),
        timestamp: new Date().toISOString()
      }
    });

    telemetryClient?.trackMetric({
      name: 'ImportKandidateksamen_Duration',
      value: duration
    });

    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        eksamensperiode,
        importedCount: result.success,
        errorCount: result.errors,
        deletedCount: result.deleted,
        user: session.user.userInfo.name,
        errorDetails: result.errorDetails || []
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Ukjent feil';

    // Log error with comprehensive details
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        operation: 'ImportKandidateksamen',
        eksamensperiode: eksamensperiode || 'unknown',
        duration: duration.toString(),
        timestamp: new Date().toISOString()
      }
    });

    console.error('Feil ved import av kandidateksamen:', {
      error: errorMessage,
      eksamensperiode,
      stack: error instanceof Error ? error.stack : undefined,
      duration
    });

    return NextResponse.json({
      error: 'En feil oppstod under import av kandidateksamen',
      details: errorMessage
    }, { status: 500 });
  }
}
