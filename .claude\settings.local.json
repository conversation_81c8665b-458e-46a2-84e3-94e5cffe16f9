{
  "$schema": "https://json.schemastore.org/claude-code-settings.json",
  "permissions": {
    "allow": [
      "Bash(npm run dev:*)",
      "Bash(cmd /c:*)",
      "<PERSON>sh(mkdir:*)",
      "<PERSON>sh(mv:*)",
      "<PERSON>sh(find:*)",
      "Bash(grep:*)",
      "<PERSON>sh(npx next lint:*)",
      "<PERSON>sh(npx tsc:*)",
      "Bash(npm install:*)",
      "Bash(npm run build:*)",
      "mcp__playwright__browser_navigate",
      "mcp__playwright__browser_wait_for",
      "mcp__playwright__browser_type",
      "mcp__playwright__browser_snapshot",
      "mcp__playwright__browser_click",
      "Bash(npm run lint)",
      "mcp__playwright__browser_press_key",
      "mcp__playwright__browser_console_messages",
      "Bash(taskkill:*)",
      "mcp__playwright__browser_resize",
      "mcp__playwright__browser_evaluate",
      "Bash(timeout:*)",
      "Bash(ping:*)",
      "<PERSON>sh(npx playwright:*)",
      "Bash(rm:*)",
      "WebSearch",
      "WebFetch(domain:github.com)",
    ],
    "deny": [],
    "additionalDirectories": [
      "C:\\d C:\\Kode\\Udir"
    ]
  }
}