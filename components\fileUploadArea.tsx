"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { memo, useCallback, useMemo } from "react";
import { FileRejection, useDropzone } from "react-dropzone";
import { FaFileUpload } from "react-icons/fa";
import { ImSpinner2 } from "react-icons/im";
import { MdOutlineFileUpload } from "react-icons/md";

interface FileUploadAreaProps {
  onDrop: (acceptedFiles: File[], fileRejections: FileRejection[]) => void;
  isLoading: boolean;
  fileValidator: (file: File) => any;
  totalFilesInBatch: number;
  numUploadedFiles: number;
  numFilesRejected: number;
  totalSize: number;
}

export const FileUploadArea: React.FC<FileUploadAreaProps> = memo(
  ({
    onDrop,
    fileValidator,
    isLoading,
    totalFilesInBatch,
    numUploadedFiles,
    numFilesRejected,
    totalSize,
  }) => {
    const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
      onDrop,
      validator: fileValidator,
      noClick: true,
      noKeyboard: true,
      disabled: isLoading,
    });

    const formatFileSize = useCallback((bytes: number): string => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "kB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }, []);

    const formattedFileSize = useMemo(
      () => formatFileSize(totalSize),
      [totalSize, formatFileSize]
    );

    return (
      <div
        {...getRootProps()}
        className={`w-full lg:w-3/5 outline-none p-6 border-2 border-dashed rounded-lg transition-all ${
          isDragActive ? "border-blue-500 bg-blue-50" : "border-gray-200"
        } ${
          isLoading ? "opacity-70 cursor-not-allowed" : ""
        } shadow-sm hover:shadow-md min-h-[200px]`} // Fjern flex-egenskaper
      >
        <input
          {...getInputProps()}
          disabled={isLoading}
          aria-label="Velg filer for opplasting"
        />
        <div className="text-center">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center">
              <ImSpinner2
                className="animate-spin text-4xl mb-2 text-gray-500"
                aria-hidden="true"
              />
              <p className="text-gray-700">Laster opp filer...</p>
            </div>
          ) : (
            <>
              <FaFileUpload
                className="mx-auto text-4xl text-gray-600"
                aria-hidden="true"
              />
              <p className="mt-2 text-gray-700">
                {isDragActive
                  ? "Slipp filene her..."
                  : "Dra og slipp filer her eller bruk knappen nedenfor"}
              </p>
              <div className="mt-4 flex justify-center">
                <Button
                  type="button"
                  variant="default"
                  className="text-white font-bold py-2 px-4 rounded-lg inline-flex items-center bg-gray-600 hover:bg-slate-900 transition-colors shadow-md"
                  onClick={(e) => {
                    e.stopPropagation();
                    open();
                  }}
                  disabled={isLoading}
                >
                  <MdOutlineFileUpload
                    className="h-5 w-5 mr-2"
                    aria-hidden="true"
                  />
                  <span>Last opp filer</span>
                </Button>
              </div>
            </>
          )}
        </div>
        {totalFilesInBatch > 0 && (
          <div className="w-full flex flex-col items-center pt-4 text-sm text-gray-600">
            <span>
              {`Lastet opp ${numUploadedFiles} av ${totalFilesInBatch} filer${
                numFilesRejected > 0 ? ` (${numFilesRejected} ble avvist)` : ""
              }.`}
            </span>
            <span>Totalt: {formattedFileSize}</span>
          </div>
        )}
      </div>
    );
  }
);
