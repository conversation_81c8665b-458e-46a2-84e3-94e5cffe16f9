"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  CheckCircle,
  AlertCircle,
  Upload,
  Loader2,
  FolderOpen,
} from "lucide-react";
import { useEksamensmateriellForm } from "@/hooks/useEksamensmateriellForm";
import { EksamensmateriellFormFields } from "@/components/admin/EksamensmateriellFormFields";
import { ExistingFilesFetcher } from "@/components/admin/ExistingFilesFetcher";
import { SearchFormFields } from "@/components/admin/SearchFormFields";
import { formatFileSize } from "@/components/admin/shared/fileManagementUtils";
import { toast } from "@/components/ui/use-toast";

interface ExistingFile {
  id: string;
  blobReference: string;
  filename: string;
  kategori: string;
  malform: string;
  eksamensdel: string;
  fileSize: number;
  mimeType: string;
}

export default function OpplastingEksamensmateriellPage() {
  const [activeTab, setActiveTab] = useState("upload");
  const [existingFiles, setExistingFiles] = useState<ExistingFile[]>([]);
  const [isFetching, setIsFetching] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [announcements, setAnnouncements] = useState<string>("");

  const {
    formData,
    uploadedFiles,
    totalSize,
    isLoading,
    handleInputChange,
    handleFileDrop,
    removeFile,
    handleVerifyMaterial,
    handleReset,
    handleDeleteFile,
    isFormValid,
    fileValidator,
    setUploadedFiles,
    setTotalSize,
  } = useEksamensmateriellForm();

  const canFetch = Boolean(formData.fagkode && formData.eksamensperiode);

  const handleFetchFiles = async () => {
    if (!canFetch) {
      setFetchError("Fagkode og eksamensperiode må fylles ut først");
      return;
    }

    setIsFetching(true);
    setFetchError(null);

    try {
      const params = new URLSearchParams({
        fagkode: formData.fagkode,
        eksamensperiode: formData.eksamensperiode,
        ...(formData.variant && { variant: formData.variant }),
      });

      const response = await fetch(
        `/api/admin/get-existing-eksamensmateriell?${params}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Kunne ikke hente eksisterende filer"
        );
      }

      const data = await response.json();
      setExistingFiles(data.files);
      setHasSearched(true);
    } catch (error) {
      setFetchError(error instanceof Error ? error.message : "Ukjent feil");
      setExistingFiles([]);
    } finally {
      setIsFetching(false);
    }
  };

  const handleFileDeleted = (fileId: string) => {
    setExistingFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleFileReplaced = (
    fileId: string,
    newFileData: Partial<ExistingFile>
  ) => {
    setExistingFiles((prev) =>
      prev.map((file) =>
        file.id === fileId ? { ...file, ...newFileData } : file
      )
    );
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Validate file using existing validator
      const validationError = fileValidator(file);
      if (validationError) {
        // Show user-friendly error message with toast
        toast({
          variant: "destructive",
          title: "Ugyldig fil",
          description: `${validationError.message}: ${file.name}`,
        });

        // Clear the file input
        event.target.value = "";
        return;
      }

      // Create new uploaded file object
      const newFile = {
        file,
        id: `${Date.now()}-${Math.random()}`,
      };

      // Replace existing file (single file mode)
      setUploadedFiles([newFile]);
      setTotalSize(file.size);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Clear search results when switching to "administrer materiell" tab
    if (value === "existing") {
      setExistingFiles([]);
      setHasSearched(false);
      setFetchError(null);
    }
  };

  return (
    <>
      <div className="py-6 bg-header">
        <div className="container-wrapper">
          <div>
            <h1 className="text-4xl">Opplasting av eksamensmateriell</h1>
            <p className="mt-4">
              Last opp eksamensmateriell (PDF-filer) med tilhørende metadata.
            </p>
          </div>
        </div>
      </div>
      <div className="container-wrapper py-8">
        <div className="max-w-6xl mx-auto space-y-6">
          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload" className="text-xs sm:text-sm">
                <div className="flex items-center gap-1">
                  <Upload className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="hidden sm:inline">Verifiser nytt materiell</span>
                  <span className="sm:hidden">Verifiser</span>
                </div>
              </TabsTrigger>
              <TabsTrigger value="existing" className="text-xs sm:text-sm">
                <div className="flex items-center gap-1">
                  <FolderOpen className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="hidden sm:inline">Administrer materiell</span>
                  <span className="sm:hidden">Administrer</span>
                </div>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="mt-6">
              <div className="mb-4 p-4 text-sm">
                Last opp nytt eksamensmateriell til systemet. Fyll ut alle
                nødvendige metadata-felt og last opp tilhørende PDF-fil.
              </div>
              <Card>
                <CardContent className="pt-6">
                  <form onSubmit={handleVerifyMaterial} className="space-y-6">
                    {/* Form fields in two columns */}
                    <EksamensmateriellFormFields
                      formData={formData}
                      handleInputChange={handleInputChange}
                      isLoading={isLoading}
                      replacementMode={false}
                    />

                    {/* File upload section */}
                    <div className="space-y-4">
                      <div>
                        <Label
                          htmlFor="file-input"
                          className="text-lg font-medium"
                        >
                          Fil *
                        </Label>
                        <p id="file-input-help" className="text-sm text-muted-foreground mt-1">
                          Last opp én PDF-fil tilknyttet dette eksamensmateriell
                        </p>
                      </div>

                      <div className="space-y-4">
                        {/* File input - only show when no file is selected */}
                        {uploadedFiles.length === 0 && (
                          <input
                            id="file-input"
                            type="file"
                            onChange={handleFileSelect}
                            disabled={isLoading}
                            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-black file:text-white file:cursor-pointer hover:file:bg-primary/90 disabled:opacity-50"
                            accept=".pdf,application/pdf"
                            aria-required="true"
                            aria-describedby="file-input-help"
                          />
                        )}

                        {/* Selected file display */}
                        {uploadedFiles.length > 0 && (
                          <div className="p-3 bg-gray-50 rounded-lg border">
                            <Label className="text-sm font-medium text-gray-700 block mb-2">
                              Valgt fil:
                            </Label>
                            <div className="flex items-center justify-between">
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {uploadedFiles[0].file.name}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {formatFileSize(uploadedFiles[0].file.size)}
                                </p>
                              </div>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={async () => await removeFile(uploadedFiles[0].id)}
                                disabled={isLoading}
                                className="ml-2"
                              >
                                Fjern
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Form validation status */}
                    {!isFormValid() && (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-sm text-blue-800">
                          For å aktivere "Verifiser materiell"-knappen, fyll ut alle obligatoriske felt (markert med *) og last opp en PDF-fil.
                        </p>
                      </div>
                    )}

                    <div className="flex gap-2 pt-4">
                      <Button
                        type="submit"
                        disabled={!isFormValid() || isLoading}
                        className="flex items-center gap-2"
                        aria-describedby={!isFormValid() ? "form-validation-help" : undefined}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin" />
                            Verifiserer...
                          </>
                        ) : (
                          <>
                            <CheckCircle className="w-4 h-4" />
                            Verifiser materiell
                          </>
                        )}
                      </Button>

                      {uploadedFiles.length > 0 && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleReset}
                          disabled={isLoading}
                        >
                          Tilbakestill
                        </Button>
                      )}
                    </div>
                  </form>
                </CardContent>
              </Card>

              {/* Live region for announcements */}
              <div
                id="form-announcements"
                aria-live="polite"
                aria-atomic="true"
                className="sr-only"
              >
                {announcements}
              </div>

            </TabsContent>

            <TabsContent value="existing" className="mt-6">
              <div className="mb-4 p-4 text-sm">
                Søk opp og administrer eksisterende eksamensmateriell. Du kan
                kansellere filer eller erstatte dem med nye versjoner. Fyll ut
                fagkode og eksamensperiode for å søke opp materiell.
              </div>
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    {/* Search Fields with inline fetch button */}
                    <SearchFormFields
                      formData={{
                        fagkode: formData.fagkode,
                        variant: formData.variant,
                        eksamensperiode: formData.eksamensperiode,
                      }}
                      handleInputChange={handleInputChange}
                      isLoading={isLoading}
                      onFetchFiles={handleFetchFiles}
                      canFetch={canFetch}
                      isFetching={isFetching}
                    />

                    {/* Search validation help */}
                    {!canFetch && (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-sm text-blue-800">
                          For å søke etter filer, fyll ut både fagkode og eksamensperiode.
                        </p>
                      </div>
                    )}

                    {/* Existing Files */}
                    <div className="border-t pt-6">
                      <ExistingFilesFetcher
                        existingFiles={existingFiles}
                        isLoading={isFetching}
                        error={fetchError}
                        hasSearched={hasSearched}
                        onDeleteFile={handleDeleteFile}
                        onFileDeleted={handleFileDeleted}
                        onFileReplaced={handleFileReplaced}
                        fileValidator={fileValidator}
                        searchFormData={{
                          fagkode: formData.fagkode,
                          eksamensperiode: formData.eksamensperiode,
                          variant: formData.variant,
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  );
}
