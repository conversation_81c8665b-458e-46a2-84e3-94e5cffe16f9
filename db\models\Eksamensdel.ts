import "reflect-metadata";
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";

@Entity("Eksamensdel")
@Index(["FagkodeeksamensID", "EksamensdelType"], { unique: true })
export class Eksamensdel {
  @PrimaryGeneratedColumn({ type: "int" })
  EksamensdelID!: number;

  @Column({ type: "int", nullable: false })
  FagkodeeksamensID!: number;

  @Column({ type: "varchar", length: 50, nullable: false })
  EksamensdelType!: string;

  @Column({ type: "datetime2", nullable: true })
  GjennomforingStart?: Date | null;

  @Column({ type: "datetime2", nullable: true })
  GjennomforingStopp?: Date | null;

  @Column({ type: "varchar", length: 100, nullable: true })
  Gjennomforingsystem?: string | null;

  @Column({ type: "nvarchar", length: "MAX", nullable: true })
  Eksamensveiledning?: string | null;

  @Column({ type: "bit", default: false })
  ErPlagiatkontroll!: boolean;

  @CreateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  CreatedDate!: Date;

  @UpdateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  ModifiedDate!: Date;

  // Lazy import to avoid circular dependency
  @ManyToOne(
    "Fagkodeeksamen",
    (fagkodeeksamen: any) => fagkodeeksamen.eksamensdeler,
    { onDelete: "CASCADE" }
  )
  @JoinColumn({ name: "FagkodeeksamensID" })
  fagkodeeksamen!: any;

}
