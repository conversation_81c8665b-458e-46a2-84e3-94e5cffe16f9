import { useState, memo } from "react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertCircle,
  Loader2,
  Upload,
  RotateCcw,
  AlertTriangle,
} from "lucide-react";
import {
  formatFileSize,
  DeleteConfirmationDialog,
  useDeleteConfirmation,
  type ExistingFile,
} from "./shared/fileManagementUtils";
import { toast } from "@/components/ui/use-toast";
import { FileCard } from "./FileCard";

interface UploadedFile {
  file: File;
  id: string;
  replacingFileId?: string;
  replacingFilename?: string;
}

interface ExistingFilesFetcherProps {
  existingFiles: ExistingFile[];
  isLoading: boolean;
  error: string | null;
  hasSearched: boolean;
  onDeleteFile: (
    fileId: string,
    filename: string,
    blobReference: string
  ) => void;
  onFileDeleted: (fileId: string) => void;
  onFileReplaced: (fileId: string, newFileData: Partial<ExistingFile>) => void;
  fileValidator: (file: File) => any;
  searchFormData: {
    fagkode: string;
    eksamensperiode: string;
    variant?: string;
  };
}


export const ExistingFilesFetcher = memo(function ExistingFilesFetcher({
  existingFiles,
  isLoading,
  error,
  hasSearched,
  onDeleteFile,
  onFileDeleted,
  onFileReplaced,
  fileValidator,
  searchFormData,
}: ExistingFilesFetcherProps) {
  const [deletingFileId, setDeletingFileId] = useState<string | null>(null);
  const [replacingFile, setReplacingFile] = useState<ExistingFile | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isReplacing, setIsReplacing] = useState(false);
  const [replaceDialogOpen, setReplaceDialogOpen] = useState(false);
  const [fileToReplace, setFileToReplace] = useState<ExistingFile | null>(null);

  const {
    deleteDialogOpen,
    fileToDelete,
    isShared,
    eksamensdeler,
    checkingShared,
    openDeleteDialog,
    handleDeleteCancel,
  } = useDeleteConfirmation();

  const handleDeleteConfirm = async () => {
    if (!fileToDelete) return;

    setDeletingFileId(fileToDelete.id);
    handleDeleteCancel(); // Close dialog

    try {
      await onDeleteFile(
        fileToDelete.id,
        fileToDelete.filename,
        fileToDelete.blobReference
      );

      // Notify parent component to remove from state
      onFileDeleted(fileToDelete.id);
    } catch (error) {
      console.error("Failed to delete file:", error);
      toast({
        variant: "destructive",
        title: "Kunne ikke slette filen",
        description: error instanceof Error ? error.message : "En ukjent feil oppstod under sletting av filen. Prøv igjen.",
      });
    } finally {
      setDeletingFileId(null);
    }
  };

  const handleReplaceFile = (file: ExistingFile) => {
    setReplacingFile(file);
    setUploadedFiles([]);
  };

  const handleReplaceFileSubmitClick = () => {
    if (!replacingFile) return;
    setFileToReplace(replacingFile);
    setReplaceDialogOpen(true);
  };

  const handleReplaceConfirm = async () => {
    setReplaceDialogOpen(false);
    setFileToReplace(null);
    await handleReplaceFileSubmit();
  };

  const handleReplaceCancel = () => {
    setReplaceDialogOpen(false);
    setFileToReplace(null);
  };

  const handleCancelReplacement = () => {
    setReplacingFile(null);
    setUploadedFiles([]);
  };

  const handleFileInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0 || !replacingFile) return;

    const file = files[0]; // Only take the first file

    // Validate file type
    const validationError = fileValidator(file);
    if (validationError) {
      // Show user-friendly error message with toast
      toast({
        variant: "destructive",
        title: "Ugyldig fil",
        description: `${validationError.message}: ${file.name}`,
      });

      // Clear the file input
      event.target.value = "";
      return;
    }

    const newFile: UploadedFile = {
      file,
      id: `${Date.now()}-${Math.random()}`,
      replacingFileId: replacingFile.id,
      replacingFilename: replacingFile.filename,
    };

    setUploadedFiles([newFile]);
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleReplaceFileSubmit = async () => {
    if (!replacingFile || uploadedFiles.length === 0) return;

    setIsReplacing(true);
    try {
      const uploadedFile = uploadedFiles[0];

      // Upload file to Azure Blob Storage using the existing file's blob reference GUID
      const guid = replacingFile.blobReference;

      // Get SAS token for the "oppgaver" container
      const tokenResponse = await fetch(
        `/api/getuploadblobsastoken-oppgaver?guid=${encodeURIComponent(guid)}`
      );
      if (!tokenResponse.ok) {
        throw new Error("Kunne ikke hente opplasting-autorisasjon fra serveren. Sjekk internetttilkobling og prøv igjen.");
      }

      const { sastoken } = await tokenResponse.json();

      // Upload file to Azure Blob Storage (this will overwrite the existing file)
      const { BlobClient } = await import("@azure/storage-blob");
      const blobClient = new BlobClient(sastoken);
      const blockBlobClient = blobClient.getBlockBlobClient();

      const buffer = await uploadedFile.file.arrayBuffer();
      await blockBlobClient.upload(buffer, buffer.byteLength, {
        metadata: {
          originalfilename: encodeURIComponent(uploadedFile.file.name),
          uploadtimestamp: new Date().toISOString(),
          filesize: uploadedFile.file.size.toString(),
        },
      });

      // Update metadata in database
      const replaceResponse = await fetch(
        "/api/admin/replace-eksamensmateriell",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            eksamensmateriellData: [
              {
                eksamensmateriellfilID: replacingFile.id,
                blobReferanse: guid,
                eksamensmateriellkategori: replacingFile.kategori,
                eksamensmateriellMalform: replacingFile.malform,
                fileSize: uploadedFile.file.size,
                mimeType: uploadedFile.file.type || "application/octet-stream",
                originalFilename: uploadedFile.file.name,
                eksamensdel: replacingFile.eksamensdel,
              },
            ],
            oldBlobReference: replacingFile.blobReference,
            searchFormData: searchFormData,
          }),
        }
      );

      const responseData = await replaceResponse.json();

      if (!replaceResponse.ok) {
        throw new Error(
          responseData.error || "Kunne ikke oppdatere filens metadata i databasen. Kontroller internetttilkobling og prøv igjen."
        );
      }

      // Check if the operation was successful
      if (!responseData.success) {
        const errorMessage = responseData.errors
          ? responseData.errors.join('; ')
          : responseData.message || "Erstatning feilet";
        throw new Error(errorMessage);
      }

      // Update the file in the parent component's list
      onFileReplaced(replacingFile.id, {
        filename: uploadedFile.file.name,
        fileSize: uploadedFile.file.size,
        mimeType: uploadedFile.file.type || "application/octet-stream",
      });

      // Reset state
      setReplacingFile(null);
      setUploadedFiles([]);
      
      toast({
        variant: "success",
        title: "Fil erstattet",
        description: `"${replacingFile.filename}" ble erstattet med "${uploadedFile.file.name}"`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Kunne ikke erstatte filen",
        description: error instanceof Error ? error.message : "En ukjent feil oppstod under erstatning av filen. Prøv igjen.",
      });
    } finally {
      setIsReplacing(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Error from parent */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="w-4 h-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}


      {/* No files message */}
      {hasSearched && existingFiles.length === 0 && !error && (
        <Alert>
          <AlertCircle className="w-4 h-4" />
          <AlertDescription>Ingen eksisterende filer funnet.</AlertDescription>
        </Alert>
      )}

      {/* Replacement Mode */}
      {replacingFile && (
        <Alert className="border-blue-200 bg-blue-50">
          <Upload className="w-4 h-4 text-blue-600" />
          <AlertDescription className="text-blue-700">
            <div className="space-y-4">
              <div>
                <span>
                  Erstatter: <strong>{replacingFile.filename}</strong>
                </span>
              </div>

              {/* Simple File Input for Replacement - only show if no file selected */}
              {uploadedFiles.length === 0 && (
                <div className="space-y-8">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Velg ny PDF-fil som skal erstatte den eksisterende
                    </Label>
                    <input
                      type="file"
                      onChange={handleFileInputChange}
                      disabled={isReplacing}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-black file:text-white file:cursor-pointer hover:file:bg-primary/90 disabled:opacity-50"
                      accept=".pdf,application/pdf"
                    />
                  </div>

                  {/* Always show both buttons when no file is selected */}
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleCancelReplacement}
                      disabled={isReplacing}
                      className="text-gray-600"
                    >
                      Avbryt
                    </Button>
                    <Button
                      onClick={handleReplaceFileSubmitClick}
                      disabled={true} // Always disabled when no file selected
                      className="flex items-center gap-2"
                      size="sm"
                    >
                      <RotateCcw className="w-4 h-4" />
                      Erstatt fil
                    </Button>
                  </div>
                </div>
              )}

              {/* Selected File and Actions */}
              {uploadedFiles.length > 0 && (
                <div className="space-y-3 mt-4">
                  <div className="p-3 bg-white rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <Label className="text-sm font-medium text-gray-700 block mb-1">
                          Valgt fil:
                        </Label>
                        <p className="text-sm font-medium truncate">
                          {uploadedFiles[0].file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(uploadedFiles[0].file.size)}
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={async () => await removeFile(uploadedFiles[0].id)}
                        disabled={isReplacing}
                        className="ml-2"
                      >
                        Fjern
                      </Button>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleCancelReplacement}
                      disabled={isReplacing}
                      className="text-gray-600"
                    >
                      Avbryt
                    </Button>
                    <Button
                      onClick={handleReplaceFileSubmitClick}
                      disabled={isReplacing}
                      className="flex items-center gap-2"
                      size="sm"
                    >
                      {isReplacing ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          Erstatter...
                        </>
                      ) : (
                        <>
                          <RotateCcw className="w-4 h-4" />
                          Erstatt fil
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Files List */}
      {existingFiles.length > 0 && !replacingFile && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-semibold text-gray-900">
              Eksisterende filer
            </h4>
            <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              {existingFiles.length} fil{existingFiles.length !== 1 ? "er" : ""}
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {existingFiles.map((file, index) => (
              <FileCard
                key={`${file.id}-${file.blobReference}-${file.eksamensdel}-${index}`}
                file={file}
                onReplace={handleReplaceFile}
                onDelete={openDeleteDialog}
                isDeleting={deletingFileId === file.id}
              />
            ))}
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        filename={fileToDelete?.filename}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isDeleting={deletingFileId === fileToDelete?.id}
        isShared={isShared}
        eksamensdeler={eksamensdeler}
      />

      {/* Replace File Warning Dialog */}
      <Dialog open={replaceDialogOpen} onOpenChange={setReplaceDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-amber-500" />
              Erstatt fil
            </DialogTitle>
            <DialogDescription className="text-left pt-2">
              <span>
                Du er i ferd med å erstatte filen{" "}
                <strong className="font-semibold">
                  {fileToReplace?.filename}
                </strong>
                .
              </span>
            </DialogDescription>
            <div className="space-y-3">
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <p className="text-sm text-amber-800">
                  <strong>Viktig:</strong> Etter at filen er erstattet, må den
                  manuelt lastes opp i PGSD for at Udir-søket skal kunne hente
                  den oppdaterte filen etter eksamen.
                </p>
              </div>
              <p className="text-sm text-muted-foreground">Ønsker du å fortsette?</p>
            </div>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleReplaceCancel}
            >
              Avbryt
            </Button>
            <Button type="button" onClick={handleReplaceConfirm}>
              Erstatt fil
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
});
