"use server";

import { getServerSession } from "next-auth/next";
import { getAccessToken } from "@/lib/server/getAccessToken";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { NextRequest, NextResponse } from "next/server";
import { getClientIp } from "@/lib/server/getClientIp";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

const telemetryClient = getAppInsightsServer();
interface IOpenNewDeliveryPayload {
  userId: string;
  username: string;
  ipAddress: string;
}
export async function POST(request: NextRequest) {
  const { userId, testpartId } = await request.json();

  let response = null;
  let body: IOpenNewDeliveryPayload | null = null;

  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const administratorUserId = session.user.userInfo.userId;
    const ip = await getClientIp(request);

    let body: IOpenNewDeliveryPayload = {
      userId: userId,
      username: administratorUserId,
      ipAddress: ip,
    };

    // Get access token
    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    if (!accessToken) {
      return NextResponse.json(
        { error: "Failed to obtain access token" },
        { status: 500 }
      );
    }

    if (!body.userId) {
      return NextResponse.json(
        { message: "Mangler userid/kandidatpåmeldingsid i forespørselen" },
        { status: 400 }
      );
    }

    // Make the API call
    const apiUrl = `${PgsaApiUrl}/api/Monitor/${body.userId}/open-new-delivery`;

    response = await fetch(apiUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
      cache: "no-store",
    });

    if (!response.ok) {
      const data = await response.text();
      console.log("Error in opening for new delivery to backend:", data);

      return NextResponse.json(data, { status: response.status });
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        payload: body || {},
        action: "openForNewDelivery",
        statuscode: response ? response.status : 0,
        response: response ? await response.text() : "Tom respons",
      },
    });
    console.error("Error in opening for delivery", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
