import { BlobServiceClient } from "@azure/storage-blob";
import { getAppInsightsServer } from "./appInsightsServer";

const connectionString = process.env.CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING;
const containerName = "pgs-admin";

const telemetryClient = getAppInsightsServer();

export async function downloadJsonFromAzure(fileName: string): Promise<any> {
  if (!connectionString) {
    throw new Error("Azure connection string is not configured");
  }

  try {
    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(fileName);

    const downloadResponse = await blobClient.download();
    const downloaded = await streamToBuffer(
      downloadResponse.readableStreamBody
    );
    const jsonContent = JSON.parse(downloaded.toString());

    return jsonContent;
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "downloadJsonFromAzure",
        fileName,
      },
    });
    console.error("Error downloading blob:", error);
    throw new Error("Failed to download the JSON file");
  }
}

async function streamToBuffer(
  readableStream: NodeJS.ReadableStream | undefined
): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    if (!readableStream) {
      reject(new Error("Readable stream is undefined"));
      return;
    }
    readableStream.on("data", (data) => {
      chunks.push(Buffer.from(data));
    });
    readableStream.on("end", () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on("error", reject);
  });
}
