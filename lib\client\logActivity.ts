export interface LogActivityOptions {
  retries?: number;
  retryDelay?: number;
  throwOnError?: boolean;
}

export async function logActivity(
  userId: string,
  fileName: string,
  examPartId: string,
  role: string,
  operationId: number,
  candidateFirstName: string,
  candidateLastName: string,
  candidateRegistrationId: string,
  candidateNumber: string,
  parameters?: Record<string, any>,
  options: LogActivityOptions = {}
): Promise<{ success: boolean; error?: string }> {
  const {
    retries = 2,
    retryDelay = 1000,
    throwOnError = false
  } = options;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetch(`/api/logActivity`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: userId,
          FileName: fileName,
          TestPartId: examPartId,
          OperationId: operationId,
          Parameters: parameters,
          Role: role,
          candidateFirstName,
          candidateLastName,
          candidateRegistrationId,
          candidateNumber,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }));
        const errorMessage = `Failed to log activity. Status: ${response.status}. Details: ${errorData.error || response.statusText}`;

        lastError = new Error(errorMessage);

        // Don't retry on client errors (4xx), only server errors (5xx)
        if (response.status < 500 || attempt === retries) {
          console.error(errorMessage, {
            userId,
            operationId,
            attempt: attempt + 1,
            status: response.status
          });

          if (throwOnError) {
            throw lastError;
          }
          return { success: false, error: errorMessage };
        }

        // Wait before retry for server errors
        if (attempt < retries) {
          console.warn(`Retrying logActivity in ${retryDelay}ms (attempt ${attempt + 1}/${retries + 1})`);
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
        }
        continue;
      }

      // Success
      await response.json(); // Consume response
      if (attempt > 0) {
        console.log(`logActivity succeeded on attempt ${attempt + 1}`);
      }
      return { success: true };

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt === retries) {
        console.error("Final attempt failed for logActivity:", {
          error: lastError.message,
          userId,
          operationId,
          attempt: attempt + 1
        });

        if (throwOnError) {
          throw lastError;
        }
        return { success: false, error: lastError.message };
      }

      // Wait before retry
      console.warn(`Network error, retrying logActivity in ${retryDelay}ms (attempt ${attempt + 1}/${retries + 1}):`, lastError.message);
      await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
    }
  }

  // This should never be reached, but just in case
  return { success: false, error: lastError?.message || "Unknown error" };
}
