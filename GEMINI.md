# Project Overview

This is a Next.js web application for administering the PGS (presumably an exam system). It is built with TypeScript, Next.js, and uses TypeORM for database interaction. The application is designed to be deployed on Azure and uses Azure services for authentication and other functionalities.

## Key Technologies

*   **Framework:** Next.js
*   **Language:** TypeScript
*   **Database:** Microsoft SQL Server with TypeORM
*   **Authentication:** NextAuth.js with Duende IdentityServer 6
*   **Styling:** Tailwind CSS with shadcn/ui
*   **Internationalization:** next-intl
*   **Deployment:** Azure App Service

## Architecture

The application follows a standard Next.js project structure. Here's a breakdown of the key directories:

*   `app/[locale]`: Contains the main application pages, layouts, and components. The `[locale]` directory indicates that the application supports internationalization.
*   `app/api`: Contains the API routes, including authentication endpoints.
*   `components`: Contains reusable React components.
*   `context`: Contains React context providers for managing application state.
*   `db`: Contains the database connection, data source, and TypeORM models.
*   `hooks`: Contains custom React hooks.
*   `interface`: Contains TypeScript interfaces for data structures.
*   `lib`: Contains utility functions and server-side logic.
*   `public`: Contains static assets like images and fonts.
*   `translations`: Contains the translation files for internationalization.

# Building and Running

## Prerequisites

*   Node.js
*   npm, yarn, or pnpm

## Installation

```bash
npm install
```

## Running the Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:4000`.

## Building for Production

```bash
npm run build
```

This will create an optimized production build in the `.next` directory.

## Starting the Production Server

```bash
npm run start
```

This will start the production server.

# Development Conventions

*   **Coding Style:** The project uses the standard Next.js ESLint and Prettier configurations.
*   **Testing:** There are no explicit testing frameworks configured in the `package.json` file. However, the project structure suggests that tests could be added in the future.
*   **Authentication:** Authentication is handled by NextAuth.js. The authentication configuration is located in `app/api/auth/authOptions.ts`.
*   **Database:** The project uses TypeORM for database interaction. The database models are located in the `db/models` directory.
*   **Internationalization:** The project uses `next-intl` for internationalization. The translation files are located in the `translations` directory.
*   **Styling:** The project uses Tailwind CSS with shadcn/ui for styling.
