import { NextRequest, NextResponse } from "next/server";
import { getStatusInfoFromPgsa } from "@/lib/server/getStatusInfoFromPgsa";
import { IStatusInfo } from "@/interface/IStatusInfo";

interface GetStatusInfoRequest {
  userId: string;
}

interface GetStatusInfoResponse extends IStatusInfo {}

interface ErrorResponse {
  error: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<GetStatusInfoResponse | ErrorResponse>> {
  try {
    const body = await request.json() as GetStatusInfoRequest;
    const { userId } = body;

    if (!userId || typeof userId !== 'string') {
      return NextResponse.json(
        { error: "userId is required and must be a string" },
        { status: 400 }
      );
    }

    const statusInfo: IStatusInfo = await getStatusInfoFromPgsa(userId);
    
    return NextResponse.json(statusInfo);
  } catch (error) {
    console.error("Error getting status info:", error);
    return NextResponse.json(
      { error: "Failed to get status info" },
      { status: 500 }
    );
  }
}
