import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Les begge JSON-filene
const metadataPath = path.join(__dirname, 'PGSD-test-metadata.json');
const copiedFilesPath = path.join(__dirname, 'copied-files.json');

console.log('Leser metadata fil...');
const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));

console.log('Leser copied files fil...');
const copiedFiles = JSON.parse(fs.readFileSync(copiedFilesPath, 'utf8'));

console.log(`Fant ${metadata.length} metadata entries og ${copiedFiles.length} copied files`);

// Opprett index av metadata basert på matching-strategier
const metadataByGenFileName = new Map();
const metadataByComposite = new Map();

metadata.forEach(item => {
    // Index by GenFileName hvis den finnes
    if (item.GenFileName) {
        metadataByGenFileName.set(item.GenFileName, item);
    }

    // Index by composite key (fagkode + eksamensperiode + eksamensdel + filnavn)
    if (item.Fagkode && item.Eksamensperiode && item.Eksamensdel && item.OrginaltFilnavn) {
        const compositeKey = `${item.Fagkode}|${item.Eksamensperiode}|${item.Eksamensdel}|${item.OrginaltFilnavn}`;
        if (!metadataByComposite.has(compositeKey)) {
            metadataByComposite.set(compositeKey, []);
        }
        metadataByComposite.get(compositeKey).push(item);
    }
});

// Funksjon for å mappe eksamensdel verdier
function mapEksamensdel(copiedFileValue, metadataValue) {
    // Direkte match
    if (copiedFileValue === metadataValue) return true;

    // Mapping mellom forskjellige formater
    const mappings = {
        'Eksamen Del 1': ['Eksamen del 1', 'Eksamen del 1 og del 2'],
        'Eksamen Del 2': ['Eksamen del 2', 'Eksamen del 1 og del 2'],
        'Eksamen': ['Eksamen'],
        'Forberedelse': ['Forberedelse']
    };

    return mappings[copiedFileValue]?.includes(metadataValue) || false;
}

// Match copied files med metadata
const enhancedCopiedFiles = copiedFiles.map(copiedFile => {
    let match = null;
    let matchType = 'none';

    // Match 1: GenFileName mot PgsaFilename
    if (copiedFile.PgsaFilename && metadataByGenFileName.has(copiedFile.PgsaFilename)) {
        match = metadataByGenFileName.get(copiedFile.PgsaFilename);
        matchType = 'GenFileName_vs_PgsaFilename';
    }

    // Match 2: Komposittnøkkel (fagkode + eksamensperiode + eksamensdel + filnavn)
    if (!match && copiedFile.Fagkode && copiedFile.OrginaltFilnavn && copiedFile.Eksamensdeltype_Id) {
        // Bygg eksamensperiode fra År og Semester_Id hvis ikke finnes
        let eksamensperiode = copiedFile.Eksamensperiode;
        if (!eksamensperiode && copiedFile.År && copiedFile.Semester_Id) {
            eksamensperiode = `${copiedFile.Semester_Id}-${copiedFile.År}`;
        }

        if (eksamensperiode) {
            // Søk gjennom metadata for match med flexible eksamensdel mapping
            const candidateMatches = metadata.filter(m =>
                m.Fagkode === copiedFile.Fagkode &&
                m.Eksamensperiode === eksamensperiode &&
                m.OrginaltFilnavn === copiedFile.OrginaltFilnavn &&
                mapEksamensdel(copiedFile.Eksamensdeltype_Id, m.Eksamensdel)
            );

            if (candidateMatches.length > 0) {
                match = candidateMatches[0];
                matchType = 'Composite_FagkodePeriodeEksamensdelFilnavn';
            }
        }
    }

    // Returner copied file med tillegg av opphavsrett og vedlegg
    const enhanced = {
        ...copiedFile,
        Opphavsrett: match ? match.Opphavsrett : null,
        Vedlegg: match ? match.Vedlegg : null
    };

    return enhanced;
});

// Statistikk
const totalMatched = enhancedCopiedFiles.filter(f => f.Opphavsrett !== null || f.Vedlegg !== null).length;
const matchedByGenFileName = enhancedCopiedFiles.filter(f => f.Opphavsrett !== null && enhancedCopiedFiles.indexOf(f) < enhancedCopiedFiles.length).length;
const unmatched = enhancedCopiedFiles.length - totalMatched;

console.log('Matching statistikk (forenklet):');
console.log(`Total filer: ${copiedFiles.length}`);
console.log(`Matchet med metadata: ${totalMatched}`);
console.log(`Umatchet: ${unmatched}`);

// Filtrer kun filer som har opphavsrett eller vedlegg
const filteredFiles = enhancedCopiedFiles.filter(file =>
    file.Opphavsrett === "1" || file.Vedlegg === "1"
);

console.log(`\nFiltrering: ${enhancedCopiedFiles.length} -> ${filteredFiles.length} filer (kun med opphavsrett eller vedlegg)`);

// Skriv resultatet til ny fil
const outputPath = path.join(__dirname, 'enhanced-copied-files-simplified.json');
fs.writeFileSync(outputPath, JSON.stringify(filteredFiles, null, 2), 'utf8');

console.log(`\nResultat skrevet til: ${outputPath}`);
console.log(`Totalt ${filteredFiles.length} filer med opphavsrett eller vedlegg inkludert`);

// Vis eksempler på filtrerte filer
console.log('\nEksempler på filer med opphavsrett eller vedlegg:');
filteredFiles
    .slice(0, 5)
    .forEach(f => {
        console.log(`- ${f.OrginaltFilnavn} -> Opphavsrett: ${f.Opphavsrett}, Vedlegg: ${f.Vedlegg}`);
    });

// Vis statistikk for opphavsrett og vedlegg
const opphavsrettCount = filteredFiles.filter(f => f.Opphavsrett === "1").length;
const vedleggCount = filteredFiles.filter(f => f.Vedlegg === "1").length;
const bothCount = filteredFiles.filter(f => f.Opphavsrett === "1" && f.Vedlegg === "1").length;

console.log(`\nOpphavsrett/Vedlegg statistikk:`);
console.log(`- Kun opphavsrett: ${opphavsrettCount - bothCount}`);
console.log(`- Kun vedlegg: ${vedleggCount - bothCount}`);
console.log(`- Både opphavsrett og vedlegg: ${bothCount}`);
console.log(`- Totalt: ${filteredFiles.length}`);