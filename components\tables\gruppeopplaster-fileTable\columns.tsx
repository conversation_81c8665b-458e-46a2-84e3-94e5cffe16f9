"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TestPartsEnum } from "@/enums/TestPart";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { ColumnDef, Table } from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown } from "lucide-react";
import { IoWarning } from "react-icons/io5";
import { GoTrash } from "react-icons/go";
import { ImSpinner2 } from "react-icons/im";

function formatFileSize(bytes: number) {
  const KB = 1024;
  const MB = 1024 * KB;
  if (bytes < MB) {
    return `${(bytes / KB).toFixed(0)} kB`;
  } else {
    return `${(bytes / MB).toFixed(2)} MB`;
  }
}

function formatTestPartId(testpartId: TestPartsEnum) {
  switch (testpartId) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og 2";
    default:
      return (
        <div className="flex items-center gap-1 text-red-600">
          <IoWarning className="text-lg" />
          {"Angi eksamensdel"}
        </div>
      );
  }
}

function sumFileSize(table: Table<IUploadedFile>) {
  let totalSize = 0;
  const rowCount = table.getRowCount();
  for (let i = 0; i < rowCount; i++) {
    const row = table.getRow(i.toString()); // Convert i to a string
    const rowData = row.original;
    totalSize += rowData.Size;
  }
  return totalSize;
}

function getDistinctCandidatesNumber(table: Table<IUploadedFile>) {
  const candidatesSet = new Set();

  for (let i = 0; i < table.getRowCount(); i++) {
    const row = table.getRow(i.toString()); // Convert i to a string
    const file = row.original.File;
    const hyphenIndex = file?.name.indexOf("-");

    if (hyphenIndex !== -1) {
      const candidateNumber = file?.name.substring(0, hyphenIndex);
      candidatesSet.add(candidateNumber);
    }
  }

  return candidatesSet.size;
}
function minimizeSuffix(filename: string) {
  const parts = filename.split(".");
  if (parts.length > 1) {
    const suffix = parts.pop();
    return parts.join(".") + "." + suffix?.toLowerCase();
  }

  return filename;
}
export const getColumns = ({
  handleFileDelete,
  filesToDelete,
  isUploading,
  downloadFile,
  setTestPart,
}: {
  handleFileDelete: (file: IUploadedFile) => Promise<void>;
  filesToDelete: Set<string>;
  isUploading: boolean;
  downloadFile: (documentCode: string, fileName: string) => Promise<void>;
  setTestPart: (fileGuid: string, testPart: TestPartsEnum) => Promise<void>;
}): ColumnDef<IUploadedFile>[] => {
  return [
    {
      accessorKey: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          disabled={isUploading}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all files"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={`Select row ${row.original.Name}`}
          disabled={isUploading}
        />
      ),
    },
    {
      accessorKey: "Name",
      header: ({ table, column }) => {
        const rowCount = table.getRowCount();
        return (
          <div className="text-left w-full">
            {" "}
            <Button
              variant="ghost"
              className="p-0 hover:bg-transparent justify-start w-full"
              onClick={() => {
                column.toggleSorting(column.getIsSorted() === "asc");
              }}
            >
              <div className="capitalize mr-2">Filer ({rowCount})</div>{" "}
              <ArrowUpDown
                className="h-4 w-4"
                role="img"
                aria-label="pil opp ned ikon"
              />
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const file = row.original.File;
        return (
          <div className="text-left">
            <Button
              variant="link"
              className="mt-1 text-current  text-left p-0"
              onClick={() =>
                downloadFile(row.original.FileGuid, row.original.Name)
              }
            >
              {minimizeSuffix(row.original.Name)}
            </Button>
          </div>
        );
      },
    },

    {
      accessorKey: "Candididate",
      header: ({ table, column }) => {
        const rowCount = table.getRowCount();

        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => {
              column.toggleSorting(column.getIsSorted() === "asc");
            }}
          >
            <div className="capitalize ">
              Kandidater ({getDistinctCandidatesNumber(table)})
            </div>
            <ArrowUpDown
              className="ml-2 h-4 w-4"
              role="img"
              aria-label="pil opp ned ikon"
            />
          </Button>
        );
      },
      cell: ({ row }) => {
        return <div className="">{row.original.Candididate}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "CandididateName",
      header: ({ table, column }) => {
        const rowCount = table.getRowCount();
  
        return (
          <Button
            className="p-0 hover:bg-transparent"
            variant="ghost"
            onClick={() => {
              column.toggleSorting(column.getIsSorted() === "asc");
            }}
          >
            <div className="capitalize ">
              Kandidatnavn ({getDistinctCandidatesNumber(table)})
            </div>
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return <div className="">{row.original.CandidateName}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "SubjectCode",
      header: ({ table, column }) => {
        const rowCount = table.getRowCount();
        const subjectCodesSet = new Set();
        for (let i = 0; i < rowCount; i++) {
          const row = table.getRow(i.toString()); // Convert i to a string
          const subjectCode = row.original.SubjectCode;
          subjectCodesSet.add(subjectCode);
        }
        const uniqueSubjectCodesCount = subjectCodesSet.size;
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => {
              column.toggleSorting(column.getIsSorted() === "asc");
            }}
          >
            <div className="capitalize">
              Fagkoder ({uniqueSubjectCodesCount})
            </div>
            <ArrowUpDown
              className="ml-2 h-4 w-4"
              role="img"
              aria-label="pil opp ned ikon"
            />
          </Button>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "Size",
      header: ({ table, column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => {
              column.toggleSorting(column.getIsSorted() === "asc");
            }}
          >
            <div>{`Størrelse (${formatFileSize(sumFileSize(table))})`}</div>
            <ArrowUpDown
              className="ml-2 h-4 w-4"
              role="img"
              aria-label="pil opp ned ikon"
            />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="">{formatFileSize(row.getValue("Size"))}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "TestPartId",
      header: ({ table, column }) => {
        return (
          <div className="text-center">
            {" "}
            {/* Midtstill header */}
            <Button
              variant="ghost"
              onClick={() => {
                column.toggleSorting(column.getIsSorted() === "asc");
              }}
              className="p-0 hover:bg-transparent"
            >
              <div className="capitalize">Eksamensdel</div>
              <ArrowUpDown
                className="ml-2 h-4 w-4"
                role="img"
                aria-label="pil opp ned ikon"
              />
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const file = row.original;
        const testPart = row.getValue("TestPartId") as TestPartsEnum;
        const handleTestPartChange = (newTestPart: TestPartsEnum) => {
          try {
            setTestPart(file.FileGuid, newTestPart);
          } catch (error) {}
        };
        if (testPart === TestPartsEnum.Eksamen) {
          return (
            <div className="text-center capitalize">
              {formatTestPartId(testPart)}
            </div>
          );
        }
        return (
          <div className="flex justify-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="justify-center min-w-[150px] border border-gray-300"
                  disabled={
                    file.IsLoading ||
                    file.IsSubmitting ||
                    file.IsDeleting ||
                    isUploading
                  }
                >
                  {file.IsLoading ? (
                    <ImSpinner2
                      className="animate-spin text-lg mr-2"
                      role="img"
                      aria-label="spinner"
                    />
                  ) : (
                    <span>{formatTestPartId(testPart)}</span>
                  )}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() =>
                    handleTestPartChange(TestPartsEnum.EksamenDel1)
                  }
                >
                  {formatTestPartId(TestPartsEnum.EksamenDel1)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    handleTestPartChange(TestPartsEnum.EksamenDel2)
                  }
                >
                  {formatTestPartId(TestPartsEnum.EksamenDel2)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    handleTestPartChange(TestPartsEnum.EksamenDel1ogDel2)
                  }
                >
                  {formatTestPartId(TestPartsEnum.EksamenDel1ogDel2)}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
      enableSorting: true,
    },

    {
      accessorKey: "Delete",
      header: () => <div></div>,
      cell: ({ row }) => (
        <Button
          variant="outline"
          className="flex items-center gap-1 w-28"
          disabled={
            row.original.IsDeleting ||
            row.original.IsSubmitting ||
            row.original.IsLoading ||
            filesToDelete.has(row.original.FileGuid) ||
            isUploading
          }
          onClick={() => {
            handleFileDelete(row.original);
          }}
        >
          {row.original.IsDeleting ||
          filesToDelete.has(row.original.FileGuid) ? (
            <ImSpinner2
              className="animate-spin text-lg"
              role="img"
              aria-label="spinner"
            />
          ) : (
            <GoTrash role="img" aria-label="slett fil" className="text-lg" />
          )}
          <span>
            {row.original.IsDeleting || filesToDelete.has(row.original.FileGuid)
              ? "Sletter..."
              : "Slett"}
          </span>
        </Button>
      ),
    },
  ];
};
