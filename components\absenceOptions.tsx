import React from "react";
import {
  DropdownMenuGroup,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { useCandidate } from "@/context/CandidateMonitorContext";
import { useRole } from "@/context/RoleContext";
import { TestPartsEnum } from "@/enums/TestPart";
import { toast } from "./ui/use-toast";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { useDialogMonitor } from "@/context/DialogMonitorContext";
import { ActivityLogger } from "@/lib/shared/ActivityLogger";
import { OperationEnum } from "@/enums/OperationEnum";

interface AbsenceDropdownProps {
  candidateInfo: ICandidateMonitor;
}

const AbsenceOptions: React.FC<AbsenceDropdownProps> = ({ candidateInfo }) => {
  const { hasAbsence, handleStatusUpdate, updateHasAbsence } = useCandidate();
  const { openDialog } = useDialogMonitor();
  const { selectedRole } = useRole();

  const isOnePartSubmission = candidateInfo.deliveryStatusPart2 === -1;

  const currentAbsenceStatus =
    candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.DokumentertFravaer
      ? "documented"
      : candidateInfo.deliveryStatusPart1 ===
        CandidateStatusEnum.IkkeDokumentertFravaer
      ? "undocumented"
      : "none";

  const handleApiCall = async (
    url: string,
    body: any,
    errorMessage: string
  ): Promise<boolean> => {
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw await response.json();
      }
      return true;
    } catch (error: any) {
      toast({
        title: "En feil har oppstått:",
        description: errorMessage || error.error,
        variant: "destructive",
      });
      return false;
    }
  };

  const fetchAndUpdateMetadata = async (userId: string) => {
    try {
      const metadataResponse = await fetch(`/api/candidateMetadata/${userId}`);
      if (!metadataResponse.ok) {
        throw new Error("Kunne ikke hente oppdatert kandidatmetadata");
      }
      const candidateMetadata = await metadataResponse.json();
      const testPartId = isOnePartSubmission
        ? TestPartsEnum.Eksamen
        : TestPartsEnum.EksamenDel2;

      handleStatusUpdate({
        candidateNumber: candidateInfo.candidateNumber,
        statusPart1: candidateMetadata.deliveryStatusPart1,
        statusPart2: candidateMetadata.deliveryStatusPart2,
        testPartId,
      });
    } catch (error: any) {
      toast({
        title: "En feil har oppstått:",
        description: "Feil ved oppdatering av metadata",
        variant: "destructive",
      });
    }
  };

  const setAbsence = async (status: CandidateStatusEnum) => {
    const success = await handleApiCall(
      `/api/setAbsenceStatus`,
      {
        userid: candidateInfo.userId,
        examGroupCode: candidateInfo.groupCode,
        candidateNumber: candidateInfo.candidateNumber,
        statusId: status,
        testparts: isOnePartSubmission ? 1 : 2,
      },
      `Kunne ikke sette fravær for ${candidateInfo.candidateNumber}`
    );

    if (success) {
      await fetchAndUpdateMetadata(candidateInfo.userId);
      await handleApiCall(
        `/api/deauthorizeUserObjects`,
        {
          candidateNumber: candidateInfo.candidateNumber,
        },
        `Kunne ikke sette fravær for ${candidateInfo.candidateNumber}`
      );

      try {
        await fetch(`/api/grantAccessRequest`, {
          method: "DELETE",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            candidateNumber: candidateInfo.candidateNumber,
            schoolId: selectedRole?.selectedSchoolId,
          }),
        });
      } catch (error) {
        console.error("Failed to remove grant access request:", error);
      }

      await ActivityLogger.logActivity(
        status === 11
          ? OperationEnum.StatusDokumentedAbsence
          : OperationEnum.StatusUnDocumentedAbsence,
        {
          userId: candidateInfo.userId,
          candidateName: candidateInfo.candidateName || "",
          candidateNumber: candidateInfo.candidateNumber || "",
          candidateRegistrationId:
            candidateInfo.candidateRegistrationId || candidateInfo.userId,
        },
        {
          roleName: `${selectedRole?.displayRoleName}`,
        }
      );

      updateHasAbsence({
        ...hasAbsence,
        [candidateInfo.candidateNumber]: true,
      });
    }
  };

  const undoAbsence = async () => {
    const success = await handleApiCall(
      `/api/undoAbsence`,
      {
        userId: candidateInfo.userId,
        schoolId: selectedRole?.selectedSchoolId,
        candidateNumber: candidateInfo.candidateNumber,
      },
      `Kunne ikke oppheve fravær for ${candidateInfo.candidateNumber}`
    );

    if (success) {
      await ActivityLogger.logActivity(
        OperationEnum.StatusUndoAbsence,
        {
          userId: candidateInfo.userId,
          candidateName: candidateInfo.candidateName || "",
          candidateNumber: candidateInfo.candidateNumber || "",
          candidateRegistrationId:
            candidateInfo.candidateRegistrationId || candidateInfo.userId,
        },
        {
          roleName: `${selectedRole?.displayRoleName}`,
        }
      );

      await fetchAndUpdateMetadata(candidateInfo.userId);
      updateHasAbsence({
        ...hasAbsence,
        [candidateInfo.candidateNumber]: false,
      });
    }
  };

  const handleAbsenceSelection = async (absenceStatus: CandidateStatusEnum) => {
    const hasUploadedOrDelivered = [
      CandidateStatusEnum.LastetOpp,
      CandidateStatusEnum.Levert,
      CandidateStatusEnum.LevertManuelt,
    ].some(
      (status) =>
        candidateInfo.deliveryStatusPart1 === status ||
        candidateInfo.deliveryStatusPart2 === status
    );

    if (hasUploadedOrDelivered) {
      openDialog("warningAbsence", candidateInfo, {
        onConfirmSetAbsence: async () => await setAbsence(absenceStatus),
      });
    } else {
      await setAbsence(absenceStatus);
    }
  };

  return (
    <DropdownMenuGroup>
      {/* Vis motsatt fraværstype av den som er satt */}
      {currentAbsenceStatus === "documented" ? (
        <DropdownMenuItem
          className="cursor-pointer focus:bg-[#D8D8ED]"
          onSelect={() =>
            handleAbsenceSelection(CandidateStatusEnum.IkkeDokumentertFravaer)
          }
        >
          Ikke-dokumentert fravær
        </DropdownMenuItem>
      ) : currentAbsenceStatus === "undocumented" ? (
        <DropdownMenuItem
          className="cursor-pointer focus:bg-[#D8D8ED]"
          onSelect={() =>
            handleAbsenceSelection(CandidateStatusEnum.DokumentertFravaer)
          }
        >
          Dokumentert fravær
        </DropdownMenuItem>
      ) : (
        /* Vis begge alternativene hvis ingen fravær er satt */
        <>
          <DropdownMenuItem
            className="cursor-pointer focus:bg-[#D8D8ED]"
            onSelect={() =>
              handleAbsenceSelection(CandidateStatusEnum.DokumentertFravaer)
            }
          >
            Dokumentert fravær
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer focus:bg-[#D8D8ED]"
            onSelect={() =>
              handleAbsenceSelection(CandidateStatusEnum.IkkeDokumentertFravaer)
            }
          >
            Ikke-dokumentert fravær
          </DropdownMenuItem>
        </>
      )}

      {/* Vis alltid "Opphev fravær" hvis kandidaten har fravær */}
      {hasAbsence[candidateInfo.candidateNumber] && (
        <DropdownMenuItem
          className="cursor-pointer focus:bg-[#D8D8ED]"
          onSelect={undoAbsence}
        >
          Opphev fravær
        </DropdownMenuItem>
      )}
    </DropdownMenuGroup>
  );
};

export default AbsenceOptions;
