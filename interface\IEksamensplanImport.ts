export interface IEksamensplanImportResponse {
  success: boolean;
  message: string;
  data?: {
    eksamensperiode: string;
    importedCount: number;
    errorCount: number;
    deletedCount: number;
    user: string;
    errorDetails?: string[];
  };
  error?: string;
  details?: string;
}

export interface IEksamensplanApiData {
  fagkode: string;
  variantkode?: string;
  eksamensperiode: string;
  fagnavn: string;
  varighet?: number;
  eksamensdato?: string;
  eksamenstid?: string;
  erTestFagkode?: boolean;
  oppgaveansvar?: string;
  opplaeringsniva?: string;
  eksamensdeler?: Array<{
    eksamensdelType: string;
    gjennomforingStart?: string;
    gjennomforingStopp?: string;
    gjennomforingsystem?: string;
    eksamensveiledning?: string;
    erPlagiatkontroll?: boolean;
  }>;
}
