import { NextRequest, NextResponse } from "next/server";
import EksamensmateriellService from "@/db/services/eksamensmateriellService";
import { BlobServiceClient } from "@azure/storage-blob";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/authOptions";
import { generateAccessToken } from "@/lib/server/getAccessTokenForEksamen";

export const dynamic = "force-dynamic";
const pgsaBaseUrl = process.env.PGSA_VERIFICATION_API_URL;
const pgsaCode = process.env.PGSA_VERIFICATION_API_URL_CODE;

// Helper function for PGSA deletion API call
async function sendPgsaDeletion(blobReference: string) {
  if (!pgsaBaseUrl || !pgsaCode) {
    console.warn(
      "PGSA_VERIFICATION_API_URL or PGSA_VERIFICATION_API_URL_CODE not configured, skipping PGSA deletion call"
    );
    return;
  }

  try {
    // Create DELETE URL for PGSA API
    const deleteUrl = `${pgsaBaseUrl}/api/examdata/${blobReference}?code=${pgsaCode}`;

    // Call PGSA deletion endpoint
    const response = await fetch(deleteUrl, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorText = await response
        .text()
        .catch(() => "Could not read error response");
      console.error("PGSA deletion failed:", {
        status: response.status,
        statusText: response.statusText,
        body: errorText,
        blobReference: blobReference,
      });
      throw new Error(
        `PGSA-sletting feilet med status: ${response.status}. ${errorText}`
      );
    }

    console.log(`PGSA deletion successful for ${blobReference}`);
  } catch (error) {
    console.error("PGSA deletion call failed for", blobReference, ":", error);
    // Throw error to notify caller of PGSA deletion failure
    throw new Error(
      `PGSA-sletting feilet: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`
    );
  }
}

// Helper function for PAS deletion API call
async function sendPasDeletion(blobReference: string) {
  const externalApiUrl = process.env.PAS_VERIFICATION_API_URL;

  if (!externalApiUrl) {
    console.warn(
      "PAS_VERIFICATION_API_URL not configured, skipping PAS deletion call"
    );
    return;
  }

  try {
    // Generate access token for PAS API
    const token = await generateAccessToken();

    // Create DELETE URL with document GUID in path
    const deleteUrl = `${externalApiUrl}/${blobReference}`;

    // Call PAS deletion endpoint
    const response = await fetch(deleteUrl, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorText = await response
        .text()
        .catch(() => "Could not read error response");
      console.error("PAS deletion failed:", {
        status: response.status,
        statusText: response.statusText,
        body: errorText,
        blobReference: blobReference,
      });
      throw new Error(
        `PAS-sletting feilet med status: ${response.status}. ${errorText}`
      );
    }

    console.log(`PAS deletion successful for ${blobReference}`);
  } catch (error) {
    console.error("PAS deletion call failed for", blobReference, ":", error);
    // Throw error to notify caller of PAS deletion failure
    throw new Error(
      `PAS-sletting feilet: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const userRoles = Array.isArray(session.user.role)
      ? session.user.role
      : [session.user.role];

    const hasAdminRole = userRoles.some(
      (role) => role === "urn:udir:pgsa:administrator"
    );

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get("fileId");
    const blobReference = searchParams.get("blobReference");

    if (!fileId || !blobReference) {
      return NextResponse.json(
        { error: "fileId og blobReference er påkrevet" },
        { status: 400 }
      );
    }

    // Delete from blob storage first
    const connectionString =
      process.env.CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING;
    if (connectionString) {
      try {
        const blobServiceClient =
          BlobServiceClient.fromConnectionString(connectionString);
        const containerClient =
          blobServiceClient.getContainerClient("oppgaver");

        // Delete the blob
        await containerClient.deleteBlob(blobReference);
        console.log(
          `Blob ${blobReference} deleted successfully from Azure Storage`
        );
      } catch (blobError) {
        console.error("Blob deletion failed:", blobError);

        return NextResponse.json(
          { error: "Kunne ikke slette fil fra blob storage" },
          { status: 500 }
        );
      }
    } else {
      console.warn(
        "CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING ikke konfigurert - hopper over blob-sletting"
      );
    }

    // Delete from database after blob and PAS deletion
    try {
      await EksamensmateriellService.deleteEksamensmateriellByBlobReference(
        blobReference
      );
    } catch (dbError) {
      console.error("Database deletion failed:", dbError);
      // Note: Blob is already deleted, so we log but don't fail the request
      // In a production system, you might want to implement a cleanup job
      console.warn(
        `Database record for blob ${blobReference} could not be deleted, but blob was removed from storage`
      );

      return NextResponse.json(
        { error: "Kunne ikke slette fra database" },
        { status: 500 }
      );
    }

    // Call external APIs deletion before database deletion
    try {
      await sendPasDeletion(blobReference);
    } catch (pasError) {
      console.error("PAS deletion failed:", pasError);
      // Note: Blob is already deleted, but PAS deletion failed
      return NextResponse.json(
        {
          error:
            pasError instanceof Error
              ? pasError.message
              : "PAS-sletting feilet",
        },
        { status: 500 }
      );
    }

    // Call PGSA deletion API
    try {
      await sendPgsaDeletion(blobReference);
    } catch (pgsaError) {
      console.error("PGSA deletion failed:", pgsaError);
      // Note: Blob and PAS are already deleted, but PGSA deletion failed
      return NextResponse.json(
        {
          error:
            pgsaError instanceof Error
              ? pgsaError.message
              : "PGSA-sletting feilet",
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Fil ble slettet",
    });
  } catch (error) {
    console.error("Error deleting exam material:", error);
    return NextResponse.json(
      { error: "Server error under sletting" },
      { status: 500 }
    );
  }
}
