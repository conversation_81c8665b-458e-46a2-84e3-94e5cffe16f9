import "reflect-metadata";
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from "typeorm";

@Entity("Eksamensmateriell")
export class Eksamensmateriell {
  @PrimaryGeneratedColumn({ type: "int" })
  EksamensmateriellID!: number;

  @Column({ type: "varchar", length: 255, nullable: false })
  BlobReferanseEksamensmateriell!: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  Eksamensmateriellkategori?: string;

  @Column({ type: "varchar", length: 10, nullable: true })
  EksamensmateriellMalform?: string;

  @Column({ type: "bigint", nullable: true })
  FileSize?: number;

  @Column({ type: "varchar", length: 100, nullable: true })
  MimeType?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  Filnavn?: string;

  @Column({ type: "bit", nullable: true })
  Opphavsrett?: boolean;

  @Column({ type: "bit", nullable: true })
  Vedlegg?: boolean;

  @CreateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  CreatedDate!: Date;

  @UpdateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  ModifiedDate!: Date;
}