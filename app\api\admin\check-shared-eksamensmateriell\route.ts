import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../../auth/authOptions";
import { getDbConnection } from "@/db/connection";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const userRoles = Array.isArray(session.user.role)
      ? session.user.role
      : [session.user.role];
    
    const hasAdminRole = userRoles.some(role => 
      role === "urn:udir:pgsa:administrator"
    );

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const blobReference = searchParams.get('blobReference');

    if (!blobReference) {
      return NextResponse.json(
        { error: "Missing blobReference parameter" },
        { status: 400 }
      );
    }

    const dataSource = await getDbConnection();

    // Check how many eksamensdeler are associated with this blob reference
    const result = await dataSource.query(
      `EXEC [dbo].[CheckSharedEksamensmateriell] @DocumentGuid = @0`,
      [blobReference]
    );

    const eksamensdeler = result.map((row: any) => row.EksamensdelType);
    const isShared = eksamensdeler.length > 1;

    return NextResponse.json({
      isShared,
      eksamensdeler,
      count: eksamensdeler.length
    });

  } catch (error) {
    console.error("Error checking shared eksamensmateriell:", error);
    
    return NextResponse.json(
      { error: "Intern serverfeil" },
      { status: 500 }
    );
  }
}