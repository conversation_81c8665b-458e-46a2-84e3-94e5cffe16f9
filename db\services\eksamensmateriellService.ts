import { DataSource, Repository, IsNull } from "typeorm";
import { getDbConnection } from "../connection";
import { Eksamensmateriell } from "../models/Eksamensmateriell";
import { EksamensdelEksamensmateriell } from "../models/EksamensdelEksamensmateriell";
import { Eksamensdel } from "../models/Eksamensdel";
import { Fagkodeeksamen } from "../models/Fagkodeeksamen";

export interface IEksamensmateriellData {
  eksamensmateriellID?: number; // Optional since it's auto-generated
  blobReferanse: string;
  eksamensmateriellkategori: string;
  eksamensmateriellMalform: string;
  fileSize: number;
  mimeType: string;
  originalFilename: string;
  // Additional fields for creating EksamensdelEksamensmateriell relationship
  fagkode: string;
  variant?: string;
  eksamensperiode: string;
  eksamensdel: string;
  // New checkbox fields for external verification
  opphavsrett?: boolean;
  vedlegg?: boolean;
}

export class EksamensmateriellService {
  private static readonly EKSAMENSDEL_MAPPING = {
    Eksamen: "Eksamen",
    Forberedelse: "Forberedelse",
    "Eksamen del 1": "EksamenDel1",
    "Eksamen del 2": "EksamenDel2",
    "Eksamen del 1 og del 2": "Del1og2",
  } as const;

  private static readonly REVERSE_EKSAMENSDEL_MAPPING = Object.fromEntries(
    Object.entries(this.EKSAMENSDEL_MAPPING).map(([key, value]) => [value, key])
  );

  /**
   * Maps frontend eksamensdel names to API format
   */
  private static mapEksamensdelToApiFormat(eksamensdel: string): string {
    return (
      this.EKSAMENSDEL_MAPPING[
        eksamensdel as keyof typeof this.EKSAMENSDEL_MAPPING
      ] || eksamensdel
    );
  }

  /**
   * Maps API format eksamensdel names back to user-friendly format
   */
  private static reverseMapEksamensdelFromApiFormat(
    apiEksamensdel: string
  ): string {
    return this.REVERSE_EKSAMENSDEL_MAPPING[apiEksamensdel] || apiEksamensdel;
  }

  /**
   * Maps Opplaeringsniva to TestType for PGSA API
   */
  public static mapOpplaeringsniveToTestType(opplaeringsniva?: string): string {
    switch (opplaeringsniva) {
      case "VGS":
        return "EV"; // Videregående
      case "GS":
        return "EG"; // Grunnskole
      case "FOV":
        return "FO"; // Forsøk/Ordning for vurdering
      default:
        return "EG"; // Default to grunnskole if unknown
    }
  }

  /**
   * Get exam metadata (Opplaeringsniva and Fagnavn) for PGSA API
   */
  public static async getExamMetadata(
    fagkode: string,
    variant: string | undefined,
    eksamensperiode: string
  ): Promise<{ opplaeringsniva?: string; fagnavn?: string }> {
    try {
      const dataSource = await getDbConnection();
      const fagkodeeksamensRepository = dataSource.getRepository(Fagkodeeksamen);

      const fagkodeeksamen = await fagkodeeksamensRepository.findOne({
        where: {
          Fagkode: fagkode,
          Variantkode: variant ? variant : IsNull(),
          Eksamensperiode: eksamensperiode,
        },
        select: ["Opplaeringsniva", "Fagnavn"],
      });

      return {
        opplaeringsniva: fagkodeeksamen?.Opplaeringsniva || undefined,
        fagnavn: fagkodeeksamen?.Fagnavn || undefined,
      };
    } catch (error) {
      console.error("Failed to get exam metadata:", error);
      return {};
    }
  }

  /**
   * Get Opplaeringsniva for a fagkode to determine TestType for PGSA
   * @deprecated Use getExamMetadata instead
   */
  public static async getOpplaeringsniva(
    fagkode: string,
    variant: string | undefined,
    eksamensperiode: string
  ): Promise<string | undefined> {
    const metadata = await this.getExamMetadata(fagkode, variant, eksamensperiode);
    return metadata.opplaeringsniva;
  }

  /**
   * Extract error message from database error
   */
  private static extractErrorMessage(error: any): string {
    const errorMessage = error?.message || "";
    const originalError =
      error?.originalError?.info?.message ||
      error?.originalError?.message ||
      "";
    return errorMessage + " " + originalError;
  }

  /**
   * Execute stored procedure for exam material
   */
  private static async executeProcessEksamensmateriell(
    dataSource: DataSource,
    data: IEksamensmateriellData,
    fagkodeRaw: string,
    eksamensdel: string
  ): Promise<void> {
    await dataSource.query(
      `
      EXEC [dbo].[ProcessEksamensmateriell]
        @DocumentGuid = @0,
        @FileSize = @1,
        @MimeType = @2,
        @OriginalFilnavn = @3,
        @Eksamensmateriellkategori = @4,
        @EksamensmateriellMalform = @5,
        @FagkodeRaw = @6,
        @Eksamensperiode = @7,
        @EksamensdelType = @8,
        @Opphavsrett = @9,
        @Vedlegg = @10
    `,
      [
        data.blobReferanse,
        data.fileSize,
        data.mimeType,
        data.originalFilename,
        data.eksamensmateriellkategori,
        data.eksamensmateriellMalform,
        fagkodeRaw,
        data.eksamensperiode,
        eksamensdel,
        data.opphavsrett ?? false,
        data.vedlegg ?? false,
      ]
    );
  }

  /**
   * Save exam material and metadata to database using stored procedure
   */
  public static async saveEksamensmateriell(
    data: IEksamensmateriellData
  ): Promise<void> {
    const dataSource = await getDbConnection();

    // Prepare parameters for stored procedure
    const fagkodeRaw = data.variant
      ? `${data.fagkode}-${data.variant}`
      : data.fagkode;
    const mappedEksamensdel =
      EksamensmateriellService.mapEksamensdelToApiFormat(data.eksamensdel);

    try {
      await EksamensmateriellService.executeProcessEksamensmateriell(
        dataSource,
        data,
        fagkodeRaw,
        mappedEksamensdel
      );
    } catch (error: any) {
      const fullErrorMessage =
        EksamensmateriellService.extractErrorMessage(error);
      const isEksamensdelNotFound = fullErrorMessage.includes(
        "EksamensdelID ikke funnet"
      );


      if (isEksamensdelNotFound) {
        const variantText = data.variant ? ` (variant: ${data.variant})` : "";
        const eksamendelText =
          EksamensmateriellService.reverseMapEksamensdelFromApiFormat(
            mappedEksamensdel
          );
        throw new Error(
          `Eksamensdelen "${eksamendelText}" finnes ikke for fagkode "${data.fagkode}"${variantText} i periode ${data.eksamensperiode}. Du må først importere eksamensplan for denne fagkoden.`
        );
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Get exam material by blob reference
   */
  public static async getEksamensmateriellByBlobReference(
    blobReference: string
  ): Promise<Eksamensmateriell | null> {
    try {
      const dataSource = await getDbConnection();
      const eksamensmateriellRepository =
        dataSource.getRepository(Eksamensmateriell);

      return await eksamensmateriellRepository.findOne({
        where: { BlobReferanseEksamensmateriell: blobReference },
      });
    } catch (error) {
      console.error("Failed to get eksamensmateriell:", error);
      throw error;
    }
  }

  /**
   * Get existing exam materials by fagkode and eksamensperiode
   */
  public static async getExistingEksamensmateriell(
    fagkode: string,
    eksamensperiode: string,
    variant?: string
  ): Promise<
    {
      eksamensmateriell: Eksamensmateriell;
      eksamensdel: string;
    }[]
  > {
    try {
      const dataSource = await getDbConnection();

      // First find the Fagkodeeksamen
      const fagkodeeksamensRepository =
        dataSource.getRepository(Fagkodeeksamen);

      const fagkodeeksamen = await fagkodeeksamensRepository.findOne({
        where: {
          Fagkode: fagkode,
          Variantkode: variant ? variant : IsNull(),
          Eksamensperiode: eksamensperiode,
        },
      });

      if (!fagkodeeksamen) {
        // Return empty array if fagkodeeksamen doesn't exist
        return [];
      }

      const eksamensdelEksamensmateriellRepository = dataSource.getRepository(
        EksamensdelEksamensmateriell
      );

      // Query to get all exam materials for the given fagkodeeksamen
      const query = eksamensdelEksamensmateriellRepository
        .createQueryBuilder("rel")
        .innerJoin(
          Eksamensmateriell,
          "em",
          "rel.EksamensmateriellID = em.EksamensmateriellID"
        )
        .innerJoin("Eksamensdel", "ed", "rel.EksamensdelID = ed.EksamensdelID")
        .where("ed.FagkodeeksamensID = :fagkodeeksamensId", {
          fagkodeeksamensId: fagkodeeksamen.FagkodeeksamensID,
        })
        .select([
          "rel.EksamensdelID",
          "rel.EksamensmateriellID",
          "em.EksamensmateriellID",
          "em.BlobReferanseEksamensmateriell",
          "em.Eksamensmateriellkategori",
          "em.EksamensmateriellMalform",
          "em.FileSize",
          "em.MimeType",
          "em.Filnavn",
          "em.Opphavsrett",
          "em.Vedlegg",
          "ed.EksamensdelType",
        ]);

      const results = await query.getRawMany();

      return results.map((row) => ({
        eksamensmateriell: Object.assign(new Eksamensmateriell(), {
          EksamensmateriellID: parseInt(row.em_EksamensmateriellID) || 0,
          BlobReferanseEksamensmateriell:
            row.em_BlobReferanseEksamensmateriell || "",
          Eksamensmateriellkategori: row.em_Eksamensmateriellkategori || "",
          EksamensmateriellMalform: row.em_EksamensmateriellMalform || "",
          FileSize: parseInt(row.em_FileSize) || 0,
          MimeType: row.em_MimeType || "",
          Filnavn: row.em_Filnavn || "",
          Opphavsrett: Boolean(row.em_Opphavsrett),
          Vedlegg: Boolean(row.em_Vedlegg),
        }),
        eksamensdel:
          EksamensmateriellService.reverseMapEksamensdelFromApiFormat(
            row.ed_EksamensdelType || ""
          ),
      }));
    } catch (error) {
      console.error("Failed to get existing eksamensmateriell:", error);
      throw error;
    }
  }

  /**
   * Replace existing exam material with new data by blob reference
   */
  public static async replaceEksamensmateriell(
    oldBlobReference: string,
    data: IEksamensmateriellData,
    searchFormData?: { fagkode: string; eksamensperiode: string; variant?: string }
  ): Promise<void> {
    const dataSource = await getDbConnection();

    // Use fagkode and variant from searchFormData if data doesn't contain them
    const fagkode = data.fagkode || searchFormData?.fagkode;
    const variant = data.variant || searchFormData?.variant;
    const eksamensperiode = data.eksamensperiode || searchFormData?.eksamensperiode;

    // Validate required parameters
    if (!fagkode || !eksamensperiode) {
      throw new Error("Fagkode og eksamensperiode er påkrevd for erstatning av eksamensmateriell");
    }

    // Prepare parameters for stored procedure
    const fagkodeRaw = variant ? `${fagkode}-${variant}` : fagkode;
    const mappedEksamensdel = EksamensmateriellService.mapEksamensdelToApiFormat(data.eksamensdel);

    // Update data object with missing values for stored procedure
    const updatedData = {
      ...data,
      fagkode: fagkode,
      variant: variant,
      eksamensperiode: eksamensperiode
    };

    try {
      // Use the stored procedure which handles both update and insert via MERGE
      // This preserves the existing EksamensmateriellID when updating
      await EksamensmateriellService.executeProcessEksamensmateriell(
        dataSource,
        data,
        fagkodeRaw,
        mappedEksamensdel
      );
    } catch (error: any) {
      const fullErrorMessage = EksamensmateriellService.extractErrorMessage(error);
      const isEksamensdelNotFound = fullErrorMessage.includes(
        "EksamensdelID ikke funnet"
      );

      if (isEksamensdelNotFound) {
        const variantText = data.variant ? ` (variant: ${data.variant})` : "";
        const eksamendelText =
          EksamensmateriellService.reverseMapEksamensdelFromApiFormat(
            mappedEksamensdel
          );
        throw new Error(
          `Eksamensdelen "${eksamendelText}" finnes ikke for fagkode "${data.fagkode}"${variantText} i periode ${data.eksamensperiode}. Du må først importere eksamensplan for denne fagkoden.`
        );
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Delete exam material by blob reference using stored procedure
   */
  public static async deleteEksamensmateriellByBlobReference(
    blobReference: string
  ): Promise<void> {
    const dataSource = await getDbConnection();

    try {
      await dataSource.query(
        `EXEC [dbo].[DeleteEksamensmateriell] @DocumentGuid = @0`,
        [blobReference]
      );
    } catch (error: any) {
      const fullErrorMessage =
        EksamensmateriellService.extractErrorMessage(error);
      console.error("Failed to delete eksamensmateriell:", fullErrorMessage);
      throw error;
    }
  }
}

export default EksamensmateriellService;
