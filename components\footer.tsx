"use client";

import Image from "next/image";
import Link from "next/link";
import logo from "../public/images/udirLogo.png";
import { Button } from "./ui/button";
import { FaExternalLinkAlt } from "react-icons/fa";
import { usePathname } from "next/navigation";
import { useState } from "react";
import Tilbakemelding from "@/app/[locale]/tilbakemelding/tilbakemelding";
import React from "react";

function Footer() {
  const pathname = usePathname();
  const [showFeedback, setShowFeedback] = useState(false);

  const hideFooterOnPages = ["/tilbakemelding"];

  const shouldHideFooter = hideFooterOnPages.includes(pathname);
  const tilgjengelighetserklaringUrl =
    process.env.NEXT_PUBLIC_TILGJENGELIGHETSERKLARING_URL;

  return (
    <>
      {!shouldHideFooter && (
        <div className="bg-stalbla p-8 ">
          {!showFeedback ? (
            <div className=" flex justify-center gap-4">
              <span className="mt-2">Savner du noe på denne siden? </span>
              <Button
                variant="outline"
                aria-label="Gi tilbakemelding"
                onClick={() => setShowFeedback(true)}
                className="text-black"
              >
                Gi tilbakemelding
              </Button>
            </div>
          ) : (
            <div className="flex flex-col justify-center items-center px-20 gap-6 py-4">
              <Tilbakemelding setState={setShowFeedback} pathname={pathname} />
            </div>
          )}
        </div>
      )}

      <footer className="bg-udirSteelBlue-700 text-white">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            <div className="w-full md:w-auto flex justify-center md:justify-start">
              <Image
                src={logo}
                alt="Utdanningsdirektoratets logo"
                width={127}
                height={36}
                className="mt-3"
              />
            </div>
            <div className="flex flex-col items-center gap-3 text-center justify-center">
              <p>PGS er levert av Utdanningsdirektoratet</p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Link href="/omPGS" className="underline hover:text-orange">
                  Om tjenesten
                </Link>
                <a
                  href="https://www.udir.no/om-udir/kontakt-oss/"
                  className="underline hover:text-orange"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Kontakt
                </a>
                <a
                  href="https://www.udir.no/om-udir/personvernerklaring-udir/#a151532"
                  className="underline hover:text-orange"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Personvernerklæring
                </a>
                {tilgjengelighetserklaringUrl && (
                  <a
                    href={tilgjengelighetserklaringUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline"
                  >
                    Tilgjengelighetserklæring
                  </a>
                )}
                 <a
                  href="/informasjonskapsler"
                  className="underline hover:text-orange"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Informasjonskapsler
                </a>
              </div>
            </div>

            <div className="w-full md:w-auto flex justify-center md:justify-end">
              <Button
                variant="secondary"
                className="mt-3 flex gap-2 text-black"
                onClick={() => window.open("https://www.udir.no", "_blank")}
              >
                Udir.no{" "}
                <FaExternalLinkAlt role="img" aria-label="ekstern link ikon" />
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
}

export default Footer;
