import { useMemo } from "react";
import useS<PERSON> from "swr";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { getCurrentTermCode } from "@/lib/shared/getCurrentTermCode";
import { POLL_INTERVAL, ERROR_RETRY_COUNT, fetcher, getFetcher } from "./candidateMonitorUtils";

interface UseCandidateDataProps {
  selectedSchoolId?: string;
  examPaper: IExamPaperInternal[];
  refreshTrigger: number;
  onExamDataSuccess: (data: IExamPaperInternal[]) => void;
  onExamDataError: (error: Error) => void;
  onRedisDataSuccess: (data: IRedisObject[]) => void;
  onRedisDataError: (error: Error) => void;
  onBlockedUsersSuccess: (data: { users: string[] }) => void;
  onBlockedUsersError: (error: Error) => void;
}

export const useCandidateData = ({
  selectedSchoolId,
  examPaper,
  refreshTrigger,
  onExamDataSuccess,
  onExamDataError,
  onRedisDataSuccess,
  onRedisDataError,
  onBlockedUsersSuccess,
  onBlockedUsersError,
}: UseCandidateDataProps) => {
  // Exam data fetching
  const { isLoading: isExamDataLoading } = useSWR(
    selectedSchoolId ? ["/api/getCandidatesMonitor", selectedSchoolId] : null,
    ([url, schoolId]) =>
      fetcher(url, {
        schoolId,
        period: getCurrentTermCode(),
      }),
    {
      refreshInterval: POLL_INTERVAL,
      errorRetryCount: ERROR_RETRY_COUNT,
      revalidateOnFocus: false,
      onSuccess: onExamDataSuccess,
      onError: onExamDataError,
    }
  );

  // Stable key generators for data filtering
  const accessFilterKey = useMemo(() => {
    return {
      type: "sessionData",
      candidates: examPaper
        .flatMap((p) => p.candidates.map((c) => c.candidateNumber))
        .sort()
        .join(","),
      role: selectedSchoolId,
      refresh: refreshTrigger,
    };
  }, [examPaper, selectedSchoolId, refreshTrigger]);

  // Memoized fetcher for the redisResultData SWR hook
  const redisSWRFetcher = useMemo(
    () =>
      (key: {
        type: string;
        candidates: string;
        role?: string;
        refresh: number;
      }) => {
        if (!key.role) return Promise.resolve([]);

        const candidatesList = key.candidates.split(",").filter(Boolean);
        if (candidatesList.length === 0) return Promise.resolve([]);

        return fetcher("/api/getCandidateSessions", candidatesList);
      },
    []
  );

  // Redis data fetching
  const { isLoading: isRedisLoading } = useSWR(
    accessFilterKey,
    redisSWRFetcher,
    {
      refreshInterval: POLL_INTERVAL,
      errorRetryCount: ERROR_RETRY_COUNT,
      onSuccess: onRedisDataSuccess,
      onError: onRedisDataError,
    }
  );

  // Blocked users data
  const blockedUsersKey = useMemo(() => {
    if (!examPaper.length || !selectedSchoolId) {
      return null;
    }
    return [`/api/blockedUsers/${selectedSchoolId}`];
  }, [examPaper.length, selectedSchoolId]);

  const { isLoading: isBlockedUsersLoading } = useSWR(
    blockedUsersKey,
    ([url]) => getFetcher(url),
    {
      refreshInterval: POLL_INTERVAL,
      errorRetryCount: ERROR_RETRY_COUNT,
      revalidateOnFocus: false,
      onSuccess: onBlockedUsersSuccess,
      onError: onBlockedUsersError,
    }
  );

  // Combined loading state
  const isLoading = useMemo(() => {
    return (
      isExamDataLoading ||
      (accessFilterKey.candidates && isRedisLoading) ||
      (blockedUsersKey !== null && isBlockedUsersLoading)
    );
  }, [
    isExamDataLoading,
    accessFilterKey.candidates,
    blockedUsersKey,
    isRedisLoading,
    isBlockedUsersLoading,
  ]);

  return {
    isLoading,
    isExamDataLoading,
    isRedisLoading,
    isBlockedUsersLoading,
  };
};
