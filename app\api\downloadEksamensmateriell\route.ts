import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { Readable } from "stream";
import { getPGSBlobServiceClient } from "@/lib/server/blobHelper";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const blobName = new URL(request.url).searchParams.get("blobName");
    const filename = new URL(request.url).searchParams.get("filename");
    
    if (!blobName) {
      return NextResponse.json(
        { error: "Missing blobName parameter" },
        { status: 400 }
      );
    }

    const blobServiceClient = getPGSBlobServiceClient();
    const containerClient = blobServiceClient.getContainerClient("oppgaver");
    const blobClient = containerClient.getBlobClient(blobName);

    // Check if the blob exists and get properties in one call
    const properties = await blobClient.getProperties();
    if (properties.contentLength === 0) {
      return NextResponse.json({ error: "File is empty" }, { status: 404 });
    }

    // Download the blob
    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      throw new Error("Unable to read file");
    }

    // Convert ReadableStream to a Web-standard ReadableStream
    const stream = Readable.from(downloadResponse.readableStreamBody);

    // Normalize and encode filename for cross-platform compatibility
    const normalizedFilename = (filename || blobName).normalize('NFC');
    const encodedFilename = encodeURIComponent(normalizedFilename);

    const headers = new Headers({
      "Content-Type": properties.contentType || "application/octet-stream",
      "Content-Disposition": `attachment; filename="${normalizedFilename}"; filename*=UTF-8''${encodedFilename}`,
      "Content-Length": properties.contentLength?.toString() || "0",
    });

    // Use Response constructor for streaming
    return new Response(stream as unknown as ReadableStream, { headers });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "downloadEksamensmateriell",
        response: error instanceof Error ? error.message : "Unknown error",
      },
    });
    console.error("Error downloading eksamensmateriell:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}