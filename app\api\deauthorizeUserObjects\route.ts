// app/api/deauthorize/route.ts
import { ISession } from "@/interface/ISession";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import { deauthorizeUserObjectRedis } from "@/lib/server/deauthorizeUserObjectRedis";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { SignalRMessageEnum } from "@/enums/SignalRMessageEnum";
import { buildSignalRUrl, sendSignalRMessage } from "@/lib/server/signalRHelpers";
import { IAuthorizationRevokedPayload } from "@/interface/IAuthorizationRevokedPayload";

const telemetryClient = getAppInsightsServer();

/**
 * POST /api/deauthorize
 * Deautoriserer en kandidat ved å sette isAuthorized til false i Redis
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Validate request
    const { candidateNumber, excludeSessionId } = await request.json();

    if (!candidateNumber || typeof candidateNumber !== "string") {
      return NextResponse.json(
        { error: "Manglende eller ugyldig kandidatnummer" },
        { status: 400 }
      );
    }

    // Perform deauthorization and get list of deauthorized sessions
    const deauthorizedSessions = await deauthorizeUserObjectRedis(candidateNumber, excludeSessionId);

    try {
      // Attempt to send SignalR message to the candidate with session information
      const url = buildSignalRUrl(`users/${candidateNumber}`);
      const payload: IAuthorizationRevokedPayload = {
        candidateNumber,
        deauthorizedSessions,
      };
      const message = {
        target: SignalRMessageEnum.AuthorizationRevoked,
        arguments: [payload],
      };
      await sendSignalRMessage(url, message);
    } catch (signalRError) {
      // Log SignalR error but continue flow
      telemetryClient?.trackException({
        exception: signalRError as Error,
        properties: {
          operation: "SendSignalRMessage",
          candidateNumber,
          errorMessage: (signalRError as Error).message,
        },
      });
      console.error("Failed to send SignalR message:", signalRError);
    }

    return NextResponse.json(
      { message: "Bruker deautorisert" },
      { status: 200 }
    );
  } catch (error) {
    // Logg feilen
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        operation: "DeauthorizeUser",
        errorMessage: (error as Error).message,
      },
    });

    // Returner feilmelding
    return NextResponse.json(
      { error: "Feil ved deautorisering av bruker" },
      { status: 500 }
    );
  }
}
