import { memo } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Loader2,
  Trash2,
  RotateCcw,
} from "lucide-react";
import { formatFileSize, type ExistingFile } from "./shared/fileManagementUtils";

interface FileCardProps {
  file: ExistingFile;
  onReplace: (file: ExistingFile) => void;
  onDelete: (file: ExistingFile) => void;
  isDeleting: boolean;
}

export const FileCard = memo(function FileCard({
  file,
  onReplace,
  onDelete,
  isDeleting,
}: FileCardProps) {
  return (
    <div className="group relative bg-stone-50 rounded-xl border border-gray-200 p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:border-gray-300 flex flex-col">
      {/* File Icon and Header */}
      <div className="flex items-start gap-3 mb-3">
        <div className="p-2 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors">
          <FileText className="w-5 h-5 text-blue-600" />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 mb-1 break-words leading-tight">
            {file.filename}
          </h3>
          <p className="text-xs text-gray-500">
            {formatFileSize(file.fileSize)}
          </p>
        </div>
      </div>

      {/* Metadata Badges */}
      <div className="flex flex-wrap gap-2 mb-4 h-16 content-start">
        <Badge
          variant="secondary"
          className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200"
        >
          {file.eksamensdel}
        </Badge>
        <Badge
          variant="outline"
          className="text-xs border-gray-300 text-gray-600"
        >
          {file.kategori}
        </Badge>
        <Badge
          variant="outline"
          className="text-xs border-gray-300 text-gray-600"
        >
          {file.malform}
        </Badge>
        {file.opphavsrett && (
          <Badge
            variant="default"
            className="text-xs bg-blue-100 text-blue-800 border-blue-200"
          >
            Opphavsrett
          </Badge>
        )}
        {file.vedlegg && (
          <Badge
            variant="default"
            className="text-xs bg-green-100 text-green-800 border-green-200"
          >
            Vedlegg
          </Badge>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 mt-auto">
        <Button
          onClick={() => onReplace(file)}
          size="sm"
          variant="outline"
          disabled={isDeleting}
          className="flex-1 flex items-center justify-center gap-2 hover:bg-blue-50 transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          Erstatt
        </Button>
        <Button
          onClick={() => onDelete(file)}
          size="sm"
          variant="outline"
          disabled={isDeleting}
          className="flex-1 flex items-center justify-center gap-2 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 hover:text-red-700 transition-colors"
        >
          {isDeleting ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Kansellerer...
            </>
          ) : (
            <>
              <Trash2 className="w-4 h-4" />
              Kanseller
            </>
          )}
        </Button>
      </div>
    </div>
  );
});