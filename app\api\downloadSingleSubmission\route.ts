import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { Readable } from "stream";
import { getPGSXContainerClient } from "@/lib/server/blobHelper";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const documentCode = new URL(request.url).searchParams.get("documentCode");
    if (!documentCode) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    const containerClient = getPGSXContainerClient();
    const blobClient = containerClient.getBlobClient(documentCode);

    // Check if the blob exists and get properties in one call
    const properties = await blobClient.getProperties();
    if (properties.contentLength === 0) {
      return NextResponse.json({ error: "File is empty" }, { status: 404 });
    }

    // Download the blob
    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      throw new Error("Unable to read file");
    }

    // Convert ReadableStream to a Web-standard ReadableStream
    const stream = Readable.from(downloadResponse.readableStreamBody);

    const headers = new Headers({
      "Content-Type": properties.contentType || "application/octet-stream",
      "Content-Disposition": "attachment",
      "Content-Length": properties.contentLength?.toString() || "0",
    });

    // Use Response constructor for streaming
    return new Response(stream as unknown as ReadableStream, { headers });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "downloadGroupSubmissions",
        response: error instanceof Error ? error.message : "Unknown error",
      },
    });
    console.error("Error downloading file:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
