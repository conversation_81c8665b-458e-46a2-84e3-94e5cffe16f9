import { NextRequest, NextResponse } from "next/server";
import { BlobServiceClient, ContainerClient } from "@azure/storage-blob";
import archiver from "archiver";
import { PassThrough, Readable } from "stream";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";
import { getPGSXContainerClient } from "@/lib/server/blobHelper";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

// Validation function
function validateExamPaper(data: any): { isValid: boolean; error?: string } {
  const requiredStringFields = [
    "groupName",
    "subjectCode",
    "subjectName",
    "testPeriod",
  ];

  for (const field of requiredStringFields) {
    if (typeof data[field] !== "string") {
      return { isValid: false, error: `Invalid ${field}` };
    }
  }

  if (!Array.isArray(data.candidates)) {
    return { isValid: false, error: "Candidates must be an array" };
  }

  for (const candidate of data.candidates) {
    if (typeof candidate.candidateNumber !== "string") {
      return { isValid: false, error: "Invalid candidateNumber" };
    }
    if (!Array.isArray(candidate.documents)) {
      return { isValid: false, error: "Documents must be an array" };
    }
    for (const doc of candidate.documents) {
      const docFields = ["documentCode", "fileName", "timestamp"];
      for (const field of docFields) {
        if (typeof doc[field] !== "string") {
          return { isValid: false, error: `Invalid ${field}` };
        }
      }
    }
  }
  return { isValid: true };
}

// Main handler function
export async function POST(req: NextRequest) {
  const data: IExamPaperInternal = await req.json();

  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const validationResult = validateExamPaper(data);

    if (!validationResult.isValid) {
      console.error("Validation error:", validationResult.error);
      return NextResponse.json(
        { error: "Invalid input data", details: validationResult.error },
        { status: 400 }
      );
    }

    const { archive, archiveStream } = setupArchive();
    const missingFiles: string[] = [];
    const processStream = new PassThrough();
    let filesAdded = false;

    // Start processing in the background
    const processingPromise = processExamPapers(data, archive, missingFiles)
      .then(() => {
        if (!filesAdded) {
          throw new Error("NoFilesFound");
        }
        archive.finalize();
      })
      .catch((error) => {
        console.error("Error processing exam papers:", error);
        processStream.emit("error", error);
      });

    // Pipe archive to process stream
    archiveStream.pipe(processStream);

    // Convert PassThrough to ReadableStream
    const readableStream = new ReadableStream({
      start(controller) {
        processStream.on("data", (chunk) => {
          filesAdded = true;
          controller.enqueue(chunk);
        });
        processStream.on("end", () => controller.close());
        processStream.on("error", (error) => controller.error(error));
      },
    });

    // Wait for processing to complete
    await processingPromise;

    if (!filesAdded) {
      return NextResponse.json(
        { error: "No files found", missingFiles: missingFiles },
        { status: 404 }
      );
    }

    // Send headers
    const zipFileName = getZipFileName(data);

    const headers = new Headers();
    headers.set("Content-Type", "application/octet-stream");
    headers.set("Content-Disposition", `attachment; filename="${zipFileName}"`);
    headers.set("Transfer-Encoding", "chunked");
    headers.set("X-Missing-Files", JSON.stringify(missingFiles));

    return new NextResponse(readableStream, { headers });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "downloadGroupSubmissions",
        data,
        response: error instanceof Error ? error.message : "Unknown error"
      },
    });

    console.error("Error:", error);

    if (error instanceof Error && error.message === "NoFilesFound") {
      return NextResponse.json({ error: "No files found" }, { status: 404 });
    }
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

function getZipFileName(data: IExamPaperInternal): string {
  return encodeURIComponent(
    `${data.groupName}_${data.subjectCode}_${data.subjectName}_${data.testPeriod}.zip`
  );
}

// Setup functions
function setupArchive() {
  const archive = archiver("zip", { zlib: { level: 9 } });
  const archiveStream = new PassThrough();

  archive.on("warning", (err) => {
    if (err.code !== "ENOENT") console.warn("Archive warning:", err);
  });

  archive.on("error", (err) => {
    console.error("Archive error:", err);
    throw err;
  });

  archive.pipe(archiveStream);

  return { archive, archiveStream };
}

// Processing functions
async function processExamPapers(
  data: IExamPaperInternal,
  archive: archiver.Archiver,
  missingFiles: string[]
): Promise<void> {
  const containerClient = getPGSXContainerClient();

  for (const examPaper of data.candidates) {
    await processExamPaper(
      examPaper,
      data,
      containerClient,
      archive,
      missingFiles
    );
  }
}

async function processExamPaper(
  examPaper: { candidateNumber: string; documents: any[] },
  data: IExamPaperInternal,
  containerClient: ContainerClient,
  archive: archiver.Archiver,
  missingFiles: string[]
): Promise<void> {
  const { candidateNumber, documents } = examPaper;

  for (const doc of documents) {
    await processDocument(
      doc,
      candidateNumber,
      data,
      containerClient,
      archive,
      missingFiles
    );
  }
}

async function processDocument(
  doc: { documentCode: string; fileName: string; timestamp: string },
  candidateNumber: string,
  data: IExamPaperInternal,
  containerClient: ContainerClient,
  archive: archiver.Archiver,
  missingFiles: string[]
): Promise<void> {
  const blobClient = containerClient.getBlobClient(doc.documentCode);

  try {
    // Sjekk om blob-en eksisterer først
    const exists = await blobClient.exists();

    if (!exists) {
      addToMissingFiles(candidateNumber, doc, missingFiles);
      return;
    }

    // Hvis blob-en eksisterer, fortsett med nedlasting og arkivering
    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      throw new Error(`Unable to download ${doc.documentCode}`);
    }

    const nodeReadableStream = Readable.from(
      downloadResponse.readableStreamBody
    );
    archive.append(nodeReadableStream, {
      name: `${candidateNumber}/${doc.fileName}`,
    });
  } catch (error) {
    console.error(`Error processing file ${doc.documentCode}:`, error);
    throw error; // Re-throw unexpected errors
  }
}

function addToMissingFiles(
  candidateNumber: string,
  doc: { fileName: string },
  missingFiles: string[]
): void {
  missingFiles.push(`${candidateNumber}/${doc.fileName}`);
}
