import "reflect-metadata";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  PrimaryColumn,
  OneToMany,
} from "typeorm";

@Entity("Besvarelsesdel")
export class Besvarelsesdel {
  @PrimaryColumn({ type: "varchar", length: 255 })
  BesvarelsesdelID!: string;

  @Column({ type: "int", nullable: false })
  KandidateksamenID!: number;

  @Column({ type: "varchar", length: 50, nullable: true })
  BesvarelsesdelType?: string;

  @Column({ type: "varchar", length: 50, nullable: true })
  BesvarelsesdelStatus?: string;

  @Column({ type: "varchar", length: 50, nullable: true })
  Gjennomforingmodus?: string;

  @CreateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  CreatedDate!: Date;

  @UpdateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  ModifiedDate!: Date;

  // Lazy relationship to Kandidateksamen to avoid circular dependency
  @ManyToOne("Kandidateksamen", (kandidateksamen: any) => kandidateksamen.besvarelsesdeler)
  @JoinColumn({ name: "KandidateksamenID" })
  kandidateksamen?: any;

  // Lazy relationship to BesvarelsesDelBesvarelsesfil to avoid circular dependency
  @OneToMany(
    "BesvarelsesDelBesvarelsesfil",
    (junction: any) => junction.besvarelsesdel
  )
  besvarelsesDelBesvarelsesfiler?: any[];
}
