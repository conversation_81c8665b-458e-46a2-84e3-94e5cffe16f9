import { NextRequest, NextResponse } from 'next/server';
import { getPGSBlobServiceClient } from '@/lib/server/blobHelper';
import { IFagkodeShared } from '@/interface/IFagkodeShared';

interface MissingFile {
  fagkode: string;
  variantkode?: string;
  fagnavn: string;
  eksamensdel: string;
  filename: string;
  blobName: string;
}

export async function POST(request: NextRequest) {
  try {
    const { blobNames, fagkodeData }: {
      blobNames: string[],
      fagkodeData: IFagkodeShared[]
    } = await request.json();

    if (!blobNames || !Array.isArray(blobNames) || blobNames.length === 0) {
      return NextResponse.json({ error: 'Invalid blob names' }, { status: 400 });
    }

    if (!fagkodeData || !Array.isArray(fagkodeData)) {
      return NextResponse.json({ error: 'Invalid fagkode data' }, { status: 400 });
    }


    // Use the same container as exam material upload ("oppgaver")
    const blobServiceClient = getPGSBlobServiceClient();
    const containerClient = blobServiceClient.getContainerClient("oppgaver");
    const existingBlobs = new Set<string>();

    // Verify container exists and is accessible
    try {
      const containerExists = await containerClient.exists();
      if (!containerExists) {
        throw new Error('Container "oppgaver" does not exist');
      }
    } catch (containerError) {
      console.error('Error accessing container:', containerError);
      throw new Error(`Failed to access container: ${containerError instanceof Error ? containerError.message : 'Unknown error'}`);
    }

    // Check blob existence using batched direct exists() calls (efficient for containers with hundreds of thousands of files)
    const uniqueBlobNames = [...new Set(blobNames)];
    const batchSize = 20;
    const batches = [];

    // Split into batches for parallel processing
    for (let i = 0; i < uniqueBlobNames.length; i += batchSize) {
      batches.push(uniqueBlobNames.slice(i, i + batchSize));
    }

    try {
      for (const batch of batches) {
        const existsPromises = batch.map(async (blobName) => {
          try {
            const blobClient = containerClient.getBlobClient(blobName);
            const exists = await blobClient.exists();
            return { blobName, exists };
          } catch (error) {
            // If blob doesn't exist or there's an error, consider it non-existent
            return { blobName, exists: false };
          }
        });

        const batchResults = await Promise.all(existsPromises);
        batchResults.forEach(({ blobName, exists }) => {
          if (exists) {
            existingBlobs.add(blobName);
          }
        });
      }
    } catch (blobError) {
      console.error('Error checking blob existence:', blobError);
      throw new Error(`Failed to check blob existence: ${blobError instanceof Error ? blobError.message : 'Unknown error'}`);
    }

    // Generate results and missing files report
    const results = blobNames.map(blobName => ({
      blobName,
      exists: existingBlobs.has(blobName)
    }));

    const finalMissingFilesReport: MissingFile[] = [];

    // Find missing blobs
    const missingBlobNames = blobNames.filter(name => !existingBlobs.has(name));

    // Create a mapping of blob names to ALL file instances (including different eksamensdeler)
    const blobToFileInstances = new Map<string, Array<{
      fagkode: string;
      variantkode?: string;
      fagnavn: string;
      eksamensdel: string;
      filename: string;
    }>>();

    fagkodeData.forEach(fagkode => {
      fagkode.eksamensdeler?.forEach(del => {
        del.eksamensmateriell?.forEach(mat => {
          if (mat.blobReferanseEksamensmateriell) {
            const fileInfo = {
              fagkode: fagkode.fagkode,
              variantkode: fagkode.variantkode,
              fagnavn: fagkode.fagnavn,
              eksamensdel: del.eksamensdelType || 'Ukjent',
              filename: mat.filnavn || 'Ukjent fil'
            };

            if (!blobToFileInstances.has(mat.blobReferanseEksamensmateriell)) {
              blobToFileInstances.set(mat.blobReferanseEksamensmateriell, []);
            }
            blobToFileInstances.get(mat.blobReferanseEksamensmateriell)!.push(fileInfo);
          }
        });
      });
    });

    // Generate report for missing blobs (each blob+eksamensdel combination)
    const uniqueMissingBlobNames = [...new Set(missingBlobNames)];

    uniqueMissingBlobNames.forEach(blobName => {
      const fileInstances = blobToFileInstances.get(blobName);
      if (fileInstances && fileInstances.length > 0) {
        // Add each eksamensdel instance as a separate missing file
        fileInstances.forEach(fileInfo => {
          finalMissingFilesReport.push({
            fagkode: fileInfo.fagkode,
            variantkode: fileInfo.variantkode,
            fagnavn: fileInfo.fagnavn,
            eksamensdel: fileInfo.eksamensdel,
            filename: fileInfo.filename,
            blobName: blobName
          });
        });
      } else {
        finalMissingFilesReport.push({
          fagkode: 'UKJENT',
          variantkode: undefined,
          fagnavn: 'Fil ikke funnet i fagkode-data',
          eksamensdel: 'Ukjent',
          filename: `Ukjent fil (${blobName.substring(0, 8)}...)`,
          blobName: blobName
        });
      }
    });

    return NextResponse.json({
      success: true,
      results: results,
      summary: {
        total: blobNames.length, // Count all blob references including duplicates
        existing: blobNames.filter(name => existingBlobs.has(name)).length, // Count all existing references
        missing: blobNames.filter(name => !existingBlobs.has(name)).length // Count all missing references
      },
      missingFilesReport: finalMissingFilesReport.sort((a, b) =>
        a.fagkode.localeCompare(b.fagkode) || a.filename.localeCompare(b.filename)
      )
    });

  } catch (error) {
    console.error('Error checking blob existence:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}