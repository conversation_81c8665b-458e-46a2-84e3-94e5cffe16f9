"use client";

import React, { useState, useEffect } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useRouter } from "next/navigation";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { IExamDocument } from "@/interface/IExamDocument";
import dayjs from "dayjs";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

const PAGINATION_KEY = "candidategroup_table_pagination_state";

export function DataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([
    { id: "groupName", desc: false },
  ]);
  const [sortKey, setSortKey] = useState(0);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(30);

  // Only trigger staggered animation on manual actions, not on data changes
  // This prevents jank during navigation when data re-fetches

  useEffect(() => {
    const savedState = sessionStorage.getItem(PAGINATION_KEY);
    if (savedState) {
      const { pageIndex: savedPageIndex, pageSize: savedPageSize } =
        JSON.parse(savedState);
      setPageIndex(savedPageIndex);
      setPageSize(savedPageSize);
    }
  }, []);

  const savePaginationState = (newPageIndex: number, newPageSize: number) => {
    sessionStorage.setItem(
      PAGINATION_KEY,
      JSON.stringify({ pageIndex: newPageIndex, pageSize: newPageSize })
    );
  };

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (updater) => {
      setSorting(updater);
      setSortKey(prev => prev + 1); // Trigger staggered animation on sort
    },
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
      pagination: {
        pageIndex,
        pageSize,
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newState = updater({ pageIndex, pageSize });
        setPageIndex(newState.pageIndex);
        setPageSize(newState.pageSize);
        savePaginationState(newState.pageIndex, newState.pageSize);
      } else {
        setPageIndex(updater.pageIndex);
        setPageSize(updater.pageSize);
        savePaginationState(updater.pageIndex, updater.pageSize);
      }
    },
  });

  const router = useRouter();

  const handleRowClick = (rowData: TData) => {
    const data: IExamPaperInternal = rowData as IExamPaperInternal;
    data.candidates.map((candidate) => {
      candidate.documents.map((document) => {
        // Preserve the original filename before applying anonymization
        if (!document.originalFileName) {
          document.originalFileName = document.fileName;
        }
        document.fileName = filenameFormatter(
          candidate.candidateNumber,
          data.testPeriod,
          data.subjectCode,
          document
        );
      });
    });
    sessionStorage.setItem("examGroupData", JSON.stringify(data));

    const groupName = (rowData as any).groupName.replace(/\./g, "%2E");
    const encodedGroupName = encodeURIComponent(groupName);

    router.push(`/nedlasting/${encodedGroupName}`);
  };

  function getYearFromPeriod(input: string): string {
    const [_, year] = input.split("-");
    return year;
  }

  // Hold en map over tidspunkt og deres tellere
  const existingFilenames = new Set<string>();

  function timeToBase36WithCounter(
    timeString: string,
    candidateNumber: string,
    subjectCode: string,
    filename: string
  ): string {
    const date = dayjs(timeString);
    if (!date.isValid()) {
      throw new Error("Invalid date format");
    }
    const nineAM = date.startOf("day").hour(9);
    const fileExtension = filename.split(".").pop() || "";
    // Generer base tidsstempel
    let baseTimestamp: string;
    if (date.isBefore(nineAM)) {
      baseTimestamp = date.format("YYYY-MM-DDTHH.mm");
    } else {
      const millisecondsSince9AM = date.diff(nineAM);
      baseTimestamp = millisecondsSince9AM.toString(36).toUpperCase();
    }
    // Test det komplette filnavnet uten counter
    let currentTimestamp = baseTimestamp;
    let fullFilename = `${candidateNumber}_${subjectCode}_${currentTimestamp}.${fileExtension}`;
    let counter = 0;

    // Hvis filnavnet eksisterer, prøv med counter til vi finner et unikt navn
    while (existingFilenames.has(fullFilename)) {
      counter++;
      currentTimestamp = `${baseTimestamp}-${counter
        .toString(36)
        .toUpperCase()}`;
      fullFilename = `${candidateNumber}_${subjectCode}_${currentTimestamp}.${fileExtension}`;
    }
    // Lagre det unike filnavnet
    existingFilenames.add(fullFilename);

    return currentTimestamp;
  }

  function filenameFormatter(
    candidateNumber: string,
    testPeriod: string,
    subjectCode: string,
    examPaper: IExamDocument
  ) {
    return `${candidateNumber}_${getYearFromPeriod(
      testPeriod
    )}_${subjectCode}_${timeToBase36WithCounter(
      examPaper.timestamp,
      candidateNumber,
      subjectCode,
      examPaper.fileName
    )}.${examPaper.fileName.split(".").pop()}`;
  }

  return (
    <div>
      <motion.div 
        className="rounded-md border overflow-y-hidden overflow-x-auto"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <Table aria-label="Tabell med eksamenspapirer og kandidatgrupper" aria-describedby="table-description">
          <caption id="table-description" className="sr-only">
            Oversikt over kandidatgrupper med fagkode, fagnavn og antall besvarelser. Klikk på en rad for å se detaljer og laste ned filer.
          </caption>
          <motion.thead
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2, delay: 0.1 }}
          >
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} scope="col">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </motion.thead>
          <TableBody key={sortKey}>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <motion.tr
                  tabIndex={0}
                  role="button"
                  aria-label={`Last ned besvarelser for ${(row.original as any).groupName}`}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      handleRowClick(row.original); // Trigger click on 'Enter' or 'Space'
                    }
                  }}
                  key={`${sortKey}-${row.id}`}
                  onClick={() => handleRowClick(row.original)}
                  className="hover:bg-slate-100 hover:cursor-pointer border-b transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                  data-state={row.getIsSelected() && "selected"}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.05,
                    ease: "easeOut"
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </motion.tr>
              ))
            ) : (
              <motion.tr
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Ingen resultater
                </TableCell>
              </motion.tr>
            )}
          </TableBody>
        </Table>
      </motion.div>
      <motion.div 
        className="flex items-center justify-between px-2 py-4"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2, ease: "easeOut" }}
      >
        <div className="flex-1 text-sm text-muted-foreground">
          Side {table.getState().pagination.pageIndex + 1} av{" "}
          {table.getPageCount()}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Forrige
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Neste
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
