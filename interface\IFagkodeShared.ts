// Shared interface that includes all possible fields
export interface IEksamensmateriell {
  eksamensmateriellID: number;
  blobReferanseEksamensmateriell?: string;
  eksamensmateriellkategori?: string;
  eksamensmateriellMalform?: string;
  fileSize?: number;
  mimeType?: string;
  filnavn?: string;
  opphavsrett?: string;
  vedlegg?: boolean;
  createdDate?: Date;
  modifiedDate?: Date;
}

export interface IEksamensdel {
  eksamensdelID: number;
  eksamensdelType?: string;
  gjennomforingStart?: Date;
  gjennomforingStopp?: Date;
  gjennomforingsystem?: string;
  eksamensveiledning?: string;
  erPlagiatkontroll: boolean;
  eksamensmateriell: IEksamensmateriell[];
}

export interface IFagkodeShared {
  fagkodeeksamensID: number;
  fagkode: string;
  variantkode?: string;
  fagnavn: string;
  eksamensdato: string | null;
  todeltStartDel2: string | null;
  eksamenstid?: string | null;
  varighet?: number | null;
  erTestFagkode: boolean;
  harPlagiatkontroll?: boolean; // Optional for simplified view
  eksamensdeler?: IEksamensdel[]; // Optional for simplified view
}