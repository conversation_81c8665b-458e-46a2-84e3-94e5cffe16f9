import {
  BlobSASPermissions,
  BlobServiceClient,
  ContainerClient,
  generateBlobSASQueryParameters,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";

const containerName = "pgsx-documents";
const pgsUrl: string = process.env.PGS_URL as string;

let blobServiceClient: BlobServiceClient | null = null;

export async function generateSasTokenUrl(
  containerName: string,
  blobName: string,
  permission: string
): Promise<string> {
  const blobServiceClient = getPGSBlobServiceClient();

  // Sjekk at credential er av typen StorageSharedKeyCredential
  if (!(blobServiceClient.credential instanceof StorageSharedKeyCredential)) {
    throw new Error(
      "BlobServiceClient er ikke initialisert med StorageSharedKeyCredential."
    );
  }

  const startsOn = new Date(new Date().getTime() - 2 * 60000); // 2 minutter før nå
  const expiresOn = new Date(new Date().getTime() + 10 * 60000); // 10 minutter etter nå

  const sasOptions = {
    containerName,
    blobName,
    permissions: BlobSASPermissions.parse(permission),
    startsOn,
    expiresOn,
  };

  const sasToken = generateBlobSASQueryParameters(
    sasOptions,
    blobServiceClient.credential
  ).toString();

  return `${pgsUrl}/${containerName}/${blobName}?${sasToken}`;
}

export const getPGSBlobServiceClient = (): BlobServiceClient => {
  if (!blobServiceClient) {
    const connectionString =
      process.env.CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING;
    if (!connectionString) {
      throw new Error("Miljøvariabel for tilkoblingsstreng er ikke satt.");
    }
    blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
  }
  return blobServiceClient;
};

export const getPGSXContainerClient = (): ContainerClient => {
  const blobServiceClient = getPGSBlobServiceClient();
  return blobServiceClient.getContainerClient(containerName);
};
