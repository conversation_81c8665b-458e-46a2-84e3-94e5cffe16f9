export enum OperationEnum {
  StatusLevertDigitalt = 20,
  StatusLevertDigitaltDel2 = 22,
  StatusDokumentedAbsence = 100,
  StatusAccessGranted = 101,
  StatusAccessRefused = 102,
  StatusUnDocumentedAbsence = 103,
  StatusUndoAbsence = 104,
  StatusSendInPost = 105,
  StatusSendInPostPart1 = 106,
  StatusSendInPostPart2 = 107,
  StatusUndoDeliveredOnPaper = 108,
  StatusUndoDeliveredOnPaperPart1 = 109,
  StatusUndoDeliveredOnPaperPart2 = 110,
  StatusDeliveredExam = 111,
  StatusDeliveredPart2 = 112,
  StatusOpenedForNewDeliveryEksamen = 113,
  StatusOpenedForNewDelivery = 114,
  StatusBlocked = 115,
  StatusUnBlocked = 116,
  StatusOppdatering = 117,
  StatusFileDeleted = 118,
  StatusFileUploaded = 119,
  StatusFileDelivered = 120,
  StatusFileDeliveredPart1 = 121,
  StatusFileDeliveredPart2 = 122,
  StatusFileDownloaded = 123,
  StatusChangedTestPartId = 124,
  StatusFileChecked = 125,
  StatusRemovedAccess = 126,
  StatusFileUploadedGroupUpload = 127,
  StatusFileDeliveredGroupUpload = 128,
  StatusFileDeliveredGroupUploadDel1 = 129,
  StatusFileDeliveredGroupUploadDel2 = 130,
  StatusFileDeletedGroupUpload = 131,
  StatusFileDeletedGroupUploadDel1 = 132,
  StatusFileDeletedGroupUploadDel2 = 133,
  StatusChangedTestPartIdGoupUpload = 134,
  StatusDeliveredDigitalAccessBlocked = 135,
  StatusDeliveredDigitalAccessBlockedPart2 = 136,
  StatusSendInPostDigitalAccessBlocked = 137,
  StatusSendInPostDigitalAccessBlockedPart1 = 138,
  StatusSendInPostDigitalAccessBlockedPart2 = 139,
  StatusDigitalAccessBlockedLogout = 140,
  StatusDigitalAccessRemovedLogout = 141,
  StatusDeliverForMonitorTwoPartsPart1 = 142,
  StatusDeliverForMonitorTwoPartsPart2 = 143,
  StatusDeliverForGroupUploadTwoPartsPart1 = 144,
  StatusDeliverForGroupUploadTwoPartsPart2 = 145,
  StatusLevertDigitaltDel1 = 146,
  StatusChangedTestPartIdPart1TwoPartsMonitor = 147,
  StatusChangedTestPartIdPart2TwoPartsMonitor = 148,
  StatusChangedTestPartIdPart1TwoPartsGroupUpload = 149,
  StatusChangedTestPartIdPart2TwoPartsGroupUpload = 150,
  StatusDeliveredDigitalAccessBlockedPart1 = 151,
}
