"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, Download, Loader2 } from "lucide-react";
import { IEksamensplanImportResponse } from "@/interface/IEksamensplanImport";

type ImportResult = IEksamensplanImportResponse;

export default function ImportDataPage() {
  const [eksamensperiode, setEksamensperiode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingKandidateksamen, setIsLoadingKandidateksamen] = useState(false);
  const [isLoadingBesvarelser, setIsLoadingBesvarelser] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [kandidateksamenResult, setKandidateksamenResult] = useState<ImportResult | null>(null);
  const [besvarelserResult, setBesvarelserResult] = useState<ImportResult | null>(null);
  const [availableEksamensperioder, setAvailableEksamensperioder] = useState<
    string[]
  >([]);

  // Generer liste over tilgjengelige eksamensperioder på klientsiden
  useEffect(() => {
    const generateEksamensperioder = () => {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;

      // Bestem nåværende periode basert på norsk skoleår
      // V (Vår) starter 1. januar, H (Høst) starter 1. juli
      const currentPeriod = currentMonth >= 7
        ? `H-${currentYear}`
        : `V-${currentYear}`;

      // Generer perioder fra 3 år tilbake til 1 år frem i tid
      const periods: string[] = [];
      const startYear = currentYear - 3;
      const endYear = currentYear + 1;

      for (let year = startYear; year <= endYear; year++) {
        periods.push(`V-${year}`);
        periods.push(`H-${year}`);
      }

      return { periods, currentPeriod };
    };
    const { periods, currentPeriod } = generateEksamensperioder();
    setAvailableEksamensperioder(periods);
    setEksamensperiode(currentPeriod);
  }, []);

  const handleImport = async () => {
    if (!eksamensperiode) {
      setResult({
        success: false,
        message: "Vennligst velg en eksamensperiode",
        error: "Manglende eksamensperiode",
      });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch("/api/admin/import-data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ eksamensperiode }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setResult({
          success: false,
          message: "Import feilet",
          error: data.error || "Ukjent feil",
          details: data.details,
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: "Nettverksfeil",
        error: "Kunne ikke koble til serveren",
        details: error instanceof Error ? error.message : "Ukjent feil",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportKandidateksamen = async () => {
    if (!eksamensperiode) {
      setKandidateksamenResult({
        success: false,
        message: "Vennligst velg en eksamensperiode",
        error: "Manglende eksamensperiode",
      });
      return;
    }

    setIsLoadingKandidateksamen(true);
    setKandidateksamenResult(null);

    try {
      const response = await fetch("/api/admin/import-kandidateksamen", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ eksamensperiode }),
      });

      const data = await response.json();

      if (response.ok) {
        setKandidateksamenResult(data);
      } else {
        setKandidateksamenResult({
          success: false,
          message: "Import feilet",
          error: data.error || "Ukjent feil",
          details: data.details,
        });
      }
    } catch (error) {
      setKandidateksamenResult({
        success: false,
        message: "Nettverksfeil",
        error: "Kunne ikke koble til serveren",
        details: error instanceof Error ? error.message : "Ukjent feil",
      });
    } finally {
      setIsLoadingKandidateksamen(false);
    }
  };

  const handleImportBesvarelser = async () => {
    if (!eksamensperiode) {
      setBesvarelserResult({
        success: false,
        message: "Vennligst velg en eksamensperiode",
        error: "Manglende eksamensperiode",
      });
      return;
    }

    setIsLoadingBesvarelser(true);
    setBesvarelserResult(null);

    try {
      const response = await fetch("/api/admin/import-kandidatbesvarelser", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ eksamensperiode }),
      });

      const data = await response.json();

      if (response.ok) {
        setBesvarelserResult(data);
      } else {
        setBesvarelserResult({
          success: false,
          message: "Import feilet",
          error: data.error || "Ukjent feil",
          details: data.details,
        });
      }
    } catch (error) {
      setBesvarelserResult({
        success: false,
        message: "Nettverksfeil",
        error: "Kunne ikke koble til serveren",
        details: error instanceof Error ? error.message : "Ukjent feil",
      });
    } finally {
      setIsLoadingBesvarelser(false);
    }
  };

  const handleReset = () => {
    setResult(null);
    setKandidateksamenResult(null);
    setBesvarelserResult(null);
    setEksamensperiode("");
  };

  return (
    <>
      <div className="py-6 bg-header">
        <div className="container-wrapper-lg">
          <div>
            <h1 className="text-4xl">Importer data</h1>
            <p className="mt-4">
              Last ned og importer kandidatgrupper fra PAS eksamensapi til PGS
              databasen.
            </p>
            <p className="mt-2 text-sm text-red-600">
              Obs! Denne siden skal kun benyttes av utviklere.
            </p>
          </div>
        </div>
      </div>
      <div className="container-wrapper-lg py-8">
        <div className="space-y-6">
          {/* Felles eksamensperiode-velger */}
          <Card className="max-w-md">
            <CardHeader>
              <CardTitle>Velg eksamensperiode</CardTitle>
              <CardDescription>
                Velg en eksamensperiode som gjelder for alle import-prosesser.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Label htmlFor="eksamensperiode">Eksamensperiode</Label>
              <Select
                value={eksamensperiode}
                onValueChange={setEksamensperiode}
                disabled={isLoading || isLoadingKandidateksamen || isLoadingBesvarelser}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Velg eksamensperiode" />
                </SelectTrigger>
                <SelectContent>
                  {availableEksamensperioder.length > 0 ? (
                    availableEksamensperioder.map((periode: string) => (
                      <SelectItem key={periode} value={periode}>
                        {periode} ({periode.startsWith("H") ? "Høst" : "Vår"}{" "}
                        {periode.split("-")[1]})
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      Laster perioder...
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Velg eksamensperiode fra listen.
              </p>
            </CardContent>
          </Card>

          {/* Grid med import-komponenter side ved side */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* Steg 1: Import Kandidatgrupper */}
            <div className="space-y-4">
              <Card className="border-2 border-blue-200">
                <CardHeader className="bg-blue-50">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-bold">
                      1
                    </div>
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Download className="w-5 h-5" />
                        Import Kandidatgrupper
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Importer kandidatgrupper for valgt periode.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="flex gap-2">
                    <Button
                      onClick={handleImport}
                      disabled={
                        isLoading ||
                        !eksamensperiode ||
                        availableEksamensperioder.length === 0
                      }
                      className="flex items-center gap-2 flex-1"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          Importerer...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4" />
                          Importer kandidatgrupper
                        </>
                      )}
                    </Button>

                    {result && (
                      <Button variant="outline" onClick={handleReset}>
                        Tilbakestill
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Resultat for kandidatgrupper */}
              {result && (
                <Alert
                  className={
                    result.success
                      ? result.data && result.data.importedCount === 0
                        ? "border-yellow-200 bg-yellow-50"
                        : "border-green-200 bg-green-50"
                      : "border-red-200 bg-red-50"
                  }
                >
                  <div className="flex items-center gap-2">
                    {result.success ? (
                      result.data && result.data.importedCount === 0 ? (
                        <CheckCircle className="w-4 h-4 text-yellow-600" />
                      ) : (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      )
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    )}
                    <AlertTitle
                      className={
                        result.success
                          ? result.data && result.data.importedCount === 0
                            ? "text-yellow-800"
                            : "text-green-800"
                          : "text-red-800"
                      }
                    >
                      {result.success
                        ? result.data && result.data.importedCount === 0
                          ? "Ingen data funnet"
                          : "Import fullført!"
                        : "Import feilet"}
                    </AlertTitle>
                  </div>
                  <AlertDescription
                    className={`mt-2 ${
                      result.success
                        ? result.data && result.data.importedCount === 0
                          ? "text-yellow-700"
                          : "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    <div className="space-y-2">
                      <p>{result.message}</p>
                      {result.success && result.data && (
                        <div
                          className={`bg-white p-3 rounded border ${
                            result.data.importedCount === 0
                              ? "border-yellow-200"
                              : "border-green-200"
                          } text-sm`}
                        >
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <strong>Eksamensperiode:</strong>{" "}
                              {result.data.eksamensperiode}
                            </div>
                            <div>
                              <strong>Importert:</strong>{" "}
                              {result.data.importedCount} kandidatgrupper
                            </div>
                            <div>
                              <strong>Feil:</strong> {result.data.errorCount}
                            </div>
                            <div>
                              <strong>Slettet:</strong> {result.data.deletedCount}
                            </div>
                          </div>
                          {result.data.errorDetails && result.data.errorDetails.length > 0 && (
                            <div className="mt-4 pt-4 border-t border-green-300">
                              <div className="flex items-center gap-2 mb-2">
                                <AlertCircle className="w-4 h-4 text-orange-600" />
                                <strong className="text-sm text-orange-800">Feildetaljer ({result.data.errorDetails.length})</strong>
                              </div>
                              <div className="bg-orange-50 border border-orange-200 rounded-md p-4 max-h-64 overflow-y-auto shadow-sm">
                                <ul className="space-y-2.5">
                                  {result.data.errorDetails.map((error: string, index: number) => (
                                    <li key={index} className="text-xs text-gray-700 flex gap-2 leading-relaxed">
                                      <span className="text-orange-600 font-bold shrink-0 mt-0.5">•</span>
                                      <span className="break-words flex-1">{error}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      {!result.success && result.error && (
                        <div className="bg-white p-3 rounded border border-red-200 text-sm">
                          <div>
                            <strong>Feilmelding:</strong> {result.error}
                          </div>
                          {result.details && (
                            <div className="mt-1">
                              <strong>Detaljer:</strong> {result.details}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Steg 2: Import Kandidateksamen */}
            <div className="space-y-4">
              <Card className="border-2 border-purple-200">
                <CardHeader className="bg-purple-50">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-600 text-white font-bold">
                      2
                    </div>
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Download className="w-5 h-5" />
                        Import Kandidateksamen
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Importer kandidateksamen for valgt periode.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-6 space-y-4">
                  <Alert className="bg-blue-50 border-blue-200">
                    <AlertCircle className="w-4 h-4 text-blue-600" />
                    <AlertTitle className="text-blue-800">Viktig!</AlertTitle>
                    <AlertDescription className="text-blue-700">
                      Kandidatgrupper må være importert først (steg 1).
                    </AlertDescription>
                  </Alert>

                  <div className="flex gap-2">
                    <Button
                      onClick={handleImportKandidateksamen}
                      disabled={
                        isLoadingKandidateksamen ||
                        !eksamensperiode ||
                        availableEksamensperioder.length === 0
                      }
                      className="flex items-center gap-2 flex-1"
                    >
                      {isLoadingKandidateksamen ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          Importerer...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4" />
                          Importer kandidateksamen
                        </>
                      )}
                    </Button>

                    {kandidateksamenResult && (
                      <Button variant="outline" onClick={handleReset}>
                        Tilbakestill
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Resultat for kandidateksamen */}
              {kandidateksamenResult && (
                <Alert
                  className={
                    kandidateksamenResult.success
                      ? kandidateksamenResult.data && kandidateksamenResult.data.importedCount === 0
                        ? "border-yellow-200 bg-yellow-50"
                        : "border-green-200 bg-green-50"
                      : "border-red-200 bg-red-50"
                  }
                >
                  <div className="flex items-center gap-2">
                    {kandidateksamenResult.success ? (
                      kandidateksamenResult.data && kandidateksamenResult.data.importedCount === 0 ? (
                        <CheckCircle className="w-4 h-4 text-yellow-600" />
                      ) : (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      )
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    )}
                    <AlertTitle
                      className={
                        kandidateksamenResult.success
                          ? kandidateksamenResult.data && kandidateksamenResult.data.importedCount === 0
                            ? "text-yellow-800"
                            : "text-green-800"
                          : "text-red-800"
                      }
                    >
                      {kandidateksamenResult.success
                        ? kandidateksamenResult.data && kandidateksamenResult.data.importedCount === 0
                          ? "Ingen data funnet"
                          : "Import fullført!"
                        : "Import feilet"}
                    </AlertTitle>
                  </div>
                  <AlertDescription
                    className={`mt-2 ${
                      kandidateksamenResult.success
                        ? kandidateksamenResult.data && kandidateksamenResult.data.importedCount === 0
                          ? "text-yellow-700"
                          : "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    <div className="space-y-2">
                      <p>{kandidateksamenResult.message}</p>
                      {kandidateksamenResult.success && kandidateksamenResult.data && (
                        <div
                          className={`bg-white p-3 rounded border ${
                            kandidateksamenResult.data.importedCount === 0
                              ? "border-yellow-200"
                              : "border-green-200"
                          } text-sm`}
                        >
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <strong>Eksamensperiode:</strong>{" "}
                              {kandidateksamenResult.data.eksamensperiode}
                            </div>
                            <div>
                              <strong>Importert:</strong>{" "}
                              {kandidateksamenResult.data.importedCount} kandidateksamen
                            </div>
                            <div>
                              <strong>Feil:</strong> {kandidateksamenResult.data.errorCount}
                            </div>
                            <div>
                              <strong>Slettet:</strong> {kandidateksamenResult.data.deletedCount}
                            </div>
                          </div>
                          {kandidateksamenResult.data.errorDetails && kandidateksamenResult.data.errorDetails.length > 0 && (
                            <div className="mt-4 pt-4 border-t border-green-300">
                              <div className="flex items-center gap-2 mb-2">
                                <AlertCircle className="w-4 h-4 text-orange-600" />
                                <strong className="text-sm text-orange-800">Feildetaljer ({kandidateksamenResult.data.errorDetails.length})</strong>
                              </div>
                              <div className="bg-orange-50 border border-orange-200 rounded-md p-4 max-h-64 overflow-y-auto shadow-sm">
                                <ul className="space-y-2.5">
                                  {kandidateksamenResult.data.errorDetails.map((error: string, index: number) => (
                                    <li key={index} className="text-xs text-gray-700 flex gap-2 leading-relaxed">
                                      <span className="text-orange-600 font-bold shrink-0 mt-0.5">•</span>
                                      <span className="break-words flex-1">{error}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      {!kandidateksamenResult.success && kandidateksamenResult.error && (
                        <div className="bg-white p-3 rounded border border-red-200 text-sm">
                          <div>
                            <strong>Feilmelding:</strong> {kandidateksamenResult.error}
                          </div>
                          {kandidateksamenResult.details && (
                            <div className="mt-1">
                              <strong>Detaljer:</strong> {kandidateksamenResult.details}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Steg 3: Import Kandidatbesvarelser */}
            <div className="space-y-4">
              <Card className="border-2 border-green-200">
                <CardHeader className="bg-green-50">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-600 text-white font-bold">
                      3
                    </div>
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Download className="w-5 h-5" />
                        Import Kandidatbesvarelser
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Importer kandidatbesvarelser for valgt periode.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-6 space-y-4">
                  <Alert className="bg-blue-50 border-blue-200">
                    <AlertCircle className="w-4 h-4 text-blue-600" />
                    <AlertTitle className="text-blue-800">Viktig!</AlertTitle>
                    <AlertDescription className="text-blue-700">
                      Kandidateksamen må være importert først (steg 1 og 2).
                    </AlertDescription>
                  </Alert>

                  <div className="flex gap-2">
                    <Button
                      onClick={handleImportBesvarelser}
                      disabled={
                        isLoadingBesvarelser ||
                        !eksamensperiode ||
                        availableEksamensperioder.length === 0
                      }
                      className="flex items-center gap-2 flex-1"
                    >
                      {isLoadingBesvarelser ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          Importerer...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4" />
                          Importer kandidatbesvarelser
                        </>
                      )}
                    </Button>

                    {besvarelserResult && (
                      <Button variant="outline" onClick={handleReset}>
                        Tilbakestill
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Resultat for kandidatbesvarelser */}
              {besvarelserResult && (
                <Alert
                  className={
                    besvarelserResult.success
                      ? besvarelserResult.data && besvarelserResult.data.importedCount === 0
                        ? "border-yellow-200 bg-yellow-50"
                        : "border-green-200 bg-green-50"
                      : "border-red-200 bg-red-50"
                  }
                >
                  <div className="flex items-center gap-2">
                    {besvarelserResult.success ? (
                      besvarelserResult.data && besvarelserResult.data.importedCount === 0 ? (
                        <CheckCircle className="w-4 h-4 text-yellow-600" />
                      ) : (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      )
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    )}
                    <AlertTitle
                      className={
                        besvarelserResult.success
                          ? besvarelserResult.data && besvarelserResult.data.importedCount === 0
                            ? "text-yellow-800"
                            : "text-green-800"
                          : "text-red-800"
                      }
                    >
                      {besvarelserResult.success
                        ? besvarelserResult.data && besvarelserResult.data.importedCount === 0
                          ? "Ingen data funnet"
                          : "Import fullført!"
                        : "Import feilet"}
                    </AlertTitle>
                  </div>
                  <AlertDescription
                    className={`mt-2 ${
                      besvarelserResult.success
                        ? besvarelserResult.data && besvarelserResult.data.importedCount === 0
                          ? "text-yellow-700"
                          : "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    <div className="space-y-2">
                      <p>{besvarelserResult.message}</p>
                      {besvarelserResult.success && besvarelserResult.data && (
                        <div
                          className={`bg-white p-3 rounded border ${
                            besvarelserResult.data.importedCount === 0
                              ? "border-yellow-200"
                              : "border-green-200"
                          } text-sm`}
                        >
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <strong>Eksamensperiode:</strong>{" "}
                              {besvarelserResult.data.eksamensperiode}
                            </div>
                            <div>
                              <strong>Importert:</strong>{" "}
                              {besvarelserResult.data.importedCount} besvarelser
                            </div>
                            <div>
                              <strong>Feil:</strong> {besvarelserResult.data.errorCount}
                            </div>
                            <div>
                              <strong>Slettet:</strong> {besvarelserResult.data.deletedCount}
                            </div>
                          </div>
                          {besvarelserResult.data.errorDetails && besvarelserResult.data.errorDetails.length > 0 && (
                            <div className="mt-4 pt-4 border-t border-green-300">
                              <div className="flex items-center gap-2 mb-2">
                                <AlertCircle className="w-4 h-4 text-orange-600" />
                                <strong className="text-sm text-orange-800">Feildetaljer ({besvarelserResult.data.errorDetails.length})</strong>
                              </div>
                              <div className="bg-orange-50 border border-orange-200 rounded-md p-4 max-h-64 overflow-y-auto shadow-sm">
                                <ul className="space-y-2.5">
                                  {besvarelserResult.data.errorDetails.map((error: string, index: number) => (
                                    <li key={index} className="text-xs text-gray-700 flex gap-2 leading-relaxed">
                                      <span className="text-orange-600 font-bold shrink-0 mt-0.5">•</span>
                                      <span className="break-words flex-1">{error}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      {!besvarelserResult.success && besvarelserResult.error && (
                        <div className="bg-white p-3 rounded border border-red-200 text-sm">
                          <div>
                            <strong>Feilmelding:</strong> {besvarelserResult.error}
                          </div>
                          {besvarelserResult.details && (
                            <div className="mt-1">
                              <strong>Detaljer:</strong> {besvarelserResult.details}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasjon</CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-muted-foreground space-y-4">
              <div>
                <strong className="text-base">Import-rekkefølge:</strong>
                <ol className="list-decimal list-inside mt-2 space-y-1 ml-2">
                  <li>Import <strong>Kandidatgrupper</strong> først</li>
                  <li>Deretter import <strong>Kandidateksamen</strong></li>
                  <li>Til slutt import <strong>Kandidatbesvarelser</strong></li>
                </ol>
                <p className="mt-2 text-orange-700">
                  ⚠️ Hver import krever at forrige steg er fullført for den valgte perioden.
                </p>
              </div>

              <div className="pt-2 border-t">
                <p className="mb-2"><strong>API-endepunkter:</strong></p>
                <ul className="list-disc list-inside ml-2 space-y-1">
                  <li>Kandidatgrupper: /pgsd/kandidatgrupper (via eksamensdato)</li>
                  <li>Kandidateksamen: /pgsa/examgroup/{`{groupCode}`}</li>
                  <li>Kandidatbesvarelser: PGSR API /api/ExamPaperInternal/metadata</li>
                </ul>
              </div>

              <div className="pt-2 border-t">
                <p>
                  <strong>Tabeller som oppdateres:</strong> Kandidatgrupper, Kandidateksamen, Kandidatbesvarelser
                </p>
                <p>
                  <strong>Import-logikk:</strong> Eksisterende data oppdateres,
                  nye data legges til, utdaterte data slettes.
                </p>
                <p>
                  <strong>Tilgang:</strong> Kun systemadministratorer kan utføre
                  denne operasjonen.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}