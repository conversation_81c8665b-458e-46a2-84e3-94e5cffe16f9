import { getDbConnection } from "../connection";
import { Kandidatgruppe } from "../models/Kandidatgruppe";
import { Fagkodeeksamen } from "../models/Fagkodeeksamen";
import { generateAccessToken } from "@/lib/server/getAccessTokenForEksamen";

interface ExamGroupApiResponse {
  groupCode: string;
  schoolId: string;
  genFileNames: string[];
  subjectCode: string;
  variantCode: string;
}

export class KandidatgruppeService {
  /**
   * Henter kandidatgrupper fra ekstern API for en spesifikk dato
   */
  private async fetchKandidatgrupperForDate(date: string): Promise<ExamGroupApiResponse[]> {
    const accessToken = await generateAccessToken();
    const apiUrl = process.env.EKSAMENAPI_URL || "";

    const response = await fetch(
      `${apiUrl}/pgsa/examgroupbydate/${date}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        console.log(`No kandidatgrupper found for date: ${date} (404)`);
        return []; // Return empty array for 404, this is normal
      }
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`Fetched ${data.length} kandidatgrupper for date: ${date}`);

    return data;
  }

  /**
   * Henter alle unike eksamensdatoer for en gitt eksamensperiode
   */
  private async getExamDatesForPeriod(eksamensperiode: string): Promise<string[]> {
    const AppDataSource = await getDbConnection();
    const fagkodeeksamensRepository = AppDataSource.getRepository(Fagkodeeksamen);

    const examDates = await fagkodeeksamensRepository
      .createQueryBuilder("f")
      .select("DISTINCT f.Eksamensdato", "eksamensdato")
      .where("f.Eksamensperiode = :periode", { periode: eksamensperiode })
      .andWhere("f.Eksamensdato IS NOT NULL")
      .getRawMany();

    const dates = examDates
      .map(row => row.eksamensdato)
      .filter(date => date !== null)
      .map(date => {
        // Format date as YYYY-MM-DD for API call
        const dateObj = new Date(date);
        return dateObj.toISOString().split('T')[0];
      });

    console.log(`Found ${dates.length} unique exam dates for period ${eksamensperiode}:`, dates);
    return dates;
  }

  /**
   * Finner FagkodeeksamensID basert på fagkode, variantkode og eksamensperiode
   */
  private async findFagkodeeksamensId(
    fagkode: string,
    variantkode: string | null,
    eksamensperiode: string
  ): Promise<number | null> {
    const AppDataSource = await getDbConnection();
    const fagkodeeksamensRepository = AppDataSource.getRepository(Fagkodeeksamen);

    const query = fagkodeeksamensRepository
      .createQueryBuilder("f")
      .select("f.FagkodeeksamensID")
      .where("f.Fagkode = :fagkode", { fagkode })
      .andWhere("f.Eksamensperiode = :periode", { periode: eksamensperiode });

    if (variantkode) {
      query.andWhere("f.Variantkode = :variantkode", { variantkode });
    } else {
      query.andWhere("f.Variantkode IS NULL");
    }

    const result = await query.getOne();
    return result?.FagkodeeksamensID || null;
  }

  /**
   * Lagrer kandidatgrupper til database
   */
  private async saveKandidatgrupper(
    kandidatgrupper: ExamGroupApiResponse[],
    eksamensperiode: string
  ): Promise<{ success: number; errors: number; deleted: number; errorDetails: string[] }> {
    const AppDataSource = await getDbConnection();
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let successCount = 0;
    let errorCount = 0;
    let deletedCount = 0;
    const errorDetails: string[] = [];

    try {
      const kandidatgruppeRepository = queryRunner.manager.getRepository(Kandidatgruppe);

      // Få alle eksisterende kandidatgrupper for alle fagkoder i denne perioden
      const existingKandidatgrupper = await kandidatgruppeRepository
        .createQueryBuilder("k")
        .innerJoin("k.fagkodeeksamen", "f")
        .where("f.Eksamensperiode = :periode", { periode: eksamensperiode })
        .getMany();

      console.log(`Found ${existingKandidatgrupper.length} existing kandidatgrupper for period ${eksamensperiode}`);

      // Opprett lookup map for eksisterende kandidatgrupper
      const existingKandidatgrupperMap = new Map<string, Kandidatgruppe>();
      existingKandidatgrupper.forEach(k => {
        existingKandidatgrupperMap.set(k.Kandidatgruppekode, k);
      });

      // Samle alle kandidatgruppekoder fra import
      const importedKandidatgruppekoder = new Set<string>();

      // Arrays for bulk operasjoner
      const kandidatgrupperToInsert: Kandidatgruppe[] = [];
      const kandidatgrupperToUpdate: Kandidatgruppe[] = [];

      // Prosesser hver kandidatgruppe
      for (const item of kandidatgrupper) {
        try {
          importedKandidatgruppekoder.add(item.groupCode);

          // Finn tilhørende fagkodeeksamensID
          const fagkodeeksamensId = await this.findFagkodeeksamensId(
            item.subjectCode,
            item.variantCode || null,
            eksamensperiode
          );

          if (!fagkodeeksamensId) {
            const errorMsg = `Kunne ikke finne FagkodeeksamensID for fagkode: ${item.subjectCode}, variant: ${item.variantCode || 'ingen'}, gruppe: ${item.groupCode}`;
            console.warn(errorMsg);
            errorDetails.push(errorMsg);
            errorCount++;
            continue;
          }

          const existing = existingKandidatgrupperMap.get(item.groupCode);

          // Use first element from genFileNames array
          const kandidatgruppenavn = item.genFileNames.length > 0
            ? item.genFileNames[0]
            : undefined;

          if (existing) {
            // Oppdater eksisterende
            existing.SkoleID = item.schoolId;
            existing.FagkodeeksamensID = fagkodeeksamensId;
            existing.Kandidatgruppenavn = kandidatgruppenavn;
            kandidatgrupperToUpdate.push(existing);
          } else {
            // Opprett ny
            const newKandidatgruppe = kandidatgruppeRepository.create({
              Kandidatgruppekode: item.groupCode,
              SkoleID: item.schoolId,
              FagkodeeksamensID: fagkodeeksamensId,
              Kandidatgruppenavn: kandidatgruppenavn
            });
            kandidatgrupperToInsert.push(newKandidatgruppe);
          }

          successCount++;
        } catch (error) {
          const errorMsg = `Feil ved prosessering av gruppe ${item.groupCode}: ${error instanceof Error ? error.message : String(error)}`;
          console.error(errorMsg, error);
          errorDetails.push(errorMsg);
          errorCount++;
        }
      }

      // Identifiser kandidatgrupper som skal slettes
      const kandidatgrupperToDelete: Kandidatgruppe[] = [];
      existingKandidatgrupperMap.forEach((kandidatgruppe, kode) => {
        if (!importedKandidatgruppekoder.has(kode)) {
          kandidatgrupperToDelete.push(kandidatgruppe);
        }
      });

      // Utfør bulk operasjoner
      if (kandidatgrupperToDelete.length > 0) {
        console.log(`Deleting ${kandidatgrupperToDelete.length} kandidatgrupper`);

        // First, remove foreign key references in Kandidateksamen
        const kandidatgruppeIds = kandidatgrupperToDelete.map(k => k.KandidatgruppeID).filter(id => id !== null && id !== undefined);
        if (kandidatgruppeIds.length > 0) {
          await queryRunner.manager.query(
            `UPDATE Kandidateksamen SET KandidatgruppeID = NULL WHERE KandidatgruppeID IN (${kandidatgruppeIds.join(', ')})`
          );
          console.log(`Cleared KandidatgruppeID references for ${kandidatgruppeIds.length} kandidatgrupper`);
        }

        await this.deleteBatched(kandidatgruppeRepository, kandidatgrupperToDelete);
        deletedCount = kandidatgrupperToDelete.length;
      }

      if (kandidatgrupperToInsert.length > 0) {
        console.log(`Inserting ${kandidatgrupperToInsert.length} new kandidatgrupper`);
        await this.saveBatched(kandidatgruppeRepository, kandidatgrupperToInsert, "insert");
      }

      if (kandidatgrupperToUpdate.length > 0) {
        console.log(`Updating ${kandidatgrupperToUpdate.length} kandidatgrupper`);
        await this.saveBatched(kandidatgruppeRepository, kandidatgrupperToUpdate, "update");
      }

      await queryRunner.commitTransaction();
      console.log(`Kandidatgrupper processed: ${successCount} success, ${errorCount} errors, ${deletedCount} deleted`);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error in kandidatgruppe save operation:", error);
      throw error;
    } finally {
      await queryRunner.release();
    }

    return { success: successCount, errors: errorCount, deleted: deletedCount, errorDetails };
  }

  /**
   * Batch save method to avoid SQL Server parameter limit
   */
  private async saveBatched<T>(
    repository: any,
    entities: T[],
    operation: "insert" | "update",
    batchSize: number = 100
  ): Promise<void> {
    if (entities.length === 0) return;

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      try {
        if (operation === "insert") {
          await repository
            .createQueryBuilder()
            .insert()
            .values(batch)
            .execute();
        } else {
          // For updates, handle each entity separately
          for (const entity of batch) {
            const primaryKey = repository.metadata.primaryColumns[0].propertyName;
            const primaryValue = (entity as any)[primaryKey];

            const updateData = { ...entity } as any;
            delete updateData[primaryKey];

            // Remove generated columns (like KandidatgruppeID) from update
            const generatedColumns = repository.metadata.columns
              .filter((col: any) => col.isGenerated)
              .map((col: any) => col.propertyName);

            generatedColumns.forEach((col: string) => {
              delete updateData[col];
            });

            await repository
              .createQueryBuilder()
              .update()
              .set(updateData)
              .where(`${primaryKey} = :id`, { id: primaryValue })
              .execute();
          }
        }
      } catch (error) {
        console.error(`Error in batch ${Math.floor(i / batchSize) + 1}:`, error);
        throw error;
      }
    }
  }

  /**
   * Batch delete method
   */
  private async deleteBatched<T>(
    repository: any,
    entities: T[],
    batchSize: number = 100
  ): Promise<void> {
    if (entities.length === 0) return;

    const primaryKey = repository.metadata.primaryColumns[0].propertyName;

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      const ids = batch.map((entity: any) => entity[primaryKey]);

      try {
        await repository
          .createQueryBuilder()
          .delete()
          .where(`${primaryKey} IN (:...ids)`, { ids })
          .execute();
      } catch (error) {
        console.error(`Error in delete batch ${Math.floor(i / batchSize) + 1}:`, error);
        throw error;
      }
    }
  }

  /**
   * Hovedmetode for å importere kandidatgrupper
   */
  async importKandidatgrupper(
    eksamensperiode: string
  ): Promise<{ success: number; errors: number; deleted: number; message: string; errorDetails?: string[] }> {
    const startTime = Date.now();

    try {
      console.log(`Starting kandidatgruppe import for period: ${eksamensperiode}`);

      // Hent alle eksamensdatoer for perioden
      const examDates = await this.getExamDatesForPeriod(eksamensperiode);

      if (examDates.length === 0) {
        return {
          success: 0,
          errors: 0,
          deleted: 0,
          message: `Ingen eksamensdatoer funnet for periode ${eksamensperiode}`,
        };
      }

      // Hent kandidatgrupper for alle datoer
      let allKandidatgrupper: ExamGroupApiResponse[] = [];

      for (const date of examDates) {
        try {
          const kandidatgrupperForDate = await this.fetchKandidatgrupperForDate(date);
          allKandidatgrupper = allKandidatgrupper.concat(kandidatgrupperForDate);
        } catch (error) {
          console.warn(`Failed to fetch kandidatgrupper for date ${date}:`, error);
          // Continue with other dates even if one fails
        }
      }

      if (allKandidatgrupper.length === 0) {
        return {
          success: 0,
          errors: 0,
          deleted: 0,
          message: `Ingen kandidatgrupper funnet for periode ${eksamensperiode}`,
        };
      }

      console.log(`Processing ${allKandidatgrupper.length} kandidatgrupper...`);

      // Lagre kandidatgrupper til database
      const result = await this.saveKandidatgrupper(allKandidatgrupper, eksamensperiode);

      const duration = Date.now() - startTime;
      const successMessage = `Import fullført på ${duration}ms. ${result.success} vellykket, ${result.errors} feil, ${result.deleted} slettet.`;

      return {
        ...result,
        message: successMessage,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Import feilet etter ${duration}ms: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`;

      console.error("Error during kandidatgruppe import:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        eksamensperiode,
        duration,
      });

      throw new Error(errorMessage);
    }
  }
}