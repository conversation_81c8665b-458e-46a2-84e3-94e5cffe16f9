"use client";

import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  getExpandedRowModel,
  ExpandedState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { useState } from "react";
import React from "react";
import { ChevronRight } from "lucide-react";
import SessionDetails from "@/app/[locale]/pgs-monitor/sessionDetails";
import { createColumns } from "./columns";
import { useCandidate } from "@/context/CandidateMonitorContext";
import { toast } from "@/components/ui/use-toast";
import useSWR from "swr";
import { ActivityLogger } from "@/lib/shared/ActivityLogger";
import { useRole } from "@/context/RoleContext";
import { OperationEnum } from "@/enums/OperationEnum";

interface ICandidateSessionData {
  Entry: string;
  SessionId: string;
  IpAddress: string;
  StartTime: string;
  LastActive: string;
  Browser: string;
  OS: string;
  Device: string;
}

interface DataTableProps {
  data: IRedisObject[];
  candidate?: {
    candidateName: string;
    candidateNumber: string;
    candidateRegistrationId?: string;
    userId: string;
  };
}

const POLL_INTERVAL = 60000; // 1 minutt

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error("Kunne ikke hente sesjonsdata");
  }
  return response.json();
};

export function DataTable({ data, candidate }: DataTableProps) {
  const [sorting, setSorting] = useState<SortingState>([
    { id: "isAuthorized", desc: true },
    { id: "timeForSession", desc: true },
  ]);
  const [expanded, setExpanded] = useState<ExpandedState>({});

  const {
    removeAccess,
    batchUpdateCandidateStatus,
    candidateStatus,
    redisResult,
    updateRedisResult,
  } = useCandidate();

  const { selectedRole } = useRole();

  // Bruk første rad for å bygge URL med parametere
  const firstRow = data[0];
  const {
    data: sessionData,
    error: sessionError,
    isLoading,
  } = useSWR(
    data.length > 0
      ? `/api/candidateSessions/${firstRow.candidateGroupCode}/${firstRow.userId}`
      : null,
    fetcher,
    {
      refreshInterval: POLL_INTERVAL,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  const handleRemoveAccess = async (
    candidateNumber: string,
    sessionId: string,
    userId: string
  ) => {
    try {
      await removeAccess(sessionId, candidateNumber);

      // Send SignalR message to deauthorize user objects (same as "Sperr digital tilgang")
      const deauthorizedResponse = await fetch(
        "/api/deauthorizeUserObjects",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            candidateNumber: candidateNumber,
            // Ingen excludeSessionId - alle sesjoner skal logges ut
          }),
        }
      );

      if (!deauthorizedResponse.ok) {
        throw new Error(
          "Could not deauthorize user objects. Access not removed."
        );
      }

      await ActivityLogger.logActivity(
        OperationEnum.StatusRemovedAccess,
        {
          userId: userId,
          candidateName: candidate?.candidateName || "",
          candidateNumber: candidate?.candidateNumber || candidateNumber,
          candidateRegistrationId:
            candidate?.candidateRegistrationId || candidate?.userId || userId,
        },
        {
          roleName: `${selectedRole?.displayRoleName}`,
        }
      );
      // Logg fjerning av digital tilgang
      await ActivityLogger.logActivity(
        OperationEnum.StatusDigitalAccessRemovedLogout,
        {
          userId: userId,
          candidateName: candidate?.candidateName || "",
          candidateNumber: candidate?.candidateNumber || candidateNumber,
          candidateRegistrationId:
            candidate?.candidateRegistrationId || candidate?.userId || userId,
        },
        {
          roleName: `PGS`,
        }
      );

      batchUpdateCandidateStatus("isAuthorized", {
        [candidateNumber]: false,
      });

      const updatedRedisResult = redisResult.map((session) => {
        if (
          session.candidateNumber === candidateNumber &&
          session.userSessionId === sessionId
        ) {
          return {
            ...session,
            isAuthorized: false,
          };
        }
        return session;
      });

      updateRedisResult(updatedRedisResult);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Feil ved fjerning av tilgang",
        description: "Kunne ikke fjerne tilgang for bruker",
      });
      console.error("Failed to remove access:", error);
    }
  };

  const columns = createColumns(handleRemoveAccess, redisResult, sessionData);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onExpandedChange: setExpanded,
    state: {
      sorting,
      expanded,
    },
  });

  const renderExpandedRow = (row: any) => {
    const { userSessionId } = row.original;
    const filteredSessions =
      sessionData?.filter(
        (session: ICandidateSessionData) => session.SessionId === userSessionId
      ) || [];

    // Sorter sesjoner synkende på LastActive
    const sessions = [...filteredSessions].sort(
      (a, b) =>
        new Date(b.LastActive).getTime() - new Date(a.LastActive).getTime()
    );

    return (
      <TableRow>
        <TableCell colSpan={columns.length + 1} className="p-0">
          <div
            className={`transition-[max-height] duration-300 ease-in-out ${
              row.getIsExpanded() ? "overflow-visible" : "max-h-0"
            }`}
          >
            <div className="p-4">
              {isLoading ? (
                <div className="p-1">Henter sesjonsdata...</div>
              ) : sessionError ? (
                <div className="text-red-500">
                  Feil ved henting av session-data
                </div>
              ) : sessions.length > 0 ? (
                <SessionDetails sessions={sessions} />
              ) : (
                <div>Ingen data tilgjengelig for denne sesjonen.</div>
              )}
            </div>
          </div>
        </TableCell>
      </TableRow>
    );
  };

  return (
    // Main wrapper with width constraints and forced overflow behavior
    <div className="w-full max-w-full overflow-x-auto block">
      <div className="min-w-[1000px]">
        <Table className="w-full table-fixed">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                <TableHead className="w-12"></TableHead>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="whitespace-nowrap">
                    {header.isPlaceholder ? null : (
                      <div
                        {...{
                          "aria-sort": header.column.getIsSorted()
                            ? header.column.getIsSorted() === "desc"
                              ? "descending"
                              : "ascending"
                            : undefined,
                        }}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <React.Fragment key={row.id}>
                  <TableRow
                    tabIndex={0}
                    role="row"
                    data-state={row.getIsSelected() && "selected"}
                    className="hover:bg-slate-100"
                  >
                    <TableCell className="w-12">
                      <button
                        onClick={() => row.toggleExpanded()}
                        className="p-2 hover:bg-slate-200 rounded transition-transform duration-200"
                      >
                        <div
                          className={`transform transition-transform duration-200 ${
                            row.getIsExpanded() ? "rotate-90" : ""
                          }`}
                        >
                          <ChevronRight className="h-4 w-4" />
                        </div>
                      </button>
                    </TableCell>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="whitespace-nowrap">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                  {row.getIsExpanded() && renderExpandedRow(row)}
                </React.Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length + 1}
                  className="h-24 text-center"
                >
                  Ingen brukersesjoner
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
