import { getDbConnection } from "../connection";
import { Besvarelsesdel } from "../models/Besvarelsesdel";
import { Besvarelsesfil } from "../models/Besvarelsesfil";
import { BesvarelsesDelBesvarelsesfil } from "../models/BesvarelsesDelBesvarelsesfil";
import { Kandidatgruppe } from "../models/Kandidatgruppe";
import { Kandidateksamen } from "../models/Kandidateksamen";
import { Fagkodeeksamen } from "../models/Fagkodeeksamen";
import { getAccessToken } from "@/lib/server/getAccessToken";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { IExamDocument } from "@/interface/IExamDocument";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string = process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

interface ImportResult {
  message: string;
  besvarelsesdelerCount: number;
  besvarelsefilerCount: number;
  errorCount: number;
  errorDetails: string[];
}

interface IExamPaperResponse {
  groupCode: string;
  groupName: string;
  subjectCode: string;
  subjectName: string;
  variant: string;
  testPeriod: string;
  numberOfSubmissions: number;
  numberOfCandidatesInGroup: number;
  examStartDate: string;
  examPapers: ICandidateMonitor[];
}

export class BesvarelseService {

  /**
   * Map delivery status from onprem to Azure BesvarelsesdelStatus
   */
  private mapDeliveryStatus(status: number): string | null {
    // Based on the email mapping table
    switch (status) {
      case 0:
      case 1:
      case 2:
        return "Ikke lastet opp";
      case 4:
        return "Sendes i posten";
      case 6:
        return "Levert digitalt";
      case 7:
      case 11:
        // Fravær - should not create Besvarelsesdel, handle at Kandidateksamen level
        return null;
      case 8:
        return "Laster opp";
      default:
        return "Ikke lastet opp";
    }
  }

  /**
   * Map file status from onprem to Azure BesvarelsesfilStatus
   */
  private mapFileStatus(status: number): string {
    // Based on the email - simplified for new solution
    switch (status) {
      case 1:
        return "Opplastet";
      case 2:
        return "Slettet";
      case 3:
        return "Innlevert";
      case 6:
        return "Opplastet"; // Gruppeopplastet, ikke levert
      case 7:
        return "Opplastet"; // UnVerified
      default:
        return "Opplastet";
    }
  }

  /**
   * Get distinct school IDs for an exam period
   */
  private async getDistinctSchoolIds(eksamensperiode: string): Promise<string[]> {
    const dataSource = await getDbConnection();
    const kandidatgruppeRepository = dataSource.getRepository(Kandidatgruppe);

    const result = await kandidatgruppeRepository
      .createQueryBuilder("kg")
      .innerJoin(Fagkodeeksamen, "fke", "kg.FagkodeeksamensID = fke.FagkodeeksamensID")
      .select("DISTINCT kg.SkoleID", "skoleID")
      .where("fke.Eksamensperiode = :eksamensperiode", { eksamensperiode })
      .getRawMany();

    return result.map((r) => r.skoleID);
  }

  /**
   * Fetch exam paper data from PGSR API for a school and period
   */
  private async fetchExamPaperForSchool(
    schoolId: string,
    period: string,
    userId: string,
    accessToken: string
  ): Promise<IExamPaperResponse[]> {
    const apiUrl = `${PgsaApiUrl}/api/ExamPaperInternal/metadata/${userId}/${schoolId}/${period}`;
console.log("apiurl", apiUrl);
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch exam paper for school ${schoolId}: ${response.status}`);
    }

    // Handle 204 No Content or empty response
    if (response.status === 204) {
      return [];
    }

    const text = await response.text();
    console.log(`[DEBUG] API Response for school ${schoolId}: status=${response.status}, text length=${text?.length || 0}`);

    if (!text || text.trim().length === 0) {
      console.log(`[DEBUG] Empty response for school ${schoolId}, returning empty array`);
      return [];
    }

    try {
      const parsed = JSON.parse(text);
      console.log(`[DEBUG] Successfully parsed JSON for school ${schoolId}, items count=${Array.isArray(parsed) ? parsed.length : 'not an array'}`);
      return parsed;
    } catch (error) {
      console.error(`[DEBUG] JSON parse error for school ${schoolId}:`, error);
      throw new Error(`Invalid JSON response for school ${schoolId}: ${text.substring(0, 100)}`);
    }
  }

  /**
   * Main import function for besvarelser
   */
  async importBesvarelser(eksamensperiode: string, userId: string): Promise<ImportResult> {
    const errorDetails: string[] = [];
    let besvarelsesdelerCount = 0;
    let besvarelsefilerCount = 0;
    let errorCount = 0;

    try {
      // Get access token
      const accessToken = await getAccessToken(clientId, clientSecret, scope, accesstokenKey);
      if (!accessToken) {
        throw new Error("Failed to obtain access token");
      }

      // Get distinct school IDs for the exam period
      const schoolIds = await this.getDistinctSchoolIds(eksamensperiode);

      if (schoolIds.length === 0) {
        return {
          message: `Ingen skoler funnet for eksamensperiode ${eksamensperiode}. Importer kandidatgrupper først.`,
          besvarelsesdelerCount: 0,
          besvarelsefilerCount: 0,
          errorCount: 0,
          errorDetails: [],
        };
      }

      console.log(`[DEBUG] Found ${schoolIds.length} schools for period ${eksamensperiode}:`, schoolIds);

      // Loop through each school and fetch exam paper data
      for (const schoolId of schoolIds) {
        try {
          const examPapers = await this.fetchExamPaperForSchool(
            schoolId,
            eksamensperiode,
            userId,
            accessToken
          );

          console.log(`[DEBUG] School ${schoolId}: Retrieved ${examPapers.length} exam papers`);
          if (examPapers.length > 0) {
            console.log(`[DEBUG] First exam paper:`, JSON.stringify(examPapers[0], null, 2));
          }

          // Process each exam group
          for (const examGroup of examPapers) {
            // Validate that examPapers is an array
            if (!Array.isArray(examGroup.examPapers)) {
              console.error(`[DEBUG] examGroup.examPapers is not an array for school ${schoolId}:`, typeof examGroup.examPapers);
              errorCount++;
              errorDetails.push(`Ugyldig data struktur for skole ${schoolId}: examPapers er ikke en array`);
              continue;
            }

            console.log(`[DEBUG] Processing ${examGroup.examPapers.length} candidates for group ${examGroup.groupCode}`);

            // Process each candidate
            for (const candidate of examGroup.examPapers) {
              try {
                console.log(`[DEBUG] Processing candidate ${candidate.candidateNumber}, documents count: ${candidate.documents?.length || 0}`);
                await this.processCandidateBesvarelser(candidate, examGroup.groupCode);
                besvarelsesdelerCount += 2; // Assuming max 2 parts
                besvarelsefilerCount += candidate.documents?.length || 0;
              } catch (error) {
                errorCount++;
                const errorMsg = `Feil ved prosessering av kandidat ${candidate.candidateNumber} (påmeldingsID: ${candidate.candidateRegistrationId || 'ikke tilgjengelig'}, groupCode: ${examGroup.groupCode}): ${
                  error instanceof Error ? error.message : "Ukjent feil"
                }`;
                errorDetails.push(errorMsg);
                console.error(errorMsg);
              }
            }
          }
        } catch (error) {
          errorCount++;
          const errorMsg = `Feil ved henting av data for skole ${schoolId}: ${
            error instanceof Error ? error.message : "Ukjent feil"
          }`;
          errorDetails.push(errorMsg);
          console.error(errorMsg);
        }
      }

      return {
        message: `Importert besvarelser for ${schoolIds.length} skoler. ${besvarelsesdelerCount} deler, ${besvarelsefilerCount} filer.`,
        besvarelsesdelerCount,
        besvarelsefilerCount,
        errorCount,
        errorDetails,
      };
    } catch (error) {
      throw new Error(
        `Kritisk feil under import av besvarelser: ${
          error instanceof Error ? error.message : "Ukjent feil"
        }`
      );
    }
  }

  /**
   * Process besvarelser for a single candidate
   */
  private async processCandidateBesvarelser(candidate: ICandidateMonitor, groupCode: string): Promise<void> {
    const dataSource = await getDbConnection();
    const kandidateksamenRepository = dataSource.getRepository(Kandidateksamen);

    console.log(`[DEBUG] Looking for kandidat: candidateNumber=${candidate.candidateNumber}, candidateRegistrationId=${candidate.candidateRegistrationId}, groupCode=${groupCode}`);

    // Try to find Kandidateksamen by candidateRegistrationId first, then by candidateNumber
    let kandidateksamen = null;

    if (candidate.candidateRegistrationId) {
      kandidateksamen = await kandidateksamenRepository.findOne({
        where: { KandidatpameldingsID: candidate.candidateRegistrationId },
      });
      console.log(`[DEBUG] Search by KandidatpameldingsID '${candidate.candidateRegistrationId}': ${kandidateksamen ? 'FOUND' : 'NOT FOUND'}`);
    }

    // Fallback to candidateNumber if registrationId not found
    if (!kandidateksamen && candidate.candidateNumber) {
      kandidateksamen = await kandidateksamenRepository.findOne({
        where: { Kandidatnummer: candidate.candidateNumber },
      });
      console.log(`[DEBUG] Search by Kandidatnummer '${candidate.candidateNumber}': ${kandidateksamen ? 'FOUND' : 'NOT FOUND'}`);
    }

    if (!kandidateksamen) {
      // Log what we have in the database for this group to help debug
      const kandidaterInGroup = await kandidateksamenRepository
        .createQueryBuilder("ke")
        .innerJoin("Kandidatgruppe", "kg", "ke.KandidatgruppeID = kg.KandidatgruppeID")
        .where("kg.Kandidatgruppekode = :groupCode", { groupCode })
        .select(["ke.Kandidatnummer", "ke.KandidatpameldingsID"])
        .limit(5)
        .getRawMany();

      console.error(`[DEBUG] Sample kandidater in group ${groupCode}:`, kandidaterInGroup);

      throw new Error(
        `Kandidateksamen ikke funnet for kandidat ${candidate.candidateNumber} ` +
        `(påmeldingsID: ${candidate.candidateRegistrationId || 'ikke tilgjengelig'}, ` +
        `groupCode: ${groupCode}). Sjekk at kandidatgrupper er importert først.`
      );
    }

    console.log(`[DEBUG] Found KandidateksamenID: ${kandidateksamen.KandidateksamenID}`);

    // Process Part 1
    if (candidate.deliveryStatusPart1 !== 7 && candidate.deliveryStatusPart1 !== 11) {
      // Not fravær
      await this.processBesvarelsesdel(
        kandidateksamen.KandidateksamenID,
        1,
        candidate.deliveryStatusPart1,
        candidate.documents.filter((doc) => doc.testPartId === 1)
      );
    }

    // Process Part 2 (if exists)
    if (candidate.deliveryStatusPart2 !== undefined &&
        candidate.deliveryStatusPart2 !== 7 &&
        candidate.deliveryStatusPart2 !== 11) {
      await this.processBesvarelsesdel(
        kandidateksamen.KandidateksamenID,
        2,
        candidate.deliveryStatusPart2,
        candidate.documents.filter((doc) => doc.testPartId === 2)
      );
    }
  }

  /**
   * Process a single besvarelsesdel (exam part)
   */
  private async processBesvarelsesdel(
    kandidateksamenID: number,
    testPartId: number,
    deliveryStatus: number,
    documents: IExamDocument[]
  ): Promise<void> {
    const dataSource = await getDbConnection();
    const besvarelsesdelRepository = dataSource.getRepository(Besvarelsesdel);

    const besvarelsesdelStatus = this.mapDeliveryStatus(deliveryStatus);

    if (!besvarelsesdelStatus) {
      // Skip if status indicates fravær
      console.log(`[DEBUG] Skipping besvarelsesdel for kandidateksamen ${kandidateksamenID} del ${testPartId}: fravær status ${deliveryStatus}`);
      return;
    }

    // Generate BesvarelsesdelID (using kandidateksamenID + part)
    const besvarelsesdelID = `${kandidateksamenID}-DEL${testPartId}`;
    console.log(`[DEBUG] Processing besvarelsesdel ${besvarelsesdelID}, documents count: ${documents.length}`);

    // Create or update Besvarelsesdel
    let besvarelsesdel = await besvarelsesdelRepository.findOne({
      where: { BesvarelsesdelID: besvarelsesdelID },
    });

    if (!besvarelsesdel) {
      besvarelsesdel = new Besvarelsesdel();
      besvarelsesdel.BesvarelsesdelID = besvarelsesdelID;
      besvarelsesdel.KandidateksamenID = kandidateksamenID;
    }

    besvarelsesdel.BesvarelsesdelType = `Del ${testPartId}`;
    besvarelsesdel.BesvarelsesdelStatus = besvarelsesdelStatus;
    besvarelsesdel.Gjennomforingmodus = undefined; // Not available from API

    await besvarelsesdelRepository.save(besvarelsesdel);

    // Process documents/files
    for (const document of documents) {
      await this.processBesvarelsesfil(besvarelsesdelID, document);
    }
  }

  /**
   * Process a single besvarelsesfil (document/file)
   */
  private async processBesvarelsesfil(
    besvarelsesdelID: string,
    document: IExamDocument
  ): Promise<void> {
    const dataSource = await getDbConnection();
    const besvarelsesfilRepository = dataSource.getRepository(Besvarelsesfil);
    const junctionRepository = dataSource.getRepository(BesvarelsesDelBesvarelsesfil);

    // Besvarelseskode is the document GUID, not the filename
    const besvarelseskode = document.documentCode;
    const besvarelsesfilStatus = this.mapFileStatus(document.status);

    // UPSERT: Find existing file by Besvarelseskode (document GUID)
    let besvarelsesfil = await besvarelsesfilRepository.findOne({
      where: { Besvarelseskode: besvarelseskode },
    });

    if (!besvarelsesfil) {
      // INSERT: Create new entry
      besvarelsesfil = new Besvarelsesfil();
      besvarelsesfil.Besvarelseskode = besvarelseskode;
    }
    // If exists, this becomes an UPDATE

    // Set/update file metadata
    besvarelsesfil.OriginalNavn = document.originalFileName || document.fileName;
    besvarelsesfil.StandardNavn = document.fileName;
    besvarelsesfil.FileSize = document.fileSize || undefined;
    besvarelsesfil.BesvarelsesfilStatus = besvarelsesfilStatus;
    besvarelsesfil.LasteOppTimeStamp = new Date(document.timestamp);
    besvarelsesfil.LevertTimeStamp = new Date(document.timestamp);
    besvarelsesfil.LasteOppAv = document.uploadedBy;
    besvarelsesfil.LevertAv = document.uploadedBy;
    besvarelsesfil.LasteOppVia = "PGSR";
    besvarelsesfil.LevertVia = "PGSR";
    // MimeType not available from API

    await besvarelsesfilRepository.save(besvarelsesfil);

    // Note: DELETE operations from external API are not currently handled
    // Files that are deleted in PGSR will remain in database with last known status

    // Create junction table entry using the auto-generated BesvarelsesfilID
    let junction = await junctionRepository.findOne({
      where: {
        BesvarelsesdelID: besvarelsesdelID,
        BesvarelsesfilID: besvarelsesfil.BesvarelsesfilID,
      },
    });

    if (!junction) {
      junction = new BesvarelsesDelBesvarelsesfil();
      junction.BesvarelsesdelID = besvarelsesdelID;
      junction.BesvarelsesfilID = besvarelsesfil.BesvarelsesfilID;
      await junctionRepository.save(junction);
    }
  }

  /**
   * Get innleveringsoversikt statistics for an exam period
   * Optimized for large datasets (100k+ besvarelser)
   */
  async getInnleveringsoversikt(eksamensperiode: string, fraDato?: Date, tilDato?: Date): Promise<{
    totalBesvarelsesdeler: number;
    totalLeverteBesvarelsesdeler: number;
    totalKandidaterMedLevering: number;
    totalKandidater: number;
    totaltForventetKandidater: number;
    gjennomforingsgradProsent: number;
    perFagkode: Array<{
      fagkode: string;
      fagkodenavn: string;
      antallBesvarelsesdeler: number;
      antallLeverteBesvarelsesdeler: number;
      antallKandidaterMedLevering: number;
      antallKandidater: number;
      forventetAntallKandidater: number;
      gjennomforingsgradProsent: number;
    }>;
    statusOversikt: Array<{
      status: string;
      antallKandidater: number;
      prosent: number;
    }>;
    innleveringerPerTime: Array<{
      time: string;
      antall: number;
      prosent: number;
    }>;
  }> {
    const dataSource = await getDbConnection();

    // Main query: Count besvarelser and kandidater per fagkode
    const perFagkodeQuery = `
      SELECT
        fke.Fagkode,
        fke.Fagnavn,
        COUNT(DISTINCT bd.BesvarelsesdelID) as AntallBesvarelsesdeler,
        COUNT(DISTINCT CASE
          WHEN bd.BesvarelsesdelStatus IN ('Levert digitalt', 'Sendes i posten')
          THEN bd.BesvarelsesdelID
        END) as AntallLeverteBesvarelsesdeler,
        COUNT(DISTINCT CASE
          WHEN bd.BesvarelsesdelStatus IN ('Levert digitalt', 'Sendes i posten')
          THEN ke.KandidateksamenID
        END) as AntallKandidaterMedLevering,
        COUNT(DISTINCT ke.KandidateksamenID) as AntallKandidater,
        COUNT(DISTINCT kg.KandidatgruppeID) as AntallGrupper
      FROM Kandidatgruppe kg
      INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
      LEFT JOIN Kandidateksamen ke ON ke.KandidatgruppeID = kg.KandidatgruppeID
      LEFT JOIN Besvarelsesdel bd ON bd.KandidateksamenID = ke.KandidateksamenID
      WHERE fke.Eksamensperiode = @0
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
      GROUP BY fke.Fagkode, fke.Fagnavn
      ORDER BY fke.Fagkode
    `;

    const perFagkodeResults = await dataSource.query(perFagkodeQuery, [
      eksamensperiode,
      ...(fraDato && tilDato ? [fraDato] : []),
      ...(fraDato && tilDato ? [tilDato] : []),
    ]);

    // Count expected candidates from Kandidateksamen
    const forventetQuery = `
      SELECT
        fke.Fagkode,
        COUNT(DISTINCT ke.KandidateksamenID) as ForventetAntall
      FROM Kandidatgruppe kg
      INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
      INNER JOIN Kandidateksamen ke ON ke.KandidatgruppeID = kg.KandidatgruppeID
      LEFT JOIN Besvarelsesdel bd ON bd.KandidateksamenID = ke.KandidateksamenID
      LEFT JOIN BesvarelsesDelBesvarelsesfil bdbf ON bd.BesvarelsesdelID = bdbf.BesvarelsesdelID
      LEFT JOIN Besvarelsesfil bf ON bdbf.BesvarelsesfilID = bf.BesvarelsesfilID
      WHERE fke.Eksamensperiode = @0
      ${fraDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
      ${tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
      GROUP BY fke.Fagkode
    `;

    const forventetResults = await dataSource.query(forventetQuery, [
      eksamensperiode,
      ...(fraDato ? [fraDato] : []),
      ...(tilDato ? [tilDato] : []),
    ]);
    const forventetMap = new Map<string, number>(
      forventetResults.map((r: any) => [r.Fagkode, parseInt(r.ForventetAntall)])
    );

    // Status oversikt - gruppere kandidater basert på deres status
    // En kandidat telles kun én gang basert på deres "høyeste" status
    const statusQuery = `
      WITH AlleKandidater AS (
        -- Get all candidates for the period
        SELECT DISTINCT ke.KandidateksamenID
        FROM Kandidateksamen ke
        INNER JOIN Kandidatgruppe kg ON ke.KandidatgruppeID = kg.KandidatgruppeID
        INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
        LEFT JOIN Besvarelsesdel bd ON bd.KandidateksamenID = ke.KandidateksamenID
        LEFT JOIN BesvarelsesDelBesvarelsesfil bdbf ON bd.BesvarelsesdelID = bdbf.BesvarelsesdelID
        LEFT JOIN Besvarelsesfil bf ON bdbf.BesvarelsesfilID = bf.BesvarelsesfilID
        WHERE fke.Eksamensperiode = @0
        ${fraDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
        ${tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
      ),
      KandidatStatus AS (
        SELECT
          ke.KandidateksamenID,
          MAX(CASE WHEN bd.BesvarelsesdelStatus = 'Levert digitalt' THEN 1 ELSE 0 END) as HarLevertDigitalt,
          MAX(CASE WHEN bd.BesvarelsesdelStatus = 'Sendes i posten' THEN 1 ELSE 0 END) as HarSendesIPosten,
          MAX(CASE WHEN bd.BesvarelsesdelStatus = 'Laster opp' THEN 1 ELSE 0 END) as HarLasterOpp,
          MAX(CASE WHEN bd.BesvarelsesdelStatus = 'Ikke lastet opp' THEN 1 ELSE 0 END) as HarIkkeLastetOpp
        FROM Besvarelsesdel bd
        INNER JOIN Kandidateksamen ke ON bd.KandidateksamenID = ke.KandidateksamenID
        INNER JOIN Kandidatgruppe kg ON ke.KandidatgruppeID = kg.KandidatgruppeID
        INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
        LEFT JOIN BesvarelsesDelBesvarelsesfil bdbf ON bd.BesvarelsesdelID = bdbf.BesvarelsesdelID
        LEFT JOIN Besvarelsesfil bf ON bdbf.BesvarelsesfilID = bf.BesvarelsesfilID
        WHERE fke.Eksamensperiode = @0 AND bd.BesvarelsesdelStatus IS NOT NULL
        ${fraDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
        ${tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
        GROUP BY ke.KandidateksamenID
      ),
      StatusGruppe AS (
        SELECT
          ak.KandidateksamenID,
          CASE
            WHEN ks.HarLevertDigitalt = 1 THEN 'Levert digitalt'
            WHEN ks.HarSendesIPosten = 1 THEN 'Sendes i posten'
            WHEN ks.HarLasterOpp = 1 THEN 'Laster opp'
            WHEN ks.HarIkkeLastetOpp = 1 THEN 'Ikke lastet opp'
            ELSE 'Ingen besvarelse'
          END as Status
        FROM AlleKandidater ak
        LEFT JOIN KandidatStatus ks ON ak.KandidateksamenID = ks.KandidateksamenID
      )
      SELECT
        Status,
        COUNT(*) as AntallKandidater
      FROM StatusGruppe
      GROUP BY Status
      ORDER BY
        CASE Status
          WHEN 'Levert digitalt' THEN 1
          WHEN 'Sendes i posten' THEN 2
          WHEN 'Laster opp' THEN 3
          WHEN 'Ikke lastet opp' THEN 4
          WHEN 'Ingen besvarelse' THEN 5
        END
    `;

    const statusResults = await dataSource.query(statusQuery, [
      eksamensperiode,
      ...(fraDato ? [fraDato] : []),
      ...(tilDato ? [tilDato] : []),
    ]);

    // Innleveringer per time
    const innleveringerPerTimeQuery = `
      SELECT
        FORMAT(DATEADD(minute, DATEDIFF(minute, 0, bf.LevertTimeStamp) / 30 * 30, 0), 'HH:mm') as Time,
        COUNT(DISTINCT bf.BesvarelsesfilID) as Antall
      FROM Besvarelsesfil bf
      INNER JOIN BesvarelsesDelBesvarelsesfil bdbf ON bf.BesvarelsesfilID = bdbf.BesvarelsesfilID
      INNER JOIN Besvarelsesdel bd ON bdbf.BesvarelsesdelID = bd.BesvarelsesdelID
      INNER JOIN Kandidateksamen ke ON bd.KandidateksamenID = ke.KandidateksamenID
      INNER JOIN Kandidatgruppe kg ON ke.KandidatgruppeID = kg.KandidatgruppeID
      INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
      WHERE fke.Eksamensperiode = @0 AND bf.LevertTimeStamp IS NOT NULL
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
      GROUP BY FORMAT(DATEADD(minute, DATEDIFF(minute, 0, bf.LevertTimeStamp) / 30 * 30, 0), 'HH:mm')
      ORDER BY Time;
    `;

    const innleveringerPerTimeResults = await dataSource.query(innleveringerPerTimeQuery, [
      eksamensperiode,
      ...(fraDato ? [fraDato] : []),
      ...(tilDato ? [tilDato] : []),
    ]);

    // Calculate totals and percentages
    const totalBesvarelsesdeler: number = perFagkodeResults.reduce(
      (sum: number, r: any) => sum + parseInt(r.AntallBesvarelsesdeler || 0),
      0
    );
    const totalLeverteBesvarelsesdeler: number = perFagkodeResults.reduce(
      (sum: number, r: any) => sum + parseInt(r.AntallLeverteBesvarelsesdeler || 0),
      0
    );
    const totalKandidaterMedLevering: number = perFagkodeResults.reduce(
      (sum: number, r: any) => sum + parseInt(r.AntallKandidaterMedLevering || 0),
      0
    );
    const totalKandidater: number = perFagkodeResults.reduce(
      (sum: number, r: any) => sum + parseInt(r.AntallKandidater || 0),
      0
    );
    const totaltForventetKandidater: number = Array.from(forventetMap.values()).reduce(
      (sum: number, count: number) => sum + count,
      0
    );

    const totalStatusCount: number = statusResults.reduce(
      (sum: number, r: any) => sum + parseInt(r.AntallKandidater),
      0
    );

    return {
      totalBesvarelsesdeler,
      totalLeverteBesvarelsesdeler,
      totalKandidaterMedLevering,
      totalKandidater,
      totaltForventetKandidater,
      gjennomforingsgradProsent: totaltForventetKandidater > 0
        ? Math.round((totalKandidaterMedLevering / totaltForventetKandidater) * 100)
        : 0,
      perFagkode: perFagkodeResults.map((r: any) => {
        const forventet = forventetMap.get(r.Fagkode) || 0;
        const antallKandidaterMedLevering = parseInt(r.AntallKandidaterMedLevering || 0);
        return {
          fagkode: r.Fagkode,
          fagkodenavn: r.Fagnavn,
          antallBesvarelsesdeler: parseInt(r.AntallBesvarelsesdeler || 0),
          antallLeverteBesvarelsesdeler: parseInt(r.AntallLeverteBesvarelsesdeler || 0),
          antallKandidaterMedLevering,
          antallKandidater: parseInt(r.AntallKandidater || 0),
          forventetAntallKandidater: forventet,
          gjennomforingsgradProsent: forventet > 0
            ? Math.round((antallKandidaterMedLevering / forventet) * 100)
            : 0,
        };
      }),
      statusOversikt: statusResults.map((r: any) => ({
        status: r.Status,
        antallKandidater: parseInt(r.AntallKandidater || 0),
        prosent: totalStatusCount > 0
          ? Math.round((parseInt(r.AntallKandidater) / totalStatusCount) * 100)
          : 0,
      })),
      innleveringerPerTime: innleveringerPerTimeResults.map((r: any) => ({
        time: r.Time,
        antall: parseInt(r.Antall),
        prosent: totalLeverteBesvarelsesdeler > 0
          ? Math.round((parseInt(r.Antall) / totalLeverteBesvarelsesdeler) * 100)
          : 0,
      })),
    };
  }

  /**
   * Get filstatistikk for an exam period
   * Optimized for large datasets (100k+ besvarelser)
   */
  async getFilstatistikk(eksamensperiode: string, fraDato?: Date, tilDato?: Date): Promise<{
    totaltAntallFiler: number;
    totalFilstorrelse: string; // in bytes as string
    totalFilstorrelseMB: number;
    gjennomsnittligAntallFilerPerKandidat: number;
    gjennomsnittligFilstorrelse: string; // in bytes as string
    gjennomsnittligFilstorrelseMB: number;
    filtyper: Array<{
      filtype: string;
      antall: number;
      prosent: number;
    }>;
    filstorrelseDistribusjon: Array<{
      kategori: string;
      antall: number;
      prosent: number;
    }>;
  }> {
    const dataSource = await getDbConnection();

    // Main statistics query
    const statsQuery = `
      SELECT
        COUNT(bf.BesvarelsesfilID) as TotaltAntallFiler,
        ISNULL(SUM(CAST(bf.FileSize AS BIGINT)), 0) as TotalFilstorrelse,
        COUNT(DISTINCT ke.KandidateksamenID) as AntallKandidater
      FROM Besvarelsesfil bf
      INNER JOIN BesvarelsesDelBesvarelsesfil bdbf ON bf.BesvarelsesfilID = bdbf.BesvarelsesfilID
      INNER JOIN Besvarelsesdel bd ON bdbf.BesvarelsesdelID = bd.BesvarelsesdelID
      INNER JOIN Kandidateksamen ke ON bd.KandidateksamenID = ke.KandidateksamenID
      INNER JOIN Kandidatgruppe kg ON ke.KandidatgruppeID = kg.KandidatgruppeID
      INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
      WHERE fke.Eksamensperiode = @0
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
    `;

    const statsResult = await dataSource.query(statsQuery, [
      eksamensperiode,
      ...(fraDato ? [fraDato] : []),
      ...(tilDato ? [tilDato] : []),
    ]);

    // File types distribution (extract from StandardNavn or OriginalNavn)
    const filtyperQuery = `
      SELECT
        UPPER(RIGHT(COALESCE(bf.StandardNavn, bf.OriginalNavn, 'unknown'),
          CHARINDEX('.', REVERSE(COALESCE(bf.StandardNavn, bf.OriginalNavn, 'unknown')))-1)) as Filtype,
        COUNT(*) as Antall
      FROM Besvarelsesfil bf
      INNER JOIN BesvarelsesDelBesvarelsesfil bdbf ON bf.BesvarelsesfilID = bdbf.BesvarelsesfilID
      INNER JOIN Besvarelsesdel bd ON bdbf.BesvarelsesdelID = bd.BesvarelsesdelID
      INNER JOIN Kandidateksamen ke ON bd.KandidateksamenID = ke.KandidateksamenID
      INNER JOIN Kandidatgruppe kg ON ke.KandidatgruppeID = kg.KandidatgruppeID
      INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
      WHERE fke.Eksamensperiode = @0
        AND COALESCE(bf.StandardNavn, bf.OriginalNavn) IS NOT NULL
        AND CHARINDEX('.', COALESCE(bf.StandardNavn, bf.OriginalNavn)) > 0
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
      GROUP BY UPPER(RIGHT(COALESCE(bf.StandardNavn, bf.OriginalNavn, 'unknown'),
        CHARINDEX('.', REVERSE(COALESCE(bf.StandardNavn, bf.OriginalNavn, 'unknown')))-1))
      ORDER BY Antall DESC
    `;

    const filtyperResults = await dataSource.query(filtyperQuery, [
      eksamensperiode,
      ...(fraDato ? [fraDato] : []),
      ...(tilDato ? [tilDato] : []),
    ]);

    // Filstørrelse-distribusjon
    const filstorrelseDistribusjonQuery = `
      SELECT
        CASE
          WHEN CAST(bf.FileSize AS BIGINT) < 1048576 THEN '< 1 MB'
          WHEN CAST(bf.FileSize AS BIGINT) < 5242880 THEN '1-5 MB'
          WHEN CAST(bf.FileSize AS BIGINT) < 10485760 THEN '5-10 MB'
          WHEN CAST(bf.FileSize AS BIGINT) < 26214400 THEN '10-25 MB'
          ELSE '> 25 MB'
        END as Kategori,
        COUNT(*) as Antall
      FROM Besvarelsesfil bf
      INNER JOIN BesvarelsesDelBesvarelsesfil bdbf ON bf.BesvarelsesfilID = bdbf.BesvarelsesfilID
      INNER JOIN Besvarelsesdel bd ON bdbf.BesvarelsesdelID = bd.BesvarelsesdelID
      INNER JOIN Kandidateksamen ke ON bd.KandidateksamenID = ke.KandidateksamenID
      INNER JOIN Kandidatgruppe kg ON ke.KandidatgruppeID = kg.KandidatgruppeID
      INNER JOIN Fagkodeeksamen fke ON kg.FagkodeeksamensID = fke.FagkodeeksamensID
      WHERE fke.Eksamensperiode = @0 AND bf.FileSize IS NOT NULL AND bf.FileSize > 0
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp >= @1' : ''}
      ${fraDato && tilDato ? 'AND bf.LevertTimeStamp < @2' : ''}
      GROUP BY
        CASE
          WHEN CAST(bf.FileSize AS BIGINT) < 1048576 THEN '< 1 MB'
          WHEN CAST(bf.FileSize AS BIGINT) < 5242880 THEN '1-5 MB'
          WHEN CAST(bf.FileSize AS BIGINT) < 10485760 THEN '5-10 MB'
          WHEN CAST(bf.FileSize AS BIGINT) < 26214400 THEN '10-25 MB'
          ELSE '> 25 MB'
        END
      ORDER BY
        CASE
          WHEN CASE
            WHEN CAST(bf.FileSize AS BIGINT) < 1048576 THEN '< 1 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 5242880 THEN '1-5 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 10485760 THEN '5-10 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 26214400 THEN '10-25 MB'
            ELSE '> 25 MB'
          END = '< 1 MB' THEN 1
          WHEN CASE
            WHEN CAST(bf.FileSize AS BIGINT) < 1048576 THEN '< 1 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 5242880 THEN '1-5 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 10485760 THEN '5-10 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 26214400 THEN '10-25 MB'
            ELSE '> 25 MB'
          END = '1-5 MB' THEN 2
          WHEN CASE
            WHEN CAST(bf.FileSize AS BIGINT) < 1048576 THEN '< 1 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 5242880 THEN '1-5 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 10485760 THEN '5-10 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 26214400 THEN '10-25 MB'
            ELSE '> 25 MB'
          END = '5-10 MB' THEN 3
          WHEN CASE
            WHEN CAST(bf.FileSize AS BIGINT) < 1048576 THEN '< 1 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 5242880 THEN '1-5 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 10485760 THEN '5-10 MB'
            WHEN CAST(bf.FileSize AS BIGINT) < 26214400 THEN '10-25 MB'
            ELSE '> 25 MB'
          END = '10-25 MB' THEN 4
          ELSE 5
        END
    `;

    const filstorrelseDistribusjonResults = await dataSource.query(filstorrelseDistribusjonQuery, [
      eksamensperiode,
      ...(fraDato ? [fraDato] : []),
      ...(tilDato ? [tilDato] : []),
    ]);

    // Parse results
    const totaltAntallFiler = parseInt(statsResult[0]?.TotaltAntallFiler || 0);
    const totalFilstorrelse = statsResult[0]?.TotalFilstorrelse?.toString() || "0";
    const antallKandidater = parseInt(statsResult[0]?.AntallKandidater || 0);

    const totalFilstorrelseMB = Math.round(parseInt(totalFilstorrelse) / (1024 * 1024));
    const gjennomsnittligFilstorrelse = totaltAntallFiler > 0
      ? Math.floor(parseInt(totalFilstorrelse) / totaltAntallFiler).toString()
      : "0";
    const gjennomsnittligFilstorrelseMB = totaltAntallFiler > 0
      ? Math.round((parseInt(totalFilstorrelse) / totaltAntallFiler) / (1024 * 1024) * 100) / 100
      : 0;

    return {
      totaltAntallFiler,
      totalFilstorrelse,
      totalFilstorrelseMB,
      gjennomsnittligAntallFilerPerKandidat: antallKandidater > 0
        ? Math.round((totaltAntallFiler / antallKandidater) * 100) / 100
        : 0,
      gjennomsnittligFilstorrelse,
      gjennomsnittligFilstorrelseMB,
      filtyper: filtyperResults.map((r: any) => ({
        filtype: r.Filtype || "unknown",
        antall: parseInt(r.Antall),
        prosent: totaltAntallFiler > 0
          ? Math.round((parseInt(r.Antall) / totaltAntallFiler) * 100)
          : 0,
      })),
      filstorrelseDistribusjon: filstorrelseDistribusjonResults.map((r: any) => ({
        kategori: r.Kategori,
        antall: parseInt(r.Antall),
        prosent: totaltAntallFiler > 0
          ? Math.round((parseInt(r.Antall) / totaltAntallFiler) * 100)
          : 0,
      })),
    };
  }
}
