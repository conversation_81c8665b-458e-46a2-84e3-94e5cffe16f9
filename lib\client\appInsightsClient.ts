"use client";

import { ApplicationInsights } from "@microsoft/applicationinsights-web";

let appInsights: ApplicationInsights | null = null;

/**
 * Initialiser Application Insights
 */
async function initAppInsights(): Promise<ApplicationInsights | null> {
  if (appInsights === null) {
    try {
      const response = await fetch(
        `${window.location.origin}/api/getappinsightsconnection`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch connection string");
      }
      const data = await response.json();

      if (!data.connectionString) {
        throw new Error("Application Insights connection string is missing.");
      }

      appInsights = new ApplicationInsights({
        config: {
          connectionString: data.connectionString,
          enableAutoRouteTracking: true,
          disableAjaxTracking: false,
          disableFetchTracking: false,
          enableCorsCorrelation: true,
          enableRequestHeaderTracking: false,
          enableResponseHeaderTracking: false,
          samplingPercentage: 100,
        },
      });

      appInsights.loadAppInsights();

      // Legg til en telemetryInitializer for å ekskludere unntak fra sampling
      appInsights.addTelemetryInitializer((envelope) => {
        if (envelope.baseData?.baseType === "ExceptionData") {
          // Unntak skal alltid logges (ikke sample disse)
          return true; // Behold denne telemetrihendelsen
        } else {
          // Vanlige hendelser skal samples (70 % sannsynlighet)
          return Math.random() < 0.7; // Behold 70 % av disse hendelsene
        }
      });
    } catch (error) {
      console.error("Error initializing Application Insights:", error);
      return null;
    }
  }
  return appInsights;
}

/**
 * Hent Application Insights-klienten
 */
export async function getAppInsights(): Promise<ApplicationInsights | null> {
  if (!appInsights) {
    return await initAppInsights();
  }
  return appInsights;
}

/**
 * Fjern brukerkontekst for Application Insights
 */
export async function clearUserContext(): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.clearAuthenticatedUserContext();
    console.log("User context cleared from Application Insights.");
  } else {
    console.error("Application Insights is not initialized.");
  }
}

/**
 * Logg en egendefinert hendelse
 */
export async function logEvent(
  name: string,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackEvent({ name, properties });
  } else {
    console.error("Application Insights is not initialized.");
  }
}

/**
 * Logg en metrikk
 */
export async function logMetric(
  name: string,
  average: number,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackMetric({ name, average, properties });
  } else {
    console.error("Application Insights is not initialized.");
  }
}

/**
 * Logg feil
 */
export async function logException(
  error: Error,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackException({ exception: error, properties });
  } else {
    console.error("Application Insights is not initialized.");
  }
}

export async function logPageView(
  name?: string,
  uri?: string,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackPageView({
      name: name || document.title,
      uri: uri || window.location.pathname,
      properties,
    });
  }
}
