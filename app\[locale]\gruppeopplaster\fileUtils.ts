import { IUploadedFile } from "@/interface/IUploadedFile";
import { IRoleObject } from "@/interface/IRoleObject";
import { FileRejection } from "react-dropzone";
import { ValidationService, FileValidationOptions } from "@/lib/shared/ValidationService";

export const handleAcceptedFiles = async (
  files: File[],
  existingFiles: IUploadedFile[],
  addFile: (file: IUploadedFile) => Promise<void>,
  mimeTypes: IAllowedMimeTypes[],
  selectedRoleSchoolId: string
): Promise<boolean> => {
  let { totalSize, totalFiles } = ValidationService.updateTotalSizeAndFiles(existingFiles);
  let allFilesValid = true;

  const validationOptions: FileValidationOptions = {
    existingFiles,
    mimeTypes,
    selectedRoleSchoolId,
  };

  for (const file of files) {
    const validationResult = await ValidationService.validateAcceptedFile(
      file,
      validationOptions
    );

    await addFile(validationResult.file);

    if (validationResult.isValid) {
      totalSize += file.size;
      totalFiles += 1;
    } else {
      allFilesValid = false;
    }
  }

  return allFilesValid;
};

export const handleRejectedFile = async (
  rejection: FileRejection,
  addFile: (file: IUploadedFile) => Promise<void>
): Promise<void> => {
  const rejectedFile = await ValidationService.validateRejectedFile(rejection);
  await addFile(rejectedFile);
};

export const fileValidator = (
  file: File,
  mimeTypes: IAllowedMimeTypes[],
  uploadedFiles: IUploadedFile[],
  selectedRole: IRoleObject | undefined
): any[] | null => {
  const errors = ValidationService.validateFileGeneral(
    file,
    mimeTypes,
    uploadedFiles,
    selectedRole?.selectedSchoolId
  );

  return errors;
};

export const deliverFiles = async (
  uploadedFiles: IUploadedFile[],
  deliverFile: (file: IUploadedFile) => Promise<void>,
  selectedSchoolId: string | undefined
): Promise<boolean> => {
  let fileMissingExamPart = false;

  const promises = uploadedFiles
    .filter(
      (file) =>
        !file.IsRejected &&
        !file.Delivered &&
        file.SchoolId === selectedSchoolId
    )
    .map(async (file) => {
      if (file.TestPartId === 0) {
        fileMissingExamPart = true;
      } else {
        try {
          await deliverFile(file);
        } catch (error) {
          console.error(`Error delivering file ${file.Name}:`, error);
        }
      }
    });

  await Promise.all(promises);
  return fileMissingExamPart;
};

// Re-export utility functions from ValidationService for backward compatibility
export const getCandidateNumber = ValidationService.getCandidateNumber;
export const getMimeTypeFromFile = ValidationService.getMimeTypeFromFile;
export const updateTotalSizeAndFiles = ValidationService.updateTotalSizeAndFiles;
export const getFileCount = ValidationService.getFileCount;
