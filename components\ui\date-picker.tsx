"use client"

import * as React from "react"
import { format } from "date-fns"
import { nb } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"

import { cn } from "@/lib/shared/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  /**
   * The selected date.
   */
  date?: Date
  /**
   * Event handler called when the user selects a date.
   */
  onSelect?: (date: Date | undefined) => void
  /**
   * The placeholder text to display when no date is selected.
   */
  placeholder?: string
  /**
   * Whether the date picker is disabled.
   */
  disabled?: boolean
  /**
   * Additional CSS classes to apply to the trigger button.
   */
  className?: string
  /**
   * Whether to show the clear button when a date is selected.
   */
  showClear?: boolean
  /**
   * Event handler called when the clear button is clicked.
   */
  onClear?: () => void
  /**
   * Minimum selectable date.
   */
  minDate?: Date
  /**
   * Maximum selectable date.
   */
  maxDate?: Date
}

export function DatePicker({
  date,
  onSelect,
  placeholder = "Velg dato",
  disabled = false,
  className,
  showClear = false,
  onClear,
  minDate,
  maxDate,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (selectedDate: Date | undefined) => {
    onSelect?.(selectedDate)
    setOpen(false)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onClear?.()
    onSelect?.(undefined)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          <span className="flex-1">
            {date ? (
              format(date, "dd.MM.yyyy", { locale: nb })
            ) : (
              placeholder
            )}
          </span>
          {showClear && date && (
            <span
              role="button"
              tabIndex={0}
              onClick={handleClear}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  handleClear(e as any)
                }
              }}
              className="ml-auto h-4 w-4 opacity-50 hover:opacity-100 cursor-pointer flex items-center justify-center"
              aria-label="Fjern valgt dato"
            >
              ×
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleSelect}
          disabled={(date) => {
            if (disabled) return true;
            if (minDate && date < minDate) return true;
            if (maxDate && date > maxDate) return true;
            return false;
          }}
          autoFocus
        />
      </PopoverContent>
    </Popover>
  )
}
