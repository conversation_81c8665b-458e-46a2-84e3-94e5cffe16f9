"use client";

import { useState } from "react";
import { Check, ChevronDown } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { useRouter, useSearchParams } from "next/navigation";
import { useCandidate } from "@/context/CandidateMonitorContext";

interface Props {}

const PermissionFilter: React.FC<Props> = () => {
  const [openPermission, setOpenPermission] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const permissionFilterLabel = "permission";

  const uniquePermissions = [
    "Har tilgang",
    "Ikke tilgang",
    "Digital tilgang sperret",
    "Dokumentert fravær",
    "Ikke-dokumentert fravær",
    "Venter på eksamensstart",
    "Venter på tilgang",
  ] as const;

  // Hent ut valgte tillatelser fra URL-en
  const getSelectedPermissions = (): string[] => {
    const permissions =
      searchParams.get(permissionFilterLabel)?.split(",") || [];
    return permissions.filter((permission) =>
      uniquePermissions.includes(permission as any)
    );
  };

  // Oppdater URL med valgte tilganger
  const updateQueryString = (permissions: string[]) => {
    const params = new URLSearchParams(searchParams.toString());

    if (permissions.length > 0) {
      params.set(permissionFilterLabel, permissions.join(","));
    } else {
      params.delete(permissionFilterLabel);
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    const currentPermissions = getSelectedPermissions();
    const newPermissions = checked
      ? [...currentPermissions, permission]
      : currentPermissions.filter((s) => s !== permission);

    updateQueryString(newPermissions);
  };

  const selectedPermissions = getSelectedPermissions();

  return (
    <div className="w-full">
      <Popover open={openPermission} onOpenChange={setOpenPermission}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-between transition-colors duration-200 ${
              openPermission ? "bg-udirLightAzure-100" : ""
            }`}
            aria-controls="permission"
            aria-expanded={openPermission}
            aria-haspopup="dialog"
            aria-label={`Tilgangsstatusfilter. ${selectedPermissions.length > 0 ? `${selectedPermissions.length} tilganger valgt` : 'Ingen tilganger valgt'}`}
          >
            <div className="flex items-center truncate">
              {selectedPermissions.length > 0 ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm">Tilgangsstatus</span>
                  <Badge variant="secondary" className="bg-mint">
                    {selectedPermissions.length}
                  </Badge>
                </div>
              ) : (
                "Tilgangsstatus"
              )}
            </div>
            <ChevronDown
              role="img"
              aria-label="åpne/lukke"
              className="ml-2 h-5 w-5 shrink-0 opacity-70 transition-transform duration-200"
              style={{
                transform: openPermission ? "rotate(180deg)" : "rotate(0deg)",
              }}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0 shadow-lg"
          align="start"
          sideOffset={4}
          style={{
            width:
              typeof window !== "undefined" && window.innerWidth < 768
                ? "var(--radix-popover-trigger-width)"
                : "auto",
          }}
        >
          <Command className="rounded-sm border border-gray-200 bg-udirLightAzure-100">
            <CommandList
              role="listbox"
              aria-label="Tilgangsstatusalternativer"
            >
              <CommandEmpty className="py-6 text-center text-sm text-gray-500">
                Ingen resultater funnet
              </CommandEmpty>
              <CommandGroup className="p-1">
                {uniquePermissions.map((status) => (
                  <CommandItem
                    key={status}
                    value={status}
                    onSelect={() => {
                      const isSelected = selectedPermissions.includes(status);
                      handlePermissionChange(status, !isSelected);
                    }}
                    className="flex items-center gap-2 px-2 py-1.5 focus:bg-udirLightAzure-500 rounded-md cursor-pointer transition-colors duration-150"
                  >
                    <div className="flex items-center flex-1 gap-2">
                      <Checkbox
                        id={`permission-${status.replace(/\s+/g, '-').toLowerCase()}`}
                        checked={selectedPermissions.includes(status)}
                        onCheckedChange={(checked) => {
                          handlePermissionChange(status, checked as boolean);
                        }}
                        className="border-udirLightAzure-500"
                        onClick={(e) => e.stopPropagation()}
                        aria-describedby={`permission-label-${status.replace(/\s+/g, '-').toLowerCase()}`}
                      />
                      <label
                        htmlFor={`permission-${status.replace(/\s+/g, '-').toLowerCase()}`}
                        id={`permission-label-${status.replace(/\s+/g, '-').toLowerCase()}`}
                        className="text-sm cursor-pointer"
                      >
                        {status}
                      </label>
                    </div>
                    {selectedPermissions.includes(status) && (
                      <Check className="h-4 w-4 text-mint" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default PermissionFilter;
