// src/entity/AuditLog.ts
import { AuditLogColumns } from "@/enums/AuditLogColumns";
import "reflect-metadata";
import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity("AuditLog")
export class AuditLog {
  @PrimaryGeneratedColumn({ name: AuditLogColumns.ID })
  [AuditLogColumns.ID]!: number;

  @Column("varchar", {
    name: AuditLogColumns.KANDIDAT_PAMELDING_ID,
    length: 255,
  })
  [AuditLogColumns.KANDIDAT_PAMELDING_ID]!: string;

  @Column("datetime", { name: AuditLogColumns.TIMESTAMP })
  [AuditLogColumns.TIMESTAMP]!: Date;

  @Column("varchar", { length: 255, name: AuditLogColumns.ROLLE })
  [AuditLogColumns.ROLLE]!: string;

  @Column("varchar", { length: 11, name: AuditLogColumns.KANDIDAT_NR })
  [AuditLogColumns.KANDIDAT_NR]!: string;

  @Column("varchar", { length: 255, name: AuditLogColumns.KANDIDAT_FORNAVN })
  [AuditLogColumns.KANDIDAT_FORNAVN]!: string;

  @Column("varchar", { length: 255, name: AuditLogColumns.KANDIDAT_ETTERNAVN })
  [AuditLogColumns.KANDIDAT_ETTERNAVN]!: string;

  @Column("varchar", { length: 45, name: AuditLogColumns.IP })
  [AuditLogColumns.IP]!: string;

  @Column("varchar", { length: 255, name: AuditLogColumns.DEVICE })
  [AuditLogColumns.DEVICE]!: string;

  @Column("varchar", { length: 255, name: AuditLogColumns.OS })
  [AuditLogColumns.OS]!: string;

  @Column("varchar", { length: 255, name: AuditLogColumns.BROWSER })
  [AuditLogColumns.BROWSER]!: string;

  @Column("varchar", { length: 255, name: AuditLogColumns.BROWSER_EDITION })
  [AuditLogColumns.BROWSER_EDITION]!: string;

  @Column("varchar", {
    length: 255,
    nullable: true,
    name: AuditLogColumns.OPERASJONSTYPE,
  })
  [AuditLogColumns.OPERASJONSTYPE]?: string;

  @Column("text", { name: AuditLogColumns.OPERASJON_BESKRIVELSE })
  [AuditLogColumns.OPERASJON_BESKRIVELSE]!: string;

  @Column("int", { name: AuditLogColumns.OPERATION_ID })
  [AuditLogColumns.OPERATION_ID]!: number;

  @Column("nvarchar", { length: "MAX", nullable: true, name: AuditLogColumns.PARAMETERS })
  [AuditLogColumns.PARAMETERS]?: string;

  @Column("varchar", {
    length: 255,
    nullable: true,
    name: AuditLogColumns.EKSAMENSDEL,
  })
  [AuditLogColumns.EKSAMENSDEL]?: string;

  @Column("varchar", {
    length: 255,
    nullable: true,
    name: AuditLogColumns.DEL1,
  })
  [AuditLogColumns.DEL1]?: string;

  @Column("varchar", {
    length: 255,
    nullable: true,
    name: AuditLogColumns.DEL2,
  })
  [AuditLogColumns.DEL2]?: string;

  @Column("varchar", {
    length: 255,
    nullable: true,
    name: AuditLogColumns.FILNAVN,
  })
  [AuditLogColumns.FILNAVN]?: string;
}
