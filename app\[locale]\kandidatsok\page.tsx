"use client";

import { useState, useEffect, useCallback } from "react";
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Search, X } from "lucide-react";
import { AuditLogDataTable } from "@/components/tables/kandidatmonitor/activityLog/activityLogDataTable";
import { activityLogColumns } from "@/components/tables/kandidatmonitor/activityLog/activityLogColumns";
import { toast } from "@/components/ui/use-toast";
import { IActivityLogColumnDef } from "@/interface/IActivityLogColumnDef";
import { TableSkeleton } from "@/components/tableSkeleton";
import CandidateInfo from "@/components/candidateInfo";

// Utility function to sanitize and validate input
const sanitizeInput = (input: string): string => {
  // Remove any potentially dangerous characters and trim whitespace
  return input.trim().replace(/[^a-fA-F0-9\-]/g, "");
};

// Utility function to validate input format
const isValidInput = (input: string): boolean => {
  const sanitized = sanitizeInput(input);
  // Allow only GUID format or numeric values (no special characters, SQL keywords etc.)
  return /^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}|\d+)$/i.test(
    sanitized
  );
};

// Utility function to convert numeric input to GUID format or return original if already GUID
const formatCandidateId = (input: string): string => {
  const sanitizedInput = sanitizeInput(input);

  // Validate input first
  if (!isValidInput(input)) {
    throw new Error("Ugyldig kandidatnummer format");
  }

  // Check if input is already a GUID format (case insensitive)
  if (
    sanitizedInput.match(
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i
    )
  ) {
    return sanitizedInput;
  }

  // Check if input is numeric only
  if (sanitizedInput.match(/^\d+$/)) {
    // Convert numeric input to GUID format: 00000000-0000-0000-0000-000000214471
    // The last segment needs to be exactly 12 characters, padded with zeros
    const paddedNumber = sanitizedInput.padStart(12, "0");
    return `00000000-0000-0000-0000-${paddedNumber}`;
  }

  // Return sanitized input if neither format matches
  return sanitizedInput;
};

export default function KandidatSok(): JSX.Element {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [searchTerm, setSearchTerm] = useState<string>(
    () => searchParams.get("q") || ""
  );
  const [activityLogData, setActivityLogData] = useState<
    IActivityLogColumnDef[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasSearched, setHasSearched] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    document.title = "Kandidatsøk | PGS-admin";
  }, []);

  // Function to update URL with search parameter
  const updateUrlWithSearchParam = useCallback((value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value.trim()) {
      params.set("q", value.trim());
    } else {
      params.delete("q");
    }
    const newUrl = params.toString()
      ? `${window.location.pathname}?${params}`
      : window.location.pathname;
    router.push(newUrl, { scroll: false });
  }, [searchParams, router]);

  // Auto-search if there's a search term in URL on component mount
  useEffect(() => {
    const urlSearchTerm = searchParams.get("q");
    if (urlSearchTerm && urlSearchTerm.trim() && !hasSearched) {
      // Set the search term and trigger search
      setSearchTerm(urlSearchTerm);
      handleSearchInternal(urlSearchTerm);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]); // Run when searchParams change

  const handleSearchInternal = async (termToSearch?: string) => {
    const searchTermToUse = termToSearch || searchTerm;
    if (!searchTermToUse.trim()) {
      toast({
        title: "Ugyldig søk",
        description: "Vennligst skriv inn en søketerm.",
        variant: "destructive",
      });
      return;
    }

    // Validate input format before proceeding
    if (!isValidInput(searchTermToUse)) {
      toast({
        title: "Ugyldig format",
        description:
          "Kandidatnummer må være numerisk eller i gyldig GUID-format.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setError(null);
    setHasSearched(true);

    try {
      // Format the search term to GUID format if it's numeric
      const formattedUserId = formatCandidateId(searchTermToUse);

      // Update URL with search term
      updateUrlWithSearchParam(searchTermToUse);

      const response = await fetch("/api/getActivityLogV2", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId: formattedUserId }),
      });

      if (!response.ok) {
        if (response.status === 429) {
          throw new Error("RATE_LIMIT_EXCEEDED");
        }
        throw new Error(`Error: ${response.status}`);
      }

      const result = await response.json();

      setActivityLogData(result);

      if (result.length === 0) {
        toast({
          title: "Ingen resultater",
          description:
            "Ingen aktivitetslogg funnet for den angitte kandidaten.",
        });
      }
    } catch (error) {
      console.error("Feil ved henting av aktivitetslogg:", error);

      if (
        error instanceof Error &&
        error.message === "Ugyldig kandidatnummer format"
      ) {
        setError(
          "Ugyldig kandidatnummer format. Bruk kun tall eller gyldig GUID-format."
        );
        toast({
          title: "Ugyldig format",
          description:
            "Kandidatnummer må være numerisk eller i gyldig GUID-format.",
          variant: "destructive",
        });
      } else if (
        error instanceof Error &&
        error.message === "RATE_LIMIT_EXCEEDED"
      ) {
        setError(
          "For mange søk på kort tid. Vennligst vent et øyeblikk før du prøver igjen."
        );
        toast({
          title: "For mange forespørsler",
          description:
            "Du har søkt for mange ganger på kort tid. Prøv igjen om et minutt.",
          variant: "destructive",
        });
      } else {
        setError(
          "Noe gikk galt ved henting av aktivitetsloggen. Prøv igjen senere."
        );
        toast({
          title: "Feil ved søk",
          description: "Kunne ikke hente aktivitetslogg. Prøv igjen senere.",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    await handleSearchInternal();
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <>
      <div className="py-6 bg-header">
        <div className="container-wrapper-lg flex flex-col gap-3">
          <div className="flex md:flex-row flex-col md:justify-between md:items-center items-start">
            <div>
              <h1 className="text-4xl">Aktivitetslogg</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="container-wrapper-lg mb-8 mt-4">
        <div className="flex flex-col gap-3">
          {/* Search Section */}
          <section className="flex flex-col gap-4">
            <div className="flex-grow">
              <div className="flex flex-col gap-1">
                <span className="text-sm font-medium">Søk etter kandidat</span>
                <div className="flex gap-4 items-center w-full sm:w-3/4 md:w-1/2 lg:w-1/4 xl:w-1/3">
                  <div className="relative flex-grow">
                    {/* Søkeikon */}
                    <div className="absolute inset-y-0 left-0 ml-3 flex items-center pointer-events-none">
                      <Search className="h-4 w-4 text-gray-500" />
                    </div>

                    {/* Input-felt */}
                    <Input
                      id="search-input"
                      type="text"
                      placeholder="Søk etter kandidatpåmeldingsID (f.eks. 214471)"
                      className="h-10 w-full pl-10 pr-10 bg-udirGray-100 py-2 text-sm border-0 border-b-2 border-black rounded-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyDown={handleKeyDown}
                      disabled={isLoading}
                      autoComplete="off"
                      autoCorrect="off"
                      autoCapitalize="off"
                      spellCheck="false"
                      maxLength={36}
                    />

                    {/* Slett-knapp */}
                    {searchTerm && (
                      <button
                        type="button"
                        onClick={() => {
                          setSearchTerm("");
                          updateUrlWithSearchParam("");
                        }}
                        className="absolute inset-y-0 right-0 mr-3 flex items-center text-gray-500 hover:text-gray-700"
                        disabled={isLoading}
                        aria-label="Slett søketekst"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  {/* Søkeknapp */}
                  <Button
                    onClick={handleSearch}
                    disabled={isLoading || !searchTerm.trim()}
                    size="sm"
                    className="h-10"
                  >
                    {isLoading ? "Søker..." : "Søk"}
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Feil</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Results Section */}
          {hasSearched && (
            <section aria-label="Aktivitetslogg resultat">
              {activityLogData.length > 0 && !isLoading && (
                <CandidateInfo
                  activityLogData={activityLogData}
                  searchTerm={searchTerm}
                />
              )}
              {isLoading ? (
                <TableSkeleton />
              ) : activityLogData.length > 0 ? (
                <div className="overflow-x-auto">
                  <AuditLogDataTable
                    columns={activityLogColumns}
                    data={activityLogData}
                  />
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>Ingen aktivitetslogg funnet for den angitte kandidaten.</p>
                </div>
              )}
            </section>
          )}
        </div>
      </div>
    </>
  );
}
