"use client";

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import React, { useEffect, useState } from "react";
import CandidateInfoModal from "@/app/[locale]/nedlasting/[slug]/candidateInfoModal";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ICandidate } from "@/interface/ICandidate";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { motion } from "framer-motion";

interface DataTableProps<IExamPaper, TValue> {
  columns: ColumnDef<IExamPaper, TValue>[];
  data: IExamPaper[];
  extraCandidateInfo: IExamPaperInternal;
}

export function DataTable<IExamPaper, TValue>({
  columns,
  data,
  extraCandidateInfo,
}: DataTableProps<IExamPaper, TValue>) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [candidateInfo, setCandidateInfo] = useState<ICandidate>();
  const [sorting, setSorting] = useState<SortingState>([
    { id: "candidateName", desc: false },
  ]);
  const [sortKey, setSortKey] = useState(0);

  // Only trigger staggered animation on manual actions, not on data changes
  // This prevents jank during navigation when data re-fetches

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (updater) => {
      setSorting(updater);
      setSortKey((prev) => prev + 1); // Trigger staggered animation on sort
    },
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  const handleRowClick = (rowData: IExamPaper) => {
    setCandidateInfo(rowData as ICandidate);
    setDialogOpen(true);
  };

  const handleRowKeyDown = (e: React.KeyboardEvent, rowData: IExamPaper) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleRowClick(rowData);
    }
  };

  return (
    <>
      <motion.div
        className="rounded-md border overflow-y-hidden overflow-x-auto"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <Table
          aria-label="Tabell med kandidater og besvarelser"
          aria-describedby="candidate-table-description"
        >
          <caption id="candidate-table-description" className="sr-only">
            Oversikt over kandidater med antall leverte filer og eksamensstatus.
            Klikk på en rad for å se detaljer om kandidatens besvarelser.
          </caption>
          <motion.thead
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2, delay: 0.1 }}
          >
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className="whitespace-nowrap"
                    scope="col"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </motion.thead>
          <TableBody key={sortKey}>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <motion.tr
                  key={`${sortKey}-${row.id}`}
                  tabIndex={0}
                  role="button"
                  aria-label={`Se detaljer for kandidat ${
                    (row.original as any).candidateName
                  }`}
                  data-state={row.getIsSelected() && "selected"}
                  className="hover:bg-slate-100 cursor-pointer border-b transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                  onClick={() => handleRowClick(row.original)}
                  onKeyDown={(e) => handleRowKeyDown(e, row.original)}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.05,
                    ease: "easeOut",
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="whitespace-nowrap">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </motion.tr>
              ))
            ) : (
              <motion.tr
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Ingen kandidater
                </TableCell>
              </motion.tr>
            )}
          </TableBody>
        </Table>
      </motion.div>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent
          className="sm:max-w-[90%] sm:max-h-[90vh] overflow-auto flex flex-col"
          role="dialog"
        >
          <DialogTitle />
          {candidateInfo && (
            <CandidateInfoModal
              candidateInfo={candidateInfo}
              subjectCode={extraCandidateInfo.subjectCode}
              testPeriod={extraCandidateInfo.testPeriod}
              subjectName={extraCandidateInfo.subjectName}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
