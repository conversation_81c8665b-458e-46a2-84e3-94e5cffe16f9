import { getDbConnection } from "../connection";
import { Fagkodeeksamen } from "../models/Fagkodeeksamen";
import { Eksamensdel } from "../models/Eksamensdel";
import { IsNull } from "typeorm";
import { IEksamensplanApiData } from "@/interface/IEksamensplanImport";
import { parseStringPromise } from "xml2js";

export type EksamensplanData = IEksamensplanApiData;

interface XmlEksamensdelData {
  GjennomforingStart?: string[];
  GjennomforingStopp?: string[];
  Gjennomforingsystem?: string[];
  Eksamensveiledning?: string[];
  ErPlagiatkontroll?: boolean[];
}

interface XmlEksamenData {
  Fagkode: string[];
  Variantkode?: string[];
  Fagnavn?: string[];
  Varighet?: number[];
  Eksamensdato?: string[];
  Eksamenstid?: string[];
  ErTestFagkode?: boolean[];
  Eksamensdeler?: XmlEksamensdelData[];
}

interface XmlEksamensplanData {
  Opplaeringsniva?: string[];
  Oppgaveansvar?: string[];
  Eksamensperiode?: {
    Kode: string[];
  }[];
  Eksamener?: {
    PgsdEksamenViewModel: XmlEksamenData[];
  }[];
}

export class EksamensplanService {
  /**
   * Henter fagkoder fra Udir API og returnerer som map for rask oppslag
   */
  private async fetchFagkoder(): Promise<Map<string, string>> {
    const fagkoderMap = new Map<string, string>();

    try {
      console.log("Fetching fagkoder from Udir API...");

      const response = await fetch(
        "https://data.udir.no/kl06/v201906/fagkoder",
        {
          headers: {
            Accept: "application/json",
            "User-Agent": "PGS-Admin-Import/1.0",
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `HTTP error! status: ${response.status} - ${response.statusText}`
        );
      }

      const fagkoder = await response.json();

      if (!Array.isArray(fagkoder)) {
        throw new Error("Invalid fagkoder API response - expected array");
      }

      // Process fagkoder and extract default language titles
      for (const fagkode of fagkoder) {
        if (fagkode.kode && fagkode.tittel && Array.isArray(fagkode.tittel)) {
          const defaultTitle = fagkode.tittel.find(
            (t: any) => t.spraak === "default"
          );
          if (defaultTitle && defaultTitle.verdi) {
            fagkoderMap.set(fagkode.kode, defaultTitle.verdi);
          }
        }
      }

      console.log(`Successfully fetched ${fagkoderMap.size} fagkoder from API`);
      return fagkoderMap;
    } catch (error) {
      console.warn("Failed to fetch fagkoder from API, will use fallback:", {
        error: error instanceof Error ? error.message : String(error),
      });
      // Return empty map - the code will fall back to using fagkode as fagnavn
      return fagkoderMap;
    }
  }

  /**
   * Konverterer XML-data til vårt interne format
   */
  private parseXmlToEksamensplanData(
    xmlData: any,
    eksamensperiode: string,
    fagkoderMap?: Map<string, string>
  ): EksamensplanData[] {
    const result: EksamensplanData[] = [];

    try {
      // Navigate the XML structure
      const root = xmlData.PgsdEksamensplanerViewModel;
      if (!root?.Eksamensplaner?.[0]?.PgsdEksamensplanViewModel) {
        console.warn("No eksamensplan data found in XML structure");
        return result;
      }

      const eksamensplaner = root.Eksamensplaner[0].PgsdEksamensplanViewModel;

      // Handle both single object and array cases
      const planArray = Array.isArray(eksamensplaner)
        ? eksamensplaner
        : [eksamensplaner];

      for (const plan of planArray) {
        if (!plan.Eksamener?.[0]?.PgsdEksamenViewModel) {
          continue;
        }

        const eksamener = plan.Eksamener[0].PgsdEksamenViewModel;
        const eksamenArray = Array.isArray(eksamener) ? eksamener : [eksamener];

        // Extract Oppgaveansvar and Opplaeringsniva from plan level
        const oppgaveansvar = plan.Oppgaveansvar?.[0];
        const opplaeringsniva = plan.Opplaeringsniva?.[0];

        for (const eksamen of eksamenArray) {
          if (!eksamen.Fagkode?.[0]) {
            console.warn("Skipping eksamen without fagkode");
            continue;
          }

          let fagkode = eksamen.Fagkode[0];
          let variantkode: string | undefined = eksamen.Variant?.[0];

          // Check if variant has actual content (not empty string, undefined, or just whitespace)
          if (
            variantkode &&
            typeof variantkode === "string" &&
            variantkode.trim() !== ""
          ) {
            variantkode = variantkode.trim();
          } else {
            variantkode = undefined;
          }

          // Get fagnavn from fagkoderMap, fall back to fagkode if not found
          let fagnavn = fagkoderMap?.get(fagkode) || fagkode;

          // Validate field lengths to prevent database truncation errors
          if (fagkode && fagkode.length > 8) {
            console.warn(
              `Fagkode too long (${fagkode.length} chars): ${fagkode}. Truncating to 8 characters.`
            );
            fagkode = fagkode.substring(0, 8);
          }

          if (variantkode && variantkode.length > 8) {
            console.warn(
              `Variantkode too long (${variantkode.length} chars): ${variantkode}. Truncating to 8 characters.`
            );
            variantkode = variantkode.substring(0, 8);
          }

          if (fagnavn && fagnavn.length > 255) {
            console.warn(
              `Fagnavn too long (${fagnavn.length} chars): ${fagnavn}. Truncating to 255 characters.`
            );
            fagnavn = fagnavn.substring(0, 255);
          }

          // Parse eksamensdeler
          const eksamensdeler: any[] = [];
          // Get gjennomforingsystem from exam level (not eksamensdel level)
          const gjennomforingsystem =
            eksamen.Gjennomføringssystem?.[0] ||
            eksamen.Gjennomforingsystem?.[0];
          const plagiatKontroll =
            eksamen.PlagiatKontroll?.[0] === "true" ||
            eksamen.PlagiatKontroll?.[0] === true;

          if (eksamen.Eksamensdeler?.[0]?.PgsdEksamensdelViewModel) {
            const deler = eksamen.Eksamensdeler[0].PgsdEksamensdelViewModel;
            const delerArray = Array.isArray(deler) ? deler : [deler];

            for (let i = 0; i < delerArray.length; i++) {
              const del = delerArray[i];
              eksamensdeler.push({
                eksamensdelType: del.Type?.[0] || del.EksamensdelType?.[0], // Use 'Type' field from API
                gjennomforingStart: del.GjennomforingStart?.[0],
                gjennomforingStopp: del.GjennomforingStopp?.[0],
                gjennomforingsystem: gjennomforingsystem, // Use from exam level
                eksamensveiledning: del.Eksamensveiledning?.[0],
                erPlagiatkontroll: plagiatKontroll, // Use from exam level
                varighet: del.Varighet?.[0]
                  ? parseInt(del.Varighet[0])
                  : undefined,
                elektroniskPalogging:
                  del.ElektroniskPålogging?.[0] === "true" ||
                  del.ElektroniskPålogging?.[0] === true,
              });
            }
          }

          // Calculate total varighet, earliest eksamensdato, and eksamenstid from eksamensdeler
          let totalVarighet = 0;
          let earliestEksamensdato: Date | undefined = undefined;
          let earliestStartTime: string | undefined = undefined;

          for (const del of eksamensdeler) {
            // Add up varighet from all eksamensdeler
            if (del.varighet) {
              totalVarighet += del.varighet;
            }

            // Find earliest GjennomforingStart as eksamensdato and eksamenstid
            if (del.gjennomforingStart) {
              const startDate = new Date(del.gjennomforingStart);
              if (!earliestEksamensdato || startDate < earliestEksamensdato) {
                earliestEksamensdato = startDate;
                // Extract time in HH:MM format
                earliestStartTime = startDate.toTimeString().substring(0, 5);
              }
            }
          }

          result.push({
            fagkode,
            variantkode,
            eksamensperiode,
            fagnavn,
            varighet: totalVarighet > 0 ? totalVarighet : undefined,
            eksamensdato: earliestEksamensdato
              ? earliestEksamensdato.toISOString().split("T")[0]
              : undefined, // Format as YYYY-MM-DD
            eksamenstid: earliestStartTime || eksamen.Eksamenstid?.[0],
            erTestFagkode:
              eksamen.ErTestFagkode?.[0] === "true" ||
              eksamen.ErTestFagkode?.[0] === true,
            oppgaveansvar: oppgaveansvar || undefined,
            opplaeringsniva: opplaeringsniva || undefined,
            eksamensdeler,
          });
        }
      }

      console.log(`Parsed ${result.length} fagkoder from XML data`);
      return result;
    } catch (error) {
      console.error("Error parsing XML data:", error);
      throw new Error(
        `Failed to parse XML data: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Henter eksamensplandata fra ekstern API med retry-logikk
   */
  async fetchEksamensplanData(
    eksamensperiode: string
  ): Promise<EksamensplanData[]> {
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second base delay

    // Fetch fagkoder once before starting the retry loop
    const fagkoderMap = await this.fetchFagkoder();

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(
          `Attempt ${attempt} - Fetching XML data for periode: ${eksamensperiode}`
        );

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        const apiUrl = process.env.EKSAMENAPI_URL || "";
        const response = await fetch(
          `${apiUrl}/pgsd/eksamensplan?eksamensperiode=${eksamensperiode}`,
          {
            signal: controller.signal,
            headers: {
              Accept: "application/xml, text/xml, */*",
              "User-Agent": "PGS-Admin-Import/1.0",
            },
          }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(
            `HTTP error! status: ${response.status} - ${response.statusText}`
          );
        }

        // Get response as text since it's XML
        const xmlText = await response.text();

        if (!xmlText || xmlText.trim().length === 0) {
          throw new Error("API returned empty response");
        }

        // Parse XML to JavaScript object
        const xmlData = await parseStringPromise(xmlText, {
          explicitArray: true,
          ignoreAttrs: true,
          trim: true,
        });

        // Convert XML structure to our expected format
        const data = this.parseXmlToEksamensplanData(
          xmlData,
          eksamensperiode,
          fagkoderMap
        );

        console.log(
          `Successfully parsed ${data.length} records for periode: ${eksamensperiode}`
        );
        return data;
      } catch (error) {
        const isLastAttempt = attempt === maxRetries;
        const isAbortError =
          error instanceof Error && error.name === "AbortError";

        console.warn(`Attempt ${attempt} failed:`, {
          error: error instanceof Error ? error.message : String(error),
          isAbortError,
          eksamensperiode,
        });

        if (isLastAttempt) {
          console.error("All retry attempts failed for API call");
          throw new Error(
            `Failed to fetch data after ${maxRetries} attempts: ${
              error instanceof Error ? error.message : String(error)
            }`
          );
        }

        // Exponential backoff: wait longer between retries
        const delay = retryDelay * Math.pow(2, attempt - 1);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw new Error("Unexpected error in retry logic");
  }

  /**
   * Lagrer eksamensplandata til database med optimalisert bulk operasjoner
   */
  async saveEksamensplanData(
    eksamensplanData: EksamensplanData[]
  ): Promise<{ success: number; errors: number; deleted: number }> {
    const AppDataSource = await getDbConnection();
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let successCount = 0;
    let errorCount = 0;
    let deletedCount = 0;

    try {
      const fagkodeeksamensRepository =
        queryRunner.manager.getRepository(Fagkodeeksamen);

      // Bulk fetch existing fagkoder for this import batch
      const existingFagkoder = await fagkodeeksamensRepository
        .createQueryBuilder("f")
        .where("f.Eksamensperiode = :periode", {
          periode: eksamensplanData[0]?.eksamensperiode,
        })
        .getMany();

      // Create lookup map for faster access - use composite key that matches database unique constraint
      const existingFagkoderMap = new Map<string, Fagkodeeksamen>();

      existingFagkoder.forEach((f) => {
        // Create lookup key based on unique constraint: Fagkode + Variantkode + Eksamensperiode
        const compositeKey = `${f.Fagkode}_${f.Variantkode || "NULL"}_${
          f.Eksamensperiode
        }`;
        existingFagkoderMap.set(compositeKey, f);
      });

      console.log(
        `Found ${existingFagkoder.length} existing fagkoder for periode ${eksamensplanData[0]?.eksamensperiode}`
      );

      // Create set of composite keys from imported data to identify fagkoder to keep
      const importedCompositeKeys = new Set<string>();
      eksamensplanData.forEach((item) => {
        const lookupKey = `${item.fagkode}_${item.variantkode || "NULL"}_${
          item.eksamensperiode
        }`;
        importedCompositeKeys.add(lookupKey);
      });

      // Identify fagkoder to delete (exist in database but not in imported data)
      const fagkoderToDelete: Fagkodeeksamen[] = [];
      existingFagkoderMap.forEach((fagkode, compositeKey) => {
        if (!importedCompositeKeys.has(compositeKey)) {
          fagkoderToDelete.push(fagkode);
        }
      });

      // Prepare arrays for bulk operations
      const fagkoderToInsert: Fagkodeeksamen[] = [];
      const fagkoderToUpdate: Fagkodeeksamen[] = [];
      const processedCompositeKeys = new Set<string>(); // Track composite keys to prevent duplicates
      const eksamensdelerToProcess: Array<{
        fagkodeeksamen: Fagkodeeksamen;
        eksamensdeler: any[];
      }> = [];

      // Process each item and categorize for bulk operations
      for (const item of eksamensplanData) {
        try {
          const lookupKey = `${item.fagkode}_${item.variantkode || "NULL"}_${
            item.eksamensperiode
          }`;
          const eksisterende = existingFagkoderMap.get(lookupKey);

          let fagkodeeksamen: Fagkodeeksamen;

          if (eksisterende) {
            // Update existing record
            fagkodeeksamen = eksisterende;
            fagkodeeksamen.Fagnavn = item.fagnavn;
            fagkodeeksamen.Varighet = item.varighet || null;
            fagkodeeksamen.Eksamensdato = item.eksamensdato
              ? new Date(item.eksamensdato)
              : null;
            fagkodeeksamen.Eksamenstid = item.eksamenstid || null;
            fagkodeeksamen.ErTestFagkode = item.erTestFagkode || false;
            fagkodeeksamen.Oppgaveansvar = item.oppgaveansvar || null;
            fagkodeeksamen.Opplaeringsniva = item.opplaeringsniva || null;
            fagkoderToUpdate.push(fagkodeeksamen);
          } else {
            // Check if we're already processing this composite key in this batch
            if (processedCompositeKeys.has(lookupKey)) {
              console.warn(
                `Duplicate composite key detected in batch: ${lookupKey}. Skipping duplicate.`
              );
              errorCount++;
              continue;
            }

            // Create new record - ID will be auto-generated
            console.log(
              `Creating new fagkode: ${item.fagkode} (${
                item.variantkode || "no variant"
              }) for ${item.eksamensperiode}`
            );

            fagkodeeksamen = fagkodeeksamensRepository.create({
              Fagkode: item.fagkode,
              Variantkode: item.variantkode || undefined,
              Eksamensperiode: item.eksamensperiode,
              Fagnavn: item.fagnavn,
              Varighet: item.varighet || undefined,
              Eksamensdato: item.eksamensdato
                ? new Date(item.eksamensdato)
                : undefined,
              Eksamenstid: item.eksamenstid || undefined,
              ErTestFagkode: item.erTestFagkode || false,
              Oppgaveansvar: item.oppgaveansvar || undefined,
              Opplaeringsniva: item.opplaeringsniva || undefined,
            });

            processedCompositeKeys.add(lookupKey);
            fagkoderToInsert.push(fagkodeeksamen);
          }

          // Store eksamensdeler for later processing
          if (item.eksamensdeler && item.eksamensdeler.length > 0) {
            eksamensdelerToProcess.push({
              fagkodeeksamen,
              eksamensdeler: item.eksamensdeler,
            });
          }

          successCount++;
        } catch (error) {
          console.error(`Error processing fagkode ${item.fagkode}:`, {
            error: error instanceof Error ? error.message : String(error),
            fagkode: item.fagkode,
            eksamensperiode: item.eksamensperiode,
          });
          errorCount++;
        }
      }

      // Delete fagkoder that are no longer in the plan
      if (fagkoderToDelete.length > 0) {
        console.log(
          `Deleting ${fagkoderToDelete.length} fagkoder that are no longer in the plan`
        );
        
        const eksamensdelRepository = queryRunner.manager.getRepository(Eksamensdel);
        const fagkodeIdsToDelete = fagkoderToDelete.map(f => f.FagkodeeksamensID);
        
        // First, get all eksamensdel IDs that will be deleted
        const eksamensdelerToDelete = await eksamensdelRepository
          .createQueryBuilder('e')
          .select('e.EksamensdelID')
          .where("e.FagkodeeksamensID IN (:...ids)", { ids: fagkodeIdsToDelete })
          .getMany();
        
        const eksamensdelIdsToDelete = eksamensdelerToDelete.map(e => e.EksamensdelID);
        
        // Second, delete references in EksamensdelEksamensmateriell table
        if (eksamensdelIdsToDelete.length > 0) {
          await queryRunner.manager
            .createQueryBuilder()
            .delete()
            .from('EksamensdelEksamensmateriell')
            .where("EksamensdelID IN (:...ids)", { ids: eksamensdelIdsToDelete })
            .execute();
        }
        
        // Third, delete the eksamensdeler
        await eksamensdelRepository
          .createQueryBuilder()
          .delete()
          .where("FagkodeeksamensID IN (:...ids)", { ids: fagkodeIdsToDelete })
          .execute();

        // Finally, delete the fagkoder
        await this.deleteBatched(fagkodeeksamensRepository, fagkoderToDelete);
        deletedCount = fagkoderToDelete.length;
      }

      // Perform bulk operations with batching to avoid SQL Server parameter limit
      const batchPromises: Promise<void>[] = [];

      // Bulk insert new fagkoder in batches
      if (fagkoderToInsert.length > 0) {
        batchPromises.push(
          this.saveBatched(
            fagkodeeksamensRepository,
            fagkoderToInsert,
            "insert"
          )
        );
      }

      // Bulk update existing fagkoder in batches
      if (fagkoderToUpdate.length > 0) {
        batchPromises.push(
          this.saveBatched(
            fagkodeeksamensRepository,
            fagkoderToUpdate,
            "update"
          )
        );
      }

      // Wait for all batch operations to complete
      await Promise.all(batchPromises);

      // Process eksamensdeler efficiently after all fagkoder are saved
      await this.processEksamensdelerBulk(queryRunner, eksamensdelerToProcess);
      ("");

      await queryRunner.commitTransaction();
      console.log(
        `Database operation completed. Success: ${successCount}, Errors: ${errorCount}, Deleted: ${deletedCount}`
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error in bulk save operation:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    } finally {
      await queryRunner.release();
    }

    return { success: successCount, errors: errorCount, deleted: deletedCount };
  }

  /**
   * Optimalisert behandling av eksamensdeler med bulk operasjoner
   */
  private async processEksamensdelerBulk(
    queryRunner: any,
    eksamensdelerToProcess: Array<{
      fagkodeeksamen: Fagkodeeksamen;
      eksamensdeler: any[];
    }>
  ): Promise<void> {
    if (eksamensdelerToProcess.length === 0) return;

    const eksamensdelRepository =
      queryRunner.manager.getRepository(Eksamensdel);

    // Collect all FagkodeeksamensIDs for bulk fetch
    const fagkodeeksamensIds: number[] = [];
    for (const item of eksamensdelerToProcess) {
      fagkodeeksamensIds.push(item.fagkodeeksamen.FagkodeeksamensID);
    }

    // Bulk fetch existing eksamensdeler by FagkodeeksamensID
    const existingEksamensdeler = await eksamensdelRepository
      .createQueryBuilder("e")
      .where("e.FagkodeeksamensID IN (:...ids)", { ids: fagkodeeksamensIds })
      .getMany();

    // Create lookup map using FagkodeeksamensID + EksamensdelType as composite key
    const existingEksamensdelerMap = new Map<string, Eksamensdel>();
    existingEksamensdeler.forEach((e: Eksamensdel) => {
      const compositeKey = `${e.FagkodeeksamensID}_${e.EksamensdelType}`;
      existingEksamensdelerMap.set(compositeKey, e);
    });

    const eksamensdelerToInsert: Eksamensdel[] = [];
    const eksamensdelerToUpdate: Eksamensdel[] = [];

    // Process all eksamensdeler
    for (const item of eksamensdelerToProcess) {
      for (const delData of item.eksamensdeler) {
        // Use FagkodeeksamensID + EksamensdelType as composite lookup key
        const compositeKey = `${item.fagkodeeksamen.FagkodeeksamensID}_${delData.eksamensdelType}`;
        const eksisterendeDel = existingEksamensdelerMap.get(compositeKey);

        let eksamensdel: Eksamensdel;

        if (eksisterendeDel) {
          // Update existing
          eksamensdel = eksisterendeDel;
          eksamensdel.GjennomforingStart = delData.gjennomforingStart
            ? new Date(delData.gjennomforingStart)
            : null;
          eksamensdel.GjennomforingStopp = delData.gjennomforingStopp
            ? new Date(delData.gjennomforingStopp)
            : null;
          eksamensdel.Gjennomforingsystem = delData.gjennomforingsystem || null;
          eksamensdel.Eksamensveiledning = delData.eksamensveiledning || null;
          eksamensdel.ErPlagiatkontroll = delData.erPlagiatkontroll || false;
          eksamensdelerToUpdate.push(eksamensdel);
        } else {
          // Create new - ID will be auto-generated
          eksamensdel = eksamensdelRepository.create({
            FagkodeeksamensID: item.fagkodeeksamen.FagkodeeksamensID, // Reference to parent Fagkodeeksamen
            EksamensdelType: delData.eksamensdelType, // EksamensdelType er eksamensdelnavnet
            GjennomforingStart: delData.gjennomforingStart
              ? new Date(delData.gjennomforingStart)
              : undefined,
            GjennomforingStopp: delData.gjennomforingStopp
              ? new Date(delData.gjennomforingStopp)
              : undefined,
            Gjennomforingsystem: delData.gjennomforingsystem || undefined, // Gjennomforingssystem fra API
            Eksamensveiledning: delData.eksamensveiledning || undefined,
            ErPlagiatkontroll: delData.erPlagiatkontroll || false,
          });
          eksamensdelerToInsert.push(eksamensdel);
        }
      }
    }

    // Bulk operations for eksamensdeler with batching
    if (eksamensdelerToInsert.length > 0) {
      await this.saveBatched(
        eksamensdelRepository,
        eksamensdelerToInsert,
        "insert"
      );
    }

    if (eksamensdelerToUpdate.length > 0) {
      await this.saveBatched(
        eksamensdelRepository,
        eksamensdelerToUpdate,
        "update"
      );
    }

    console.log(
      `Eksamensdeler processed: ${eksamensdelerToInsert.length} inserts, ${eksamensdelerToUpdate.length} updates`
    );
  }

  /**
   * Batch save method to avoid SQL Server parameter limit (2100 parameters)
   * Uses createQueryBuilder to avoid TypeORM circular dependency issues
   */
  private async saveBatched<T>(
    repository: any,
    entities: T[],
    operation: "insert" | "update",
    batchSize: number = 100
  ): Promise<void> {
    if (entities.length === 0) return;

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      try {
        if (operation === "insert") {
          await repository
            .createQueryBuilder()
            .insert()
            .values(batch)
            .execute();
        } else {
          // For updates, we need to handle each entity separately
          for (const entity of batch) {
            const primaryKey =
              repository.metadata.primaryColumns[0].propertyName;
            const primaryValue = (entity as any)[primaryKey];

            // Create a copy of entity without the primary key to avoid updating identity column
            const updateData = { ...entity } as any;
            delete updateData[primaryKey];

            await repository
              .createQueryBuilder()
              .update()
              .set(updateData)
              .where(`${primaryKey} = :id`, { id: primaryValue })
              .execute();
          }
        }
      } catch (error) {
        console.error(`Error in batch ${Math.floor(i / batchSize) + 1}:`, {
          error: error instanceof Error ? error.message : String(error),
          batchSize: batch.length,
          operation,
        });
        throw error;
      }
    }
  }

  /**
   * Batch delete method to avoid SQL Server parameter limit (2100 parameters)
   */
  private async deleteBatched<T>(
    repository: any,
    entities: T[],
    batchSize: number = 100
  ): Promise<void> {
    if (entities.length === 0) return;

    const primaryKey = repository.metadata.primaryColumns[0].propertyName;

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      const ids = batch.map((entity: any) => entity[primaryKey]);
      
      try {
        await repository
          .createQueryBuilder()
          .delete()
          .where(`${primaryKey} IN (:...ids)`, { ids })
          .execute();
      } catch (error) {
        console.error(`Error in delete batch ${Math.floor(i / batchSize) + 1}:`, {
          error: error instanceof Error ? error.message : String(error),
          batchSize: batch.length,
        });
        throw error;
      }
    }
  }

  /**
   * Importerer eksamensplandata fra API og lagrer til database
   */
  async importEksamensplan(
    eksamensperiode: string
  ): Promise<{ success: number; errors: number; deleted: number; message: string }> {
    const startTime = Date.now();

    try {
      console.log(`Starting import for eksamensperiode: ${eksamensperiode}`);

      // Fetch data from external API
      const eksamensplanData = await this.fetchEksamensplanData(
        eksamensperiode
      );

      if (!eksamensplanData || eksamensplanData.length === 0) {
        console.warn(`No data found for eksamensperiode: ${eksamensperiode}`);
        return {
          success: 0,
          errors: 0,
          deleted: 0,
          message: `Ingen data funnet for eksamensperiode ${eksamensperiode}`,
        };
      }

      console.log(`Processing ${eksamensplanData.length} records...`);

      // Save data to database
      const result = await this.saveEksamensplanData(eksamensplanData);

      const duration = Date.now() - startTime;
      const successMessage = `Import fullført på ${duration}ms. ${result.success} vellykket, ${result.errors} feil, ${result.deleted} slettet.`;

      return {
        ...result,
        message: successMessage,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Import feilet etter ${duration}ms: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`;

      console.error("Error during import:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        eksamensperiode,
        duration,
      });

      throw new Error(errorMessage);
    }
  }
}
