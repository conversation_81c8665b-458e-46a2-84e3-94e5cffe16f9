import { Alert, AlertDescription } from "@/components/ui/alert";
import { TbInfoTriangle } from "react-icons/tb";
import { IoEyeOutline } from "react-icons/io5";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { useMemo } from "react";
import dayjs from "dayjs";
import { useRouter, useSearchParams } from "next/navigation";
import { IAccessRequestUpdate } from "@/hooks/accessRequestProvider";

interface AccessRequestBannerProps {
  accessRequests: IAccessRequestUpdate[];
  examPaper: any[];
  candidateStatus: Record<string, { isAuthorized: boolean; hasAbsence: boolean; isBlocked: boolean; hasLevert: boolean; isWaitingForExam: boolean; }>;
  resetSearchInput: () => void;
}

const shouldHideAccessRequest = (
  candidate: any,
  examItem: any,
  candidateStatus: Record<string, { isAuthorized: boolean; hasAbsence: boolean; isBlocked: boolean; hasLevert: boolean; isWaitingForExam: boolean; }>
) => {
  const status =
    candidate.deliveryStatusPart2 !== -1
      ? candidate.deliveryStatusPart2
      : candidate.deliveryStatusPart1;

  const hasAbsenceStatus = [
    CandidateStatusEnum.DokumentertFravaer,
    CandidateStatusEnum.IkkeDokumentertFravaer,
  ].includes(status);

  const hasNeverBeenAuthorized = [
    CandidateStatusEnum.IkkeInnlogget,
    CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
  ].includes(status);

  const hasDelivered = [
    CandidateStatusEnum.Levert,
    CandidateStatusEnum.LevertManuelt,
  ].includes(status);

  const isBlocked = candidateStatus[candidate.candidateNumber]?.isBlocked || false;

  const deliveryStatus = formatCandidateStatus(
    status,
    candidate.deliveryStatusPart2 !== -1
      ? examItem.partTwoStartDateTime
      : examItem.partOneStartDateTime,
    candidate.isPreauthenticated
  );

  const isDeliveredStatus =
    deliveryStatus === "Levert digitalt" ||
    deliveryStatus === "Sendes i posten";

  return hasAbsenceStatus || hasDelivered || isBlocked || isDeliveredStatus;
};

const formatCandidateStatus = (
  candidateStatus: CandidateStatusEnum,
  examStartDate: string,
  isPreauthenticated: boolean
): string => {
  const examStart = dayjs(examStartDate);
  const now = dayjs();

  if (
    candidateStatus === CandidateStatusEnum.InnloggetAutentisert &&
    now.isBefore(examStart)
  ) {
    return "Venter på eksamensstart";
  }

  if (
    candidateStatus === CandidateStatusEnum.IkkeInnlogget &&
    isPreauthenticated
  ) {
    return "Autorisert";
  }

  const statusMap = {
    [CandidateStatusEnum.IkkeInnlogget]: "Ingen status (-)",
    [CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert]:
      "Venter på tilgang",
    [CandidateStatusEnum.InnloggetAutentisert]: "Startet",
    [CandidateStatusEnum.LastetOpp]: "Laster opp",
    [CandidateStatusEnum.Levert]: "Levert digitalt",
    [CandidateStatusEnum.LevertManuelt]: "Sendes i posten",
    [CandidateStatusEnum.DokumentertFravaer]: "Dokumentert fravær",
    [CandidateStatusEnum.IkkeDokumentertFravaer]: "Ikke-dokumentert fravær",
  };

  return statusMap[candidateStatus] || "Ukjent status";
};

export const AccessRequestBanner = ({
  accessRequests,
  examPaper,
  candidateStatus,
  resetSearchInput,
}: AccessRequestBannerProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const validAccessRequests = useMemo(() => {
    return accessRequests.filter((request) => {
      const examItem = examPaper.find((paper) =>
        paper.candidates.some(
          (c: { candidateNumber: string }) =>
            c.candidateNumber === request.candidateNumber
        )
      );

      if (!examItem) return false;

      const candidate = examItem.candidates.find(
        (c: { candidateNumber: string }) =>
          c.candidateNumber === request.candidateNumber
      );

      if (!candidate) return false;

      return !shouldHideAccessRequest(candidate, examItem, candidateStatus);
    });
  }, [accessRequests, examPaper, candidateStatus]);

  const firstTimeAccessRequests = validAccessRequests.filter((request) => {
    const examItem = examPaper.find((paper) =>
      paper.candidates.some(
        (c: { candidateNumber: string }) =>
          c.candidateNumber === request.candidateNumber
      )
    );

    if (!examItem) return false;

    const candidate = examItem.candidates.find(
      (c: { candidateNumber: string }) =>
        c.candidateNumber === request.candidateNumber
    );

    if (!candidate) return false;

    const status =
      candidate.deliveryStatusPart2 !== -1
        ? candidate.deliveryStatusPart2
        : candidate.deliveryStatusPart1;

    return [
      CandidateStatusEnum.IkkeInnlogget,
      CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
    ].includes(status);
  });

  const secondTimeAccessRequests = validAccessRequests.filter(
    (request) => !firstTimeAccessRequests.includes(request)
  );

  const handleShowRequests = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("groupCodes");
    params.delete("candidate");
    params.delete("statuses");
    params.delete("absenceStatus");
    params.delete("examPerformance");
    params.delete("permission");
    resetSearchInput();

    router.push(`?permission=Venter+på+tilgang`, { scroll: false });
  };

  if (validAccessRequests.length === 0) return null;

  return (
    <Alert variant="warning" className="md:sticky md:top-[63px] ">
      <AlertDescription>
        <div className="flex flex-row justify-between items-center">
          <div className=" flex gap-10 items-center  md:hidden block text-xs">
            <TbInfoTriangle
              className="h-4 w-4 text-[#EF6C00]"
              role="img"
              aria-label="Info"
            />
            <ul className="list-disc ">
              {firstTimeAccessRequests.length > 0 && (
                <li className="">
                  {firstTimeAccessRequests.length} kandidat(er) venter på
                  tilgang.
                </li>
              )}
            </ul>
          </div>

          {/* show on large screen only*/}
          <div className=" flex gap-10 items-center flex hidden md:flex">
            <TbInfoTriangle
              className="h-4 w-4 text-[#EF6C00]"
              role="img"
              aria-label="Info"
            />
            <ul className="list-disc ">
              {firstTimeAccessRequests.length > 0 && (
                <li className="">
                  {firstTimeAccessRequests.length} kandidat(er) venter på
                  tilgang. Sjekk at kandidatene har møtt før du gir tilgang.
                </li>
              )}
              {secondTimeAccessRequests.length > 0 && (
                <li className="">
                  {secondTimeAccessRequests.length} kandidat(er) har eller har
                  hatt tilgang tidligere, men ber om tilgang på nytt.
                </li>
              )}
            </ul>
          </div>

          <div className="flex gap-2">
            <IoEyeOutline role="img" aria-label="se" className="h-6 w-5" />
            <span
              className="underline-offset-4 underline decoration-2 cursor-pointer text-xs md:text-base"
              onClick={handleShowRequests}
            >
              Se hvilke kandidater det gjelder
            </span>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default AccessRequestBanner;
