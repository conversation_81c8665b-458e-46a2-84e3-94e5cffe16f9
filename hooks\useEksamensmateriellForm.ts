"use client";

import { useState, useCallback } from "react";
import { FileRejection } from "react-dropzone";
import { BlobClient } from "@azure/storage-blob";
import { validateEksamensmateriellForm, validateFilename, sanitizeString } from "@/lib/shared/validation";
import { toast } from "@/components/ui/use-toast";


// Azure upload utility function
const uploadToAzure = async (file: File, guid: string, formData: any) => {
  const tokenResponse = await fetch(`/api/getuploadblobsastoken-oppgaver?guid=${encodeURIComponent(guid)}`);
  if (!tokenResponse.ok) {
    throw new Error('Kunne ikke hente opplasting-autorisasjon fra serveren. Sjekk internetttilkobling og prøv igjen.');
  }
  
  const { sastoken } = await tokenResponse.json();
  const blobClient = new BlobClient(sastoken);
  const blockBlobClient = blobClient.getBlockBlobClient();
  
  const buffer = await file.arrayBuffer();
  // Normalize filename to NFC form to prevent Mac NFD decomposition issues
  const normalizedFilename = file.name.normalize('NFC');
  await blockBlobClient.upload(buffer, buffer.byteLength, {
    metadata: {
      LanguageVariant: encodeURIComponent(formData.malForm),
      Mime: encodeURIComponent(file.type || 'application/pdf'),
      OrgFileName: encodeURIComponent(normalizedFilename),
      FileExtension: encodeURIComponent('.' + (normalizedFilename.split('.').pop() || 'pdf'))
    }
  });
  
  return guid;
};

// External verification DELETE utility function
const deleteExternalVerification = async (documentGuid: string) => {
  const response = await fetch("/api/admin/delete-verification", {
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ documentGuid }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: "Unknown error" }));
    throw new Error(errorData.error || "Kunne ikke kansellere ekstern verifisering");
  }

  return response.json();
};

// Database operations
const saveNewFiles = async (filesData: any[]) => {
  const response = await fetch("/api/admin/save-eksamensmateriell", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ eksamensmateriellData: filesData }),
  });

  const responseData = await response.json();

  if (!response.ok) {
    throw new Error(responseData.error || "Kunne ikke lagre nye filer i databasen");
  }

  // Check if the operation was successful
  if (!responseData.success) {
    throw new Error(responseData.error || responseData.message || "Lagring av filer feilet");
  }

  return responseData;
};

const replaceFiles = async (filesData: any[], searchFormData?: { fagkode: string; eksamensperiode: string; variant?: string }, oldBlobReference?: string) => {
  const response = await fetch("/api/admin/replace-eksamensmateriell", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      eksamensmateriellData: filesData,
      oldBlobReference: oldBlobReference,
      searchFormData: searchFormData
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: "Unknown error" }));
    throw new Error(errorData.error || "Kunne ikke erstatte filer i databasen");
  }

  return response.json();
};

interface FormData {
  eksamensmaterielltype: string;
  fagkode: string;
  variant: string;
  eksamensperiode: string;
  eksamensdel: string;
  malForm: string;
  opphavsrett: boolean;
  vedlegg: boolean;
}

interface UploadedFile {
  file: File;
  id: string;
  replacingFileId?: string; // ID of existing file being replaced
  replacingFilename?: string; // Filename of existing file being replaced
  isVerified?: boolean; // Track if file has been verified
  verificationData?: {
    documentGuid: string;
  }; // Store verification data for potential DELETE call
}

export const useEksamensmateriellForm = () => {
  const [formData, setFormData] = useState<FormData>({
    eksamensmaterielltype: "",
    fagkode: "",
    variant: "",
    eksamensperiode: "",
    eksamensdel: "",
    malForm: "",
    opphavsrett: false,
    vedlegg: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [totalSize, setTotalSize] = useState(0);
  const [replacementMode, setReplacementMode] = useState<{
    fileId: string;
    filename: string;
    metadata: {
      kategori: string;
      malform: string;
      eksamensdel: string;
    };
  } | null>(null);

  const handleInputChange = useCallback((field: keyof FormData, value: string | boolean) => {
    // Sanitize input to prevent injection attacks (only for string values)
    const sanitizedValue = typeof value === 'string' ? sanitizeString(value) : value;
    
    setFormData(prev => ({
      ...prev,
      [field]: sanitizedValue
    }));
  }, []);

  const fileValidator = useCallback((file: File) => {
    // PDF file type validation
    if (file.type !== 'application/pdf') {
      return {
        code: "invalid-file-type",
        message: "Kun PDF-filer er tillatt"
      };
    }

    // Additional check for file extension
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      return {
        code: "invalid-file-extension",
        message: "Kun PDF-filer er tillatt"
      };
    }

    // File size validation
    if (file.size > 200 * 1024 * 1024) { // 200MB limit
      return {
        code: "file-too-large",
        message: "Filen er for stor (maks 200MB)"
      };
    }

    // Minimum file size validation
    if (file.size < 5) { // 5 bytes minimum
      return {
        code: "file-too-small",
        message: "Filen er for liten"
      };
    }

    // Filename security validation
    const filenameValidation = validateFilename(file.name);
    if (!filenameValidation.isValid) {
      return {
        code: "invalid-filename",
        message: filenameValidation.error || "Ugyldig filnavn"
      };
    }

    return null;
  }, []);

  const handleFileDrop = useCallback((acceptedFiles: File[], fileRejections: FileRejection[]) => {
    // Handle rejected files
    if (fileRejections.length > 0) {
      fileRejections.forEach(rejection => {
        console.error("File rejected:", rejection.file.name, rejection.errors);
      });
    }

    // Handle accepted files
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      file,
      id: `${Date.now()}-${Math.random()}`,
      ...(replacementMode && {
        replacingFileId: replacementMode.fileId,
        replacingFilename: replacementMode.filename
      })
    }));

    if (replacementMode) {
      // If in replacement mode, replace any existing replacement files
      setUploadedFiles(prev => {
        const withoutReplacements = prev.filter(f => !f.replacingFileId);
        return [...withoutReplacements, ...newFiles];
      });
      setReplacementMode(null);
    } else {
      // Normal file addition
      setUploadedFiles(prev => [...prev, ...newFiles]);
    }
    
    const newSize = acceptedFiles.reduce((acc, file) => acc + file.size, 0);
    setTotalSize(prev => prev + newSize);
  }, [replacementMode]);

  const removeFile = useCallback(async (fileId: string) => {
    const fileToRemove = uploadedFiles.find(f => f.id === fileId);
    if (!fileToRemove) return;

    try {
      // If the file has been verified, call DELETE endpoint to cancel verification
      if (fileToRemove.isVerified && fileToRemove.verificationData) {
        await deleteExternalVerification(fileToRemove.verificationData.documentGuid);
        toast({
          variant: "success",
          title: "Fil kansellert",
          description: `"${fileToRemove.file.name}" ble kansellert`,
        });
      }
    } catch (error) {
      console.error("Failed to cancel verification for file:", error);
      toast({
        variant: "destructive",
        title: "Kunne ikke kansellere verifisering",
        description: `Filen "${fileToRemove.file.name}" ble fjernet, men verifisering kunne ikke tilbakekalles fra PAS`,
      });
    }

    // Remove file from local state regardless of DELETE call result
    setUploadedFiles(prev => {
      setTotalSize(prevSize => prevSize - fileToRemove.file.size);
      return prev.filter(f => f.id !== fileId);
    });
  }, [uploadedFiles]);

  const handleReplaceFile = (
    existingFileId: string, 
    existingFilename: string, 
    metadata: { kategori: string; malform: string; eksamensdel: string }
  ) => {
    setReplacementMode({ 
      fileId: existingFileId, 
      filename: existingFilename,
      metadata 
    });

    // Auto-fill form data with existing metadata (but keep fagkode, variant, eksamensperiode unchanged)
    setFormData(prev => ({
      ...prev,
      eksamensmaterielltype: metadata.kategori,
      eksamensdel: metadata.eksamensdel,
      malForm: metadata.malform
    }));

    toast({
      variant: "info",
      title: "Klar for erstatning",
      description: `Klar for å erstatte "${existingFilename}". Metadata er utfylt automatisk. Last opp en ny fil for å erstatte den.`,
    });
  };

  const cancelReplacement = () => {
    setReplacementMode(null);
  };

  const handleDeleteFile = async (fileId: string, filename: string, blobReference: string) => {
    try {
      // First delete from blob storage and database
      const response = await fetch(`/api/admin/delete-eksamensmateriell?fileId=${encodeURIComponent(fileId)}&blobReference=${encodeURIComponent(blobReference)}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kunne ikke slette filen');
      }

      const result = await response.json();

      // Then call external verification DELETE endpoint using blobReference as documentGuid
      try {
        await deleteExternalVerification(blobReference);
      } catch (verificationError) {
        console.error("External verification DELETE failed:", verificationError);
        // Don't throw error here - file is already deleted from our system
        toast({
          variant: "destructive",
          title: "Delvis feil",
          description: `"${filename}" ble slettet, men ekstern verifisering kunne ikke tilbakekalles`,
        });
        return result;
      }

      toast({
        variant: "success",
        title: "Fil slettet",
        description: `"${filename}" ble kansellert og slettet`,
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'En ukjent feil oppstod under sletting av filen. Prøv igjen.';
      toast({
        variant: "destructive",
        title: `Kunne ikke slette "${filename}"`,
        description: errorMessage,
      });
      throw error;
    }
  };

  // Check if all required fields are filled and at least one file is uploaded
  const isFormValid = useCallback(() => {
    return (
      formData.eksamensmaterielltype &&
      formData.fagkode &&
      formData.eksamensperiode &&
      formData.eksamensdel &&
      formData.malForm &&
      uploadedFiles.length > 0
    );
  }, [formData, uploadedFiles.length]);

  const handleVerifyMaterial = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Convert fagkode to uppercase for backend processing
    const upperCaseFagkode = formData.fagkode.toUpperCase();
    
    // Security validation first
    const securityValidation = validateEksamensmateriellForm({
      fagkode: upperCaseFagkode,
      variant: formData.variant,
      eksamensperiode: formData.eksamensperiode,
      eksamensmaterielltype: formData.eksamensmaterielltype,
      eksamensdel: formData.eksamensdel,
      malForm: formData.malForm
    });

    if (!securityValidation.isValid) {
      toast({
        variant: "destructive",
        title: "Sikkerhetsfeil",
        description: securityValidation.errors.join(", "),
      });
      return;
    }
    
    if (!isFormValid()) {
      toast({
        variant: "destructive",
        title: "Ufullstendig skjema",
        description: "Vennligst fyll ut alle påkrevde felter og last opp en fil",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Upload each file to Azure
      const uploadPromises = uploadedFiles.map(async (uploadedFile) => {
        const guid = uploadedFile.replacingFileId || crypto.randomUUID().toLowerCase();
        return uploadToAzure(uploadedFile.file, guid, formData);
      });

      const uploadResults = await Promise.all(uploadPromises);
      
      // Prepare data for database insertion
      const eksamensmateriellData = uploadResults.map((guid, index) => {
        const uploadedFile = uploadedFiles[index];
        // Normalize filename to NFC form to prevent Mac NFD decomposition issues
        const normalizedFilename = uploadedFile.file.name.normalize('NFC');
        return {
          // eksamensmateriellfilID is now auto-generated, no need to set it
          blobReferanse: guid, // GUID for blob reference
          eksamensmateriellkategori: formData.eksamensmaterielltype,
          eksamensmateriellMalform: formData.malForm,
          fileSize: uploadedFile.file.size,
          mimeType: uploadedFile.file.type || 'application/octet-stream',
          originalFilename: normalizedFilename,
          // Additional fields for creating EksamensdelEksamensmateriell relationship
          fagkode: upperCaseFagkode,
          variant: formData.variant,
          eksamensperiode: formData.eksamensperiode,
          eksamensdel: formData.eksamensdel,
          // New checkbox fields for external verification
          opphavsrett: formData.opphavsrett,
          vedlegg: formData.vedlegg
        };
      });

      // Mark files as verified and store verification data
      setUploadedFiles(prev => prev.map((file, index) => ({
        ...file,
        isVerified: true,
        verificationData: {
          documentGuid: uploadResults[index],
        }
      })));

      // Separate new files and replacement files
      const newFiles = eksamensmateriellData.filter((_, index) => !uploadedFiles[index].replacingFileId);
      const replacementFiles = eksamensmateriellData.filter((_, index) => uploadedFiles[index].replacingFileId);

      let dbResult: any = { success: true, savedCount: 0, failedCount: 0 };

      // Handle new files
      if (newFiles.length > 0) {
        const newFilesResult = await saveNewFiles(newFiles);
        if (!newFilesResult.success) {
          // Include more details if available
          const errorDetails = newFilesResult.details ? ` (${newFilesResult.details})` : "";
          throw new Error(newFilesResult.error + errorDetails || "Kunne ikke lagre nye filer i databasen. Kontroller internetttilkobling og prøv igjen.");
        }
        dbResult.savedCount += newFilesResult.savedCount;
        dbResult.failedCount += newFilesResult.failedCount;
      }

      // Handle replacement files
      if (replacementFiles.length > 0) {
        const searchFormData = {
          fagkode: formData.fagkode,
          eksamensperiode: formData.eksamensperiode,
          variant: formData.variant
        };
        // Get oldBlobReference from the first replacement file
        const oldBlobReference = uploadedFiles.find(f => f.replacingFileId)?.replacingFileId;
        const replaceResult = await replaceFiles(replacementFiles, searchFormData, oldBlobReference);
        if (!replaceResult.success) {
          // Include errors array if available
          const errorDetails = replaceResult.errors ? ` Detaljer: ${replaceResult.errors.join('; ')}` : "";
          throw new Error((replaceResult.error || "Kunne ikke erstatte eksisterende filer i databasen") + errorDetails);
        }
        dbResult.savedCount += replaceResult.savedCount;
        dbResult.failedCount += replaceResult.failedCount;
      }
      
      // Check if any database saves failed
      if (dbResult.failedCount > 0) {
        throw new Error(`Kunne ikke lagre ${dbResult.failedCount} av ${eksamensmateriellData.length} filer i databasen. Sjekk at alle metadata-felter er riktig utfylt og prøv igjen.`);
      }
      
      toast({
        variant: "success",
        title: "Verifisering fullført!",
        description: `Eksamensmateriell ble verifisert og lagret`,
      });
      
      // Reset form on success
      setFormData({
        eksamensmaterielltype: "",
        fagkode: "",
        variant: "",
        eksamensperiode: "",
        eksamensdel: "",
        malForm: "",
        opphavsrett: false,
        vedlegg: false,
      });
      setUploadedFiles([]);
      setTotalSize(0);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Verifisering feilet",
        description: error instanceof Error ? error.message : "En ukjent feil oppstod under verifisering og lagring. Kontroller internetttilkobling og prøv igjen.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = useCallback(() => {
    setReplacementMode(null);
    setFormData({
      eksamensmaterielltype: "",
      fagkode: "",
      variant: "",
      eksamensperiode: "",
      eksamensdel: "",
      malForm: "",
      opphavsrett: false,
      vedlegg: false,
    });
    setUploadedFiles([]);
    setTotalSize(0);
  }, []);

  return {
    // State
    formData,
    uploadedFiles,
    totalSize,
    isLoading,
    replacementMode,
    
    // Handlers
    handleInputChange,
    handleFileDrop,
    removeFile,
    handleVerifyMaterial,
    handleReset,
    handleReplaceFile,
    cancelReplacement,
    handleDeleteFile,
    
    // State setters (for direct manipulation)
    setUploadedFiles,
    setTotalSize,
    
    // Computed
    isFormValid,
    fileValidator
  };
};