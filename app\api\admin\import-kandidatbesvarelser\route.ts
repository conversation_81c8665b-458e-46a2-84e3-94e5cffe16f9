import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/authOptions';
import { BesvarelseService } from '@/db/services/besvarelseService';
import { ISession } from '@/interface/ISession';
import { getAppInsightsServer } from '@/lib/server/appInsightsServer';

// Initialize Application Insights for logging and monitoring
const telemetryClient = getAppInsightsServer();

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let eksamensperiode: string | undefined;

  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidatbesvarelser_Unauthorized',
        properties: {
          timestamp: new Date().toISOString(),
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      });
      return NextResponse.json({ error: 'Ikke autorisert' }, { status: 401 });
    }

    // Authorization check - verify administrator role
    const userRoles = Array.isArray(session.user.role) ? session.user.role : [session.user.role];
    const hasAdminRole = userRoles.some(role => role.includes('urn:udir:pgsa:administrator'));

    if (!hasAdminRole) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidatbesvarelser_Forbidden',
        properties: {
          userId: session.user.userInfo.name,
          userRoles: userRoles.join(','),
          timestamp: new Date().toISOString()
        }
      });
      return NextResponse.json({ error: 'Ikke tilgang' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    eksamensperiode = body.eksamensperiode;

    if (!eksamensperiode || typeof eksamensperiode !== 'string') {
      return NextResponse.json({ error: 'Eksamensperiode er påkrevd og må være en tekststreng' }, { status: 400 });
    }

    // Trim whitespace and validate examination period format (e.g., H-2025, V-2026)
    eksamensperiode = eksamensperiode.trim();
    const eksamensperiodeRegex = /^[HV]-\d{4}$/;

    if (!eksamensperiodeRegex.test(eksamensperiode)) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidatbesvarelser_InvalidFormat',
        properties: {
          userId: session.user.userInfo.name,
          eksamensperiode,
          timestamp: new Date().toISOString()
        }
      });
      return NextResponse.json({
        error: 'Ugyldig eksamensperiode format. Kun H-ÅÅÅÅ eller V-ÅÅÅÅ er tillatt (f.eks. H-2025, V-2026)'
      }, { status: 400 });
    }

    // Additional validation: Check if year is reasonable (3 years back to 1 year forward)
    const [season, yearStr] = eksamensperiode.split('-');
    const year = parseInt(yearStr, 10);
    const currentYear = new Date().getFullYear();
    const minYear = currentYear - 3;
    const maxYear = currentYear + 1;

    if (year < minYear || year > maxYear) {
      telemetryClient?.trackEvent({
        name: 'ImportKandidatbesvarelser_InvalidYear',
        properties: {
          userId: session.user.userInfo.name,
          eksamensperiode,
          year: year.toString(),
          timestamp: new Date().toISOString()
        }
      });
      return NextResponse.json({
        error: `Ugyldig årstall. Årstall må være mellom ${minYear} og ${maxYear}`
      }, { status: 400 });
    }

    // Log start of import operation
    telemetryClient?.trackEvent({
      name: 'ImportKandidatbesvarelser_Started',
      properties: {
        userId: session.user.userInfo.name,
        eksamensperiode,
        timestamp: new Date().toISOString()
      }
    });

    // Execute import with proper error handling
    const userId = session.user.userInfo.userId;
    const besvarelseService = new BesvarelseService();
    const result = await besvarelseService.importBesvarelser(eksamensperiode, userId);

    const duration = Date.now() - startTime;

    // Log successful completion
    telemetryClient?.trackEvent({
      name: 'ImportKandidatbesvarelser_Completed',
      properties: {
        userId: session.user.userInfo.name,
        eksamensperiode,
        importedBesvarelsesdelerCount: result.besvarelsesdelerCount.toString(),
        importedBesvarelsefilerCount: result.besvarelsefilerCount.toString(),
        errorCount: result.errorCount.toString(),
        duration: duration.toString(),
        timestamp: new Date().toISOString()
      }
    });

    telemetryClient?.trackMetric({
      name: 'ImportKandidatbesvarelser_Duration',
      value: duration
    });

    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        eksamensperiode,
        importedCount: result.besvarelsesdelerCount + result.besvarelsefilerCount,
        errorCount: result.errorCount,
        deletedCount: 0, // Not implementing delete for now
        user: session.user.userInfo.name,
        errorDetails: result.errorDetails || []
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Ukjent feil';

    // Log error with comprehensive details
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        operation: 'ImportKandidatbesvarelser',
        eksamensperiode: eksamensperiode || 'unknown',
        errorMessage,
        duration: duration.toString(),
        timestamp: new Date().toISOString()
      }
    });

    console.error('Import error:', error);

    return NextResponse.json({
      success: false,
      message: 'Import av kandidatbesvarelser feilet',
      error: errorMessage,
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
