"use client";

import { useState } from "react";
import { ChevronsUpDown, Check, X } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/shared/utils";

interface Props {}

const AbsencePopover: React.FC<Props> = () => {
  const [openStatus, setOpenStatus] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const statusFilterLabel = "absenceStatus";

  const absenceStatus = [
    "Møtt",
    "Dokumentert fravær",
    "Ikke-dokumentert fravær",
  ] as const;

  // Get selected statuses from URL
  const getSelectedStatuses = (): string[] => {
    const statuses = searchParams.get(statusFilterLabel)?.split(",") || [];
    return statuses.filter((status) => absenceStatus.includes(status as any));
  };

  // Update URL with selected statuses
  const updateQueryString = (statuses: string[]) => {
    const params = new URLSearchParams(searchParams.toString());

    if (statuses.length > 0) {
      params.set(statusFilterLabel, statuses.join(","));
    } else {
      params.delete(statusFilterLabel);
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleStatusChange = (status: string, checked: boolean) => {
    const currentStatuses = getSelectedStatuses();
    const newStatuses = checked
      ? [...currentStatuses, status]
      : currentStatuses.filter((s) => s !== status);

    updateQueryString(newStatuses);
  };

  const filteredStatuses = absenceStatus.filter((status) =>
    status.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedStatuses = getSelectedStatuses();

  return (
    <>
      <Popover open={openStatus} onOpenChange={setOpenStatus}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-between text-left font-medium w-[200px]"
            )}
          >
            <div className="flex items-center truncate">
              {selectedStatuses.length > 0 ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm">Oppmøte</span>
                  <Badge variant="secondary" className="bg-mint">
                    {selectedStatuses.length}
                  </Badge>
                </div>
              ) : (
                "Oppmøte"
              )}
            </div>
            <ChevronsUpDown
              className="ml-2 h-4 w-4 shrink-0 opacity-50 transition-transform duration-200"
              role="img"
              aria-label="FitrerIkon"
              style={{
                transform: openStatus ? "rotate(180deg)" : "rotate(0deg)",
              }}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <div className="w-[200px]">
            <Command>
              <CommandList>
                {filteredStatuses.length > 0 ? (
                  <CommandGroup className="p-1">
                    {filteredStatuses.map((status) => (
                      <CommandItem
                        key={status}
                        value={status}
                        onSelect={() => {
                          const isSelected = selectedStatuses.includes(status);
                          handleStatusChange(status, !isSelected);
                        }}
                        className="flex items-center gap-2 px-2 py-1.5 hover:bg-gray-100 rounded-md cursor-pointer transition-colors duration-150"
                      >
                        <div className="flex items-center flex-1 gap-2">
                          <Checkbox
                            checked={selectedStatuses.includes(status)}
                            onCheckedChange={(checked) => {
                              handleStatusChange(status, checked as boolean);
                            }}
                            className="border-gray-300"
                            onClick={(e) => e.stopPropagation()}
                          />
                          <span className="text-sm">{status}</span>
                        </div>
                        {selectedStatuses.includes(status) && (
                          <Check className="h-4 w-4 text-mint" />
                        )}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                ) : (
                  <CommandEmpty>Ingen resultater funnet</CommandEmpty>
                )}
              </CommandList>
            </Command>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default AbsencePopover;
