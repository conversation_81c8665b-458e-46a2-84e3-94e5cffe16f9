"use server";

import dayjs from "dayjs";

import utc from "dayjs/plugin/utc";
import { getAppInsightsServer } from "./appInsightsServer";
import { getAccessToken } from "./getAccessToken";
import { getClientIp } from "./getClientIp";
import { NextRequest } from "next/server";
import { getPgsaStatusInfoFromRedis } from "@/app/lib/redisHelper";
import { getServerSession } from "next-auth";
import { ISession } from "@/interface/ISession";
import { authOptions } from "@/app/api/auth/authOptions";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;

const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

dayjs.extend(utc);

const telemetryClient = getAppInsightsServer();

export async function sendOtaRequest(
  request: NextRequest,
  candidateNumber: string,
  userId: string
): Promise<Response> {
  let response: Response | null = null;

  try {
    const userSession: ISession | null = await getServerSession(authOptions);

    const pgsaAccessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    const statusInfo = await getPgsaStatusInfoFromRedis(userId);

    const updateBody = {
      toggleValue: true,
      userName: userSession?.user.userInfo.userId,
      testPartId: statusInfo?.TestPartId,
      ipAddress: await getClientIp(request),
    };

    response = await fetch(`${PgsaApiUrl}/api/Monitor/${userId}/toggle-ota`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateBody),
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(
        `Feil ved oppdatering av status i PGSA. Feilkode: ${
          response.status
        }. Feilmelding: ${response.statusText}. Paylod: ${JSON.stringify(
          updateBody
        )}`
      );
    }

    return response;
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        message: "Error in sendOtaRequest",
        userId,
        candidateNumber,
      },
    });
    console.error("Error in updateCandidateStatus:", error);
    throw error;
  }
}
