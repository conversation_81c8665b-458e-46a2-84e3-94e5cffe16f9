import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET(request: Request) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const idToken = searchParams.get("idtoken") ?? null;

    if (idToken) {
      const redirectUrl =
        process.env.NEXTAUTH_UIDP_URL +
        "/connect/endsession?id_token_hint=" +
        idToken;

      const response = { url: redirectUrl };

      return NextResponse.json(response);
    }

    const redirectUrl = process.env.NEXTAUTH_UIDP_URL + "/connect/endsession";

    const response = { url: redirectUrl };

    return NextResponse.json(response);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "federatedlogout",
        response: error instanceof Error ? error.message : "Unknown error",
      },
    });
    console.log("Error", error);
  }
}
