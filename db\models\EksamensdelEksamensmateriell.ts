import "reflect-metadata";
import {
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { Eksamensdel } from "./Eksamensdel";
import { Eksamensmateriell } from "./Eksamensmateriell";

@Entity("EksamensdelEksamensmateriell")
@Index(["EksamensdelID", "EksamensmateriellID"], { unique: true })
export class EksamensdelEksamensmateriell {
  @PrimaryColumn({ type: "int" })
  EksamensdelID!: number;

  @PrimaryColumn({ type: "int" })
  EksamensmateriellID!: number;

  @CreateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  CreatedDate!: Date;

  // Relations
  @ManyToOne(() => Eksamensdel, { onDelete: "CASCADE" })
  @JoinColumn({ name: "EksamensdelID" })
  eksamensdel!: Eksamensdel;

  @ManyToOne(() => Eksamensmateriell, { onDelete: "CASCADE" })
  @JoinColumn({ name: "EksamensmateriellID" })
  eksamensmateriell!: Eksamensmateriell;
}