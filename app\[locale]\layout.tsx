import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import NavBar from "@/components/navbar/navbar";
import { locales } from "@/middleware";
import "./globals.css";
import { NextAuthProvider } from "./nextAuthProvider";
import { getServerSession } from "next-auth";
import { authOptions } from "../api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { RoleProvider } from "@/context/RoleContext";
import { Toaster } from "@/components/ui/toaster";
import { FileProvider } from "@/hooks/useFileHandler";

import Footer from "@/components/footer";
import { SchoolDataProvider } from "@/context/SchoolDataContext";
import { redirect } from "next/navigation";

import "dayjs/locale/nb"; // Importerer norsk locale
import dayjs from "dayjs";
import { FileProviderMonitor } from "@/hooks/useFilehandlerForMonitor";

dayjs.locale("nb"); // Setter locale til norsk

const roboto = Roboto({
  weight: ["400", "100", "700", "300", "500", "900"],
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "PGS-admin",
  description: "Admin for PGS",
  icons: {
    icon: "/favikon.png",
  },
};

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { locale: string };
}>) {
  const { locale } = await params;

  if (!locales.includes(locale as any)) {
    // Handle invalid locale
  }

  const userSessionData = (await getServerSession(
    authOptions
  )) as ISession | null;

  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/api/auth/signin");
  }

  return (
    <html lang="nb" data-theme="udir">
      <body className={`${roboto.className} flex flex-col min-h-screen`}>
        <NextAuthProvider session={await getServerSession()}>
          <RoleProvider>
            <FileProvider>
              <FileProviderMonitor>
                <SchoolDataProvider>
                  <div className="flex flex-col min-h-screen">
                    <header className="sticky top-0 z-50">
                      {userSessionData && (
                        <NavBar
                          session={userSessionData}
                          userRoles={userSessionData.user.role}
                        />
                      )}
                    </header>
                    <main className="flex-grow ">{children}</main>
                    <Footer />
                  </div>
                </SchoolDataProvider>
              </FileProviderMonitor>
            </FileProvider>
            <Toaster />
          </RoleProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
