import { useState } from "react";
import { Check, ChevronDown } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Button } from "@/components/ui/button";

interface CustomDropdownProps {
  value: string;
  onValueChange: (value: string) => void;
  options: readonly string[] | string[] | { value: string; label: string }[];
  placeholder: string;
  disabled?: boolean;
  label: string;
  formatOption?: (option: string) => string;
  id?: string;
  "aria-required"?: boolean;
}

export function CustomDropdown({
  value,
  onValueChange,
  options,
  placeholder,
  disabled = false,
  label,
  formatOption,
  id,
  "aria-required": ariaRequired
}: CustomDropdownProps) {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          id={id}
          role="combobox"
          aria-expanded={open}
          aria-haspopup="listbox"
          aria-required={ariaRequired}
          className={`w-full justify-between transition-colors duration-200 ${
            open ? "bg-udirLightAzure-100" : ""
          } ${disabled ? "bg-gray-200 cursor-not-allowed" : ""} ${
            value ? "border-green-500 bg-green-50" : "border-gray-300"
          }`}
          aria-label={`${label}: ${value || placeholder}`}
        >
          <span className={`flex items-center gap-2 ${
            value ? "text-foreground" : "text-muted-foreground"
          }`}>
            {value && (
              <Check className="h-4 w-4 text-green-600" aria-hidden="true" />
            )}
            {value ? (() => {
              if (formatOption) return formatOption(value);
              const option = Array.isArray(options) && options.find(opt => typeof opt === 'object' && opt.value === value);
              return option && typeof option === 'object' ? option.label : value;
            })() : placeholder}
          </span>
          <ChevronDown
            className="ml-2 h-4 w-4 shrink-0 opacity-50 transition-transform duration-200"
            style={{
              transform: open ? "rotate(180deg)" : "rotate(0deg)",
            }}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-full p-0 shadow-lg"
        align="start"
        sideOffset={4}
        style={{
          width: "var(--radix-popover-trigger-width)",
        }}
      >
        <Command className="rounded-sm border border-gray-200 bg-udirLightAzure-100">
          <CommandList
            className="max-h-64 overflow-auto"
            role="listbox"
            aria-label={`Alternativer for ${label}`}
          >
            <CommandEmpty className="py-6 text-center text-sm text-gray-500">
              Ingen resultater funnet
            </CommandEmpty>
            <CommandGroup className="p-1">
              {options.map((option) => {
                const optionValue = typeof option === 'string' ? option : option.value;
                const optionLabel = typeof option === 'string' ? (formatOption ? formatOption(option) : option) : option.label;
                
                return (
                  <CommandItem
                    key={optionValue}
                    value={optionValue}
                    onSelect={() => {
                      onValueChange(optionValue);
                      setOpen(false);
                    }}
                    className="flex items-center gap-2 px-2 py-1.5 hover:bg-udirLightAzure-300 rounded-md cursor-pointer transition-colors duration-150"
                  >
                    <span className="text-sm">
                      {optionLabel}
                    </span>
                    {value === optionValue && (
                      <Check className="ml-auto h-4 w-4 text-mint" />
                    )}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}