"use client";
import { useEffect, useState, useCallback, useMemo } from "react";
import React from "react";
import { Search, X } from "lucide-react";
import { useRole } from "@/context/RoleContext";
import { DataTable } from "@/components/tables/kandidatmonitor/candidate-list/data-table";
import GroupPopover from "./groupPopoverSelector";
import StatusPopover from "./statusPopoverSelector";
import { useCandidate } from "@/context/CandidateMonitorContext";
import ErrorDisplay from "./ErrorDisplay";
import { Button } from "@/components/ui/button";
import { MdFilterAltOff } from "react-icons/md";
import { TableSkeleton } from "@/components/tableSkeleton";
import { PGSMonitorSkeleton } from "./pgsMonitorSkeleton";
import { useRouter, useSearchParams } from "next/navigation";
import debounce from "lodash/debounce";
import { Input } from "@/components/ui/input";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import AccessRequestBanner from "./accessRequestBanner";
import { SignalRConnectionStatusBanner } from "./signalRConnectionStatusBanner";
import { ICandidate } from "@/interface/ICandidate";
import PermissionFilter from "./permissionFilter";
import {
  IAccessRequestUpdate,
  useAccessRequests,
} from "@/hooks/accessRequestProvider";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

// Helper function to format candidate status
const formatCandidateStatus = (
  candidateStatus: CandidateStatusEnum,
  examStartDate: string,
  isPreauthenticated: boolean
): string => {
  const statusMap = {
    [CandidateStatusEnum.IkkeInnlogget]: "Ikke lastet opp",
    [CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert]:
      "Ikke lastet opp",
    [CandidateStatusEnum.InnloggetAutentisert]: "Ikke lastet opp",
    [CandidateStatusEnum.LastetOpp]: "Laster opp",
    [CandidateStatusEnum.Levert]: "Levert digitalt",
    [CandidateStatusEnum.LevertManuelt]: "Sendes i posten",
    [CandidateStatusEnum.DokumentertFravaer]: "Dokumentert fravær",
    [CandidateStatusEnum.IkkeDokumentertFravaer]: "Ikke-dokumentert fravær",
  };

  return statusMap[candidateStatus] || "Ukjent status";
};

// Types for filter parameters
interface FilterParams {
  groupFilter: string[];
  statusFilter: string[];
  performanceFilter: string[];
  permissionFilter: string[];
}

const getActiveFilterCount = (
  filterParams: FilterParams,
  searchInput: string
): number => {
  let count = 0;

  if (searchInput) count++;

  count += filterParams.groupFilter.length;
  count += filterParams.statusFilter.length;
  count += filterParams.performanceFilter.length;
  count += filterParams.permissionFilter.length;

  return count;
};

export default function SearchForm() {
  const { selectedRole } = useRole();
  const {
    examPaper,
    isLoading,
    errors,
    updateErrors,
    redisResult,
    candidateStatus,
    updateVisibleCandidates,
    updateAuthorizedWithAccessRequests,
  } = useCandidate();
  const {
    accessRequests,
    error: accessRequestError,
    isLoading: accessRequestsLoading,
    isSubmitting,
  } = useAccessRequests();

  const router = useRouter();
  const searchParams = useSearchParams();
  const [previousSchoolId, setPreviousSchoolId] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState(
    () => searchParams.get("candidate") || ""
  );
  const [hideFilters, setHideFilters] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const [showIp, setShowIp] = useState(false);

  const paramKey = "showIp"; // Query param key

  const filterParams = useMemo((): FilterParams => {
    return {
      groupFilter: searchParams.get("groupCodes")?.split(",") || [],
      statusFilter:
        searchParams.get("statuses")?.split(",").filter(Boolean) || [],
      performanceFilter: searchParams.get("examPerformance")?.split(",") || [],
      permissionFilter: searchParams.get("permission")?.split(",") || [],
    };
  }, [searchParams]);

  const activeFilterCount = useMemo(() => {
    return getActiveFilterCount(filterParams, searchInput);
  }, [filterParams, searchInput]);

  useEffect(() => {
    const paramValue = searchParams.get("showIp");
    setShowIp(paramValue === "true"); // Convert string to boolean
  }, [searchParams]); // React to URL changes

  useEffect(() => {
    if (activeFilterCount > 0) {
      setHideFilters(false);
    }
  }, [activeFilterCount]);

  useEffect(() => {
    if (accessRequestError) {
      updateErrors(
        "Tilgangsforespørsler",
        true,
        "Klarte ikke å hente tilgangsforespørsler"
      );
    } else {
      updateErrors("Tilgangsforespørsler", false, "");
    }
  }, [accessRequestError]);

  // Update authorized candidates when access requests change
  useEffect(() => {
    if (accessRequests.length >= 0) {
      updateAuthorizedWithAccessRequests(accessRequests);
    }
  }, [accessRequests, updateAuthorizedWithAccessRequests]);

  const filterFunctions = useMemo(
    () => ({
      matchesSearchTerm: (candidate: any) => {
        const searchTerm = searchInput.toLowerCase().trim();
        return (
          !searchTerm ||
          candidate.candidateNumber.toLowerCase().includes(searchTerm) ||
          candidate.candidateName.toLowerCase().includes(searchTerm)
        );
      },

      matchesGroup: (groupCode: string) => {
        return (
          filterParams.groupFilter.length === 0 ||
          filterParams.groupFilter.includes(groupCode)
        );
      },

      matchesStatus: (candidate: any, examItem: any) => {
        if (filterParams.statusFilter.length === 0) return true;

        const getPartStatus = (partStatus: number, isPart2: boolean) => {
          const examStart = isPart2
            ? examItem.partTwoStartDateTime
            : examItem.partOneStartDateTime;

          return formatCandidateStatus(
            partStatus,
            examStart || "",
            candidate.isPreauthenticated
          );
        };

        const part1Status = getPartStatus(candidate.deliveryStatusPart1, false);
        const part2Status =
          candidate.deliveryStatusPart2 !== -1
            ? getPartStatus(candidate.deliveryStatusPart2, true)
            : null;

        return (
          filterParams.statusFilter.includes(part1Status) ||
          (part2Status && filterParams.statusFilter.includes(part2Status))
        );
      },

      matchesPermission: (
        candidate: ICandidate,
        accessRequests: IAccessRequestUpdate[],
        isAuthorized: boolean
      ) => {
        if (filterParams.permissionFilter.length === 0) return true;

        const candidateNumber = candidate.candidateNumber;
        const status =
          candidate.deliveryStatusPart2 !== -1
            ? candidate.deliveryStatusPart2
            : candidate.deliveryStatusPart1;
        
        const candidateState = candidateStatus[candidateNumber];
        const isBlocked = candidateState?.isBlocked || false;

        const hasAbsenceStatus = [
          CandidateStatusEnum.DokumentertFravaer,
          CandidateStatusEnum.IkkeDokumentertFravaer,
        ].includes(status);

        const isWaitingForAccess = accessRequests.some(
          (req: IAccessRequestUpdate) => req.candidateNumber === candidateNumber
        ) && !isAuthorized; // Exclude candidates who already have authorization

        const isRequestingNewAccess = isAuthorized && accessRequests.some(
          (req: IAccessRequestUpdate) => req.candidateNumber === candidateNumber
        );

        const isWaitingForExamStart =
          candidateState?.isWaitingForExam && isAuthorized;

        // Consistent logic: has access if authorized OR requesting new access
        const hasFullAccess = isAuthorized || isRequestingNewAccess;

        const noAccess =
          !isBlocked &&
          !hasAbsenceStatus &&
          !isWaitingForAccess &&
          !isAuthorized;

        const statusMap: Record<string, boolean> = {
          "Har tilgang": hasFullAccess,
          "Ikke tilgang": noAccess,
          "Digital tilgang sperret": isBlocked && !hasAbsenceStatus,
          "Dokumentert fravær":
            status === CandidateStatusEnum.DokumentertFravaer,
          "Ikke-dokumentert fravær":
            status === CandidateStatusEnum.IkkeDokumentertFravaer,
          "Venter på eksamensstart": isWaitingForExamStart,
          "Venter på tilgang": isWaitingForAccess,
        };

        for (const permission of filterParams.permissionFilter) {
          if (statusMap[permission]) {
            return true;
          }
        }

        return false;
      },
    }),
    [searchInput, filterParams, accessRequests, candidateStatus]
  );

  const updateUrlWithSearchParam = useMemo(
    () =>
      debounce((value: string) => {
        const params = new URLSearchParams(searchParams);
        value ? params.set("candidate", value) : params.delete("candidate");
        const newUrl = params.toString()
          ? `${window.location.pathname}?${params}`
          : window.location.pathname;
        router.push(newUrl, { scroll: false });
      }, 700),
    [searchParams, router]
  );

  useEffect(() => {
    if (selectedRole?.selectedSchoolId) {
      const currentSchoolId = selectedRole.selectedSchoolId;

      if (previousSchoolId && currentSchoolId !== previousSchoolId) {
        setSearchInput("");
        const baseUrl = window.location.pathname;
        router.replace(baseUrl);
      }

      setPreviousSchoolId(currentSchoolId);
    }
  }, [selectedRole?.selectedSchoolId, router, previousSchoolId]);

  useEffect(() => {
    return () => updateUrlWithSearchParam.cancel();
  }, [updateUrlWithSearchParam]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchInput(value);
      updateUrlWithSearchParam(value);
    },
    [updateUrlWithSearchParam]
  );

  const resetAllFilters = useCallback(() => {
    setSearchInput("");
    router.replace(window.location.pathname);
  }, [router]);

  const handleToggleShowIp = (checked: boolean) => {
    setShowIp(checked);
    const params = new URLSearchParams(searchParams.toString());

    if (checked) {
      params.set("showIp", "true");
    } else {
      params.delete("showIp");
    }

    router.replace(`?${params.toString()}`); // Update URL
  };

  const getFilteredCandidates = useMemo(() => {
    return examPaper.flatMap((item) =>
      item.candidates
        .filter((candidate) => {
          // Search is always applied if present (AND logic with search)
          if (
            searchInput.trim() !== "" &&
            !filterFunctions.matchesSearchTerm(candidate)
          ) {
            return false;
          }

          // Check if any filter category is active
          const hasGroupFilter = filterParams.groupFilter.length > 0;
          const hasStatusFilter = filterParams.statusFilter.length > 0;
          const hasPermissionFilter = filterParams.permissionFilter.length > 0;

          // If no filters are active, include all candidates that match the search
          if (!hasGroupFilter && !hasStatusFilter && !hasPermissionFilter) {
            return true;
          }

          // Group filter has AND logic with other filters
          if (hasGroupFilter && !filterFunctions.matchesGroup(item.groupCode)) {
            return false; // If group doesn't match, exclude regardless of other filters
          }

          // Check other filters (status, attendance, permission) with OR logic between them
          // Only needed if at least one of these filters is active
          if (hasStatusFilter || hasPermissionFilter) {
            const matchesStatus = hasStatusFilter
              ? filterFunctions.matchesStatus(candidate, item)
              : false;

            const matchesPermission = hasPermissionFilter
              ? filterFunctions.matchesPermission(
                  candidate,
                  accessRequests,
                  candidateStatus[candidate.candidateNumber]?.isAuthorized || false
                )
              : false;

            // OR logic between status and permission filters
            return matchesStatus || matchesPermission;
          }

          // If only group filter is active and we reached here, include the candidate
          return true;
        })
        .map((candidate) => ({
          ...candidate,
          groupCode: item.groupCode,
          groupName: item.groupName,
          subjectCode: item.subjectCode,
          subjectName: item.subjectName,
          examStartDate: item.examStartDate,
          partOneStartDateTime: item.partOneStartDateTime,
          partTwoStartDateTime: item.partTwoStartDateTime,
          testPeriod: item.testPeriod,
          redisObject: redisResult.filter(
            (obj) =>
              obj !== null && obj.candidateNumber === candidate.candidateNumber
          ),
        }))
    );
  }, [
    examPaper,
    filterFunctions,
    filterParams,
    redisResult,
    searchInput,
    candidateStatus,
    accessRequests,
  ]);

  useEffect(() => {
    if (getFilteredCandidates.length > 0) {
      // Send kun kandidatnumrene til konteksten
      const filteredCandidateNumbers = getFilteredCandidates.map(
        (candidate) => candidate.candidateNumber
      );
      updateVisibleCandidates(filteredCandidateNumbers);
    }
  }, [getFilteredCandidates, updateVisibleCandidates]);

  if (!selectedRole) {
    return <PGSMonitorSkeleton />;
  }

  return (
    <div className="flex flex-col gap-3">
      <section className="flex flex-col xl:flex-row gap-4 justify-between">
        {/* Søkeboks-seksjon */}
        <div className="flex-grow">
          <div className="flex flex-col gap-1">
            <span className="text-sm font-medium">Finn kandidater</span>
            <div className="relative flex-grow w-full lg:w-1/2 xl:w-2/3">
              <div className="flex items-center relative">
                {/* Søkeikon */}
                <div className="absolute inset-y-0 left-0 ml-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-500" />
                </div>

                {/* Input-felt */}
                <Input
                  type="text"
                  autoComplete="on"
                  placeholder="Søk etter kandidatnummer eller navn"
                  className="h-10 w-full pl-10 pr-10 bg-udirGray-100 py-2 text-sm border-0 border-b-2 border-black rounded-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0"
                  value={searchInput}
                  onChange={handleSearchChange}
                  aria-label="Filtrer på kandidatnavn eller nummer"
                />

                {/* X-knapp */}
                {searchInput && (
                  <button
                    onClick={() => {
                      const params = new URLSearchParams(searchParams);
                      params.delete("candidate");
                      const newUrl = params.toString()
                        ? `${window.location.pathname}?${params}`
                        : window.location.pathname;
                      router.push(newUrl, undefined);

                      setSearchInput("");
                    }}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-600"
                    aria-label="Tøm søk"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Filter-seksjon */}
        <div className="w-full md:w-auto mt-0 xl:mt-6">
          <div className="flex flex-col lg:flex-row lg:items-center gap-3 w-full">
            <span className="text-sm font-medium">Filtrering:</span>
            <div className="w-full lg:w-auto">
              <GroupPopover handleFilterChange={() => {}} />
            </div>
            <div className="w-full lg:w-auto">
              <StatusPopover />
            </div>
            <div className="w-full lg:w-auto">
              <PermissionFilter />
            </div>
            <Button
              variant="ghost"
              onClick={resetAllFilters}
              className="flex items-center gap-1 w-full lg:w-32"
              disabled={activeFilterCount === 0}
              aria-label={
                activeFilterCount > 0
                  ? `Nullstill alle filtre (${activeFilterCount} aktive)`
                  : "Ingen aktive filtre å nullstille"
              }
            >
              <MdFilterAltOff className="h-4 w-4" />
              Nullstill filter{" "}
              {activeFilterCount > 0 ? `(${activeFilterCount})` : ""}
            </Button>
            <div className="flex items-center space-x-2">
              <Switch
                checked={showIp}
                onCheckedChange={handleToggleShowIp}
                id="show-ip-switch"
                aria-label="Vis IP-adresser"
              />
              <Label htmlFor="show-ip-switch">Vis IP-adresser</Label>
            </div>
          </div>
        </div>
      </section>

      {errors.length > 0 && (
        <div className="mt-4">
          <ErrorDisplay errors={errors} />
        </div>
      )}

      <SignalRConnectionStatusBanner />

      <AccessRequestBanner
        accessRequests={accessRequests}
        examPaper={examPaper}
        candidateStatus={candidateStatus}
        resetSearchInput={() => setSearchInput("")}
      />

      {isLoading && examPaper.length === 0 && errors.length === 0 ? (
        <TableSkeleton />
      ) : (
        <section className="w-full my-0" aria-label="Resultat av søk">
          <h2 className="sr-only">Tabell med eksamenspapirer</h2>
          <DataTable
            data={getFilteredCandidates}
            examPaper={examPaper[0]}
            showIp={showIp}
          />
        </section>
      )}
    </div>
  );
}
