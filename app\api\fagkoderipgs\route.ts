import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/authOptions';
import { getDbConnection } from '@/db/connection';
import { ISession } from '@/interface/ISession';

export async function GET(request: NextRequest) {
  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Ikke autorisert' }, { status: 401 });
    }

    // Get eksamensperiode from query parameters
    const { searchParams } = new URL(request.url);
    const eksamensperiode = searchParams.get('eksamensperiode');

    if (!eksamensperiode) {
      return NextResponse.json({ error: 'Eksamensperiode er påkrevd' }, { status: 400 });
    }

    // Get database connection
    const AppDataSource = await getDbConnection();

    // Execute stored procedure to get consistent data structure
    const rawResults = await AppDataSource.query(
      'EXEC GetFagkodeeksamensWithDeler @Eksamensperiode = @0',
      [eksamensperiode]
    );

    // Group results by fagkodeeksamen and transform data
    // Filter out GS (Grunnleggende studieferdighet) entries from fagkoderipgs route
    const fagkodeMap = new Map();

    for (const row of rawResults) {
      // Skip entries with Opplaeringsniva = 'GS'
      if (row.Opplaeringsniva === 'GS') {
        continue;
      }

      const fagkodeId = row.FagkodeeksamensID;

      if (!fagkodeMap.has(fagkodeId)) {
        fagkodeMap.set(fagkodeId, {
          fagkodeeksamensID: row.FagkodeeksamensID,
          fagkode: row.Fagkode,
          variantkode: row.Variantkode,
          fagnavn: row.Fagnavn,
          eksamensdato: row.Eksamensdato,
          eksamenstid: row.Eksamenstid,
          varighet: row.Varighet,
          erTestFagkode: row.ErTestFagkode,
          oppgaveansvar: row.Oppgaveansvar,
          eksamensdeler: new Map() // Use Map to track eksamensdeler by ID
        });
      }

      // Add or get eksamensdel if it exists
      if (row.EksamensdelID) {
        const fagkodeData = fagkodeMap.get(fagkodeId);
        const eksamensdeler = fagkodeData.eksamensdeler;

        if (!eksamensdeler.has(row.EksamensdelID)) {
          eksamensdeler.set(row.EksamensdelID, {
            eksamensdelID: row.EksamensdelID,
            eksamensdelType: row.EksamensdelType,
            gjennomforingStart: row.GjennomforingStart,
            gjennomforingStopp: row.GjennomforingStopp
          });
        }
      }
    }

    // Transform data for frontend (simplified without files and plagiarism data)
    const transformedData = Array.from(fagkodeMap.values()).map(fagkode => {
      // Convert Map to array for processing
      const eksamensdelerArray = Array.from(fagkode.eksamensdeler.values());

      // Find del 2 based on EksamensdelType
      let todeltStartDel2 = null;
      let korrektEksamensdato = fagkode.eksamensdato ? `${fagkode.eksamensdato.getFullYear()}-${(fagkode.eksamensdato.getMonth() + 1).toString().padStart(2, '0')}-${fagkode.eksamensdato.getDate().toString().padStart(2, '0')}` : null;

      if (eksamensdelerArray?.length > 0) {
        // Filter out "Forberedelse" for all checks
        const valideDeler = eksamensdelerArray.filter((del: any) =>
          del.eksamensdelType && !del.eksamensdelType.toLowerCase().includes('forberedelse')
        );

        // If we have valid eksamensdeler, use the date from the first valid one
        if (valideDeler.length > 0 && (valideDeler[0] as any).gjennomforingStart) {
          const validDato = (valideDeler[0] as any).gjennomforingStart;
          korrektEksamensdato = `${validDato.getFullYear()}-${(validDato.getMonth() + 1).toString().padStart(2, '0')}-${validDato.getDate().toString().padStart(2, '0')}`;
        }

        // Look for any valid eksamensdel that contains "del2"
        const del2 = valideDeler.find((del: any) =>
          del.eksamensdelType && del.eksamensdelType.toLowerCase().includes('del2')
        );

        if ((del2 as any)?.gjennomforingStart) {
          todeltStartDel2 = new Date((del2 as any).gjennomforingStart).toISOString();
        }
      }

      return {
        fagkodeeksamensID: fagkode.fagkodeeksamensID,
        fagkode: fagkode.fagkode,
        variantkode: fagkode.variantkode,
        fagnavn: fagkode.fagnavn,
        eksamensdato: korrektEksamensdato,
        todeltStartDel2: todeltStartDel2,
        eksamenstid: fagkode.eksamenstid,
        varighet: fagkode.varighet,
        erTestFagkode: fagkode.erTestFagkode,
        oppgaveansvar: fagkode.oppgaveansvar
      };
    });

    return NextResponse.json({
      success: true,
      data: transformedData,
      count: transformedData.length
    });

  } catch (error) {
    console.error('Error fetching fagkoderipgs:', error);
    return NextResponse.json({
      error: 'En feil oppstod ved henting av fagkoder i PGS data',
      details: error instanceof Error ? error.message : 'Ukjent feil'
    }, { status: 500 });
  }
}