import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { ISession } from "@/interface/ISession";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

const telemetryClient = getAppInsightsServer();

export async function POST(request: NextRequest) {
  const { role } = await request.json();
  
  const session: ISession | null = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Valider rollen
  const userSessionData = (await getServerSession(authOptions)) as ISession;

  if (
    !userSessionData ||
    !userSessionData.user ||
    !userSessionData.user.role.includes(role)
  ) {
    return NextResponse.json({ error: "Ugyldig rolle" }, { status: 400 });
  }

  // Opprette responsen
  const response = NextResponse.json(
    { message: "Rolle satt vellykket" },
    { status: 200 }
  );

  // Sette cookien
  response.cookies.set("userRole", role, {
    httpOnly: true,
    secure:
      process.env.PGS_ENVIRONMENT && process.env.PGS_ENVIRONMENT === "localhost"
        ? false
        : true,
    sameSite: "strict",
    path: "/",
  });

  return response;
}
