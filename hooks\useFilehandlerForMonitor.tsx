"use client";

import React, {
  createContext,
  useEffect,
  useState,
  useContext,
  ReactNode,
} from "react";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { TestPartsEnum } from "@/enums/TestPart";
import { toast } from "@/components/ui/use-toast";
import { OperationEnum } from "@/enums/OperationEnum";
import { useBaseFileHandler, BaseFileContextType, FileHandlerConfig } from "./useBaseFileHandler";
import { useRole } from "@/context/RoleContext";
import { ActivityLogger, CandidateInfo } from "@/lib/shared/ActivityLogger";

// Define API endpoints specific to this monitor hook
const API_ENDPOINTS_MONITOR = {
  validate: "/api/pgsaValidate/monitor/{fileGuid}",
  delete: "/api/deleteFileMonitor/{fileGuid}",
  updateStatus: "/api/updateFileStatusForMonitor",
  download: "/api/downloadSingleSubmission?documentCode={documentCode}",
};

// Extended interface for monitor-specific functionality
interface MonitorFileContextType extends BaseFileContextType {
  candidateNumber: string;
  setCandidateNumber: React.Dispatch<React.SetStateAction<string>>;
  setUploadedFiles: React.Dispatch<React.SetStateAction<IUploadedFile[]>>;
  setStatusChecked: (status: boolean, fileGuid: string) => void;
  markFileAsNotDelivered: (fileGuid: string) => void;
}

const FileContext = createContext<MonitorFileContextType | undefined>(undefined);

function useFileHandlerForMonitor(): MonitorFileContextType {
  const [candidateNumber, setCandidateNumber] = useState("");
  const [userId, setUserId] = useState("");
  
  const dataFromStorage =
    typeof window !== "undefined" && window.sessionStorage
      ? window.sessionStorage.getItem("selectedCandidate")
      : '{"userId": ""}';
  const { selectedRole } = useRole();

  useEffect(() => {
    try {
      const data = dataFromStorage ? JSON.parse(dataFromStorage) : null;
      setUserId(data?.userId || "");
    } catch (e) {
      console.error("Error parsing selectedCandidate from sessionStorage:", e);
      setUserId("");
    }
  }, [dataFromStorage]);

  // Configuration for monitor file handler
  const monitorConfig: FileHandlerConfig = {
    componentName: "UseFileHandlerForMonitor",
    endpoints: API_ENDPOINTS_MONITOR,
    isGroupUpload: false,
    validateBeforeDelivery: (file: IUploadedFile) => {
      if (!file.Checked) {
        toast({
          variant: "destructive",
          title: "Feil ved levering av fil",
          description: "Du må markere at filen er sjekket for å få levert den.",
        });
        return false;
      }
      return true;
    },
    getUploadOperation: () => OperationEnum.StatusFileUploaded,
    getDeletionOperation: () => OperationEnum.StatusFileDeleted,
    getDeliveryOperation: (file: IUploadedFile) => {
      switch (file.TestPartId) {
        case TestPartsEnum.EksamenDel1:
          return OperationEnum.StatusFileDeliveredPart1;
        case TestPartsEnum.EksamenDel2:
          return OperationEnum.StatusFileDeliveredPart2;
        default:
          return OperationEnum.StatusFileDelivered;
      }
    },
    getCandidateInfo: () => {
      // Use candidate information from sessionStorage for monitor
      const candidateData = JSON.parse(dataFromStorage || '{}');
      return {
        userId: userId,
        firstName: candidateData.candidateName?.split(' ')[0] || "",
        lastName: candidateData.candidateName?.split(' ')[1] || "",
        registrationId: candidateData.candidateRegistrationId || userId,
        candidateNumber: candidateData.candidateNumber || "",
      };
    },
  };

  const baseHandler = useBaseFileHandler(monitorConfig);



  // Monitor-specific functions
  const setStatusChecked = async (status: boolean, fileGuid: string) => {
    const candidateData = JSON.parse(dataFromStorage || '{}');
    const file = baseHandler.uploadedFiles.find((f) => f.FileGuid === fileGuid);
    
    if (file) {
      const candidateInfo: CandidateInfo = {
        userId: userId,
        candidateName: candidateData.candidateName || "",
        candidateNumber: candidateData.candidateNumber || "",
        candidateRegistrationId: candidateData.candidateRegistrationId || userId,
      };

      await ActivityLogger.logFileChecked(
        file.Name,
        file.TestPartId,
        candidateInfo,
        `${selectedRole?.displayRoleName}`
      );
    }

    baseHandler.updateFile(fileGuid, { Checked: status });
    
    // Save checked status to session storage for persistence
    if (typeof window !== "undefined" && window.sessionStorage) {
      const storageKey = `fileChecked_${userId}_${fileGuid}`;
      if (status) {
        sessionStorage.setItem(storageKey, "true");
      } else {
        sessionStorage.removeItem(storageKey);
      }
    }
  };

  const markFileAsNotDelivered = (fileGuid: string) => {
    baseHandler.updateFile(fileGuid, { Delivered: false });
  };

  // Use the base handler's setUploadedFiles directly to ensure state synchronization
  const customSetUploadedFiles: React.Dispatch<React.SetStateAction<IUploadedFile[]>> = (value) => {
    // Update base handler state directly - this will trigger the useEffect above to sync local state
    baseHandler.setUploadedFiles(value);
  };

  return {
    ...baseHandler,
    // Use base handler's uploadedFiles directly since they're now synchronized
    candidateNumber,
    setCandidateNumber,
    setUploadedFiles: customSetUploadedFiles,
    setStatusChecked,
    markFileAsNotDelivered,
  };
}

export const FileProviderMonitor = ({ children }: { children: ReactNode }) => {
  const fileHandler = useFileHandlerForMonitor();
  return (
    <FileContext.Provider value={fileHandler}>{children}</FileContext.Provider>
  );
};

const useFileContext = (): MonitorFileContextType => {
  const context = useContext(FileContext);
  if (context === undefined) {
    throw new Error("useFileContext må brukes innenfor en FileProviderMonitor");
  }
  return context;
};

export default useFileContext;
