"use client";
import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { IoIosArrowDown } from "react-icons/io";
import { AiOutlineUser } from "react-icons/ai";
import { getSession, signOut } from "next-auth/react";
import <PERSON><PERSON> from "js-cookie";
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from "@/components/ui/menubar";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { IRoleObject } from "@/interface/IRoleObject";
import { IMenuItem } from "@/interface/IMenuItem";
import { useRole } from "@/context/RoleContext";
import { Button } from "../ui/button";
import { usePathname, useRouter } from "next/navigation";
import { getRoleObject, NavBarProps } from "./navBarUtils";
import { useSchoolData } from "@/context/SchoolDataContext";

function NavBar({ session, userRoles }: NavBarProps) {
  const router = useRouter();
  const { selectedRole, setSelectedRole } = useRole();
  const [accessibleMenuItems, setAccessibleMenuItems] = useState<IMenuItem[]>(
    []
  );
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { schools, pasxSchools, enhetsservice, skoleansvarlig } =
    useSchoolData();
  const [isRoleMenuOpen, setIsRoleMenuOpen] = useState(false);
  const [isRoleMenuOpenSmall, setIsRoleMenuOpenSmall] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownRefSmall = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsRoleMenuOpen(false);
      }
      if (
        dropdownRefSmall.current &&
        !dropdownRefSmall.current.contains(event.target as Node)
      ) {
        setIsRoleMenuOpenSmall(false);
      }
      if (
        isDropdownOpen &&
        event.target instanceof Element &&
        !event.target.closest(".dropdown")
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isDropdownOpen]);

  const pathname = usePathname();

  useEffect(() => {
    const getMenuItems = async () => {
      const encodedRole = encodeURIComponent(selectedRole?.role || "");
      const response = await fetch(
        `${window.location.origin}/api/getMenuItems?role=${encodedRole}`
      );
      const data: IMenuItem[] = await response.json();
      if (data) setAccessibleMenuItems(data);
    };
    if (selectedRole) getMenuItems();
  }, [selectedRole]);

  useEffect(() => {
    if (!selectedRole) {
      const roles = getRoleObject(
        session,
        schools,
        userRoles,
        pasxSchools,
        enhetsservice,
        skoleansvarlig
      );
      if (roles.length > 0) {
        setSelectedRole(roles[0]);
      }
    }
  }, [
    userRoles,
    selectedRole,
    setSelectedRole,
    session,
    schools,
    pasxSchools,
    enhetsservice,
    skoleansvarlig,
  ]);

  useEffect(() => {
    const currentPath = pathname;
    const activeMenuItem = accessibleMenuItems.find(
      (item) =>
        item.path === currentPath ||
        (item.children &&
          item.children.some((child) => child.path === currentPath))
    );
    setActiveItem(activeMenuItem ? activeMenuItem.label : null);
  }, [pathname, accessibleMenuItems]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      setOpenDropdown(null);
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleRoleSelect = (role: IRoleObject) => {
    setIsRoleMenuOpen(false); // Close menu after selection
    setIsRoleMenuOpenSmall(false); // Close mobile menu
    setSelectedRole(role);
  };

  const handleLogout = async () => {
    const session: any = await getSession();
    const queryParams = {
      idtoken: session?.user?.idToken,
    };
    const queryString = new URLSearchParams(queryParams).toString();
    const response = await fetch(
      `${window.location.origin}/api/federatedlogout?${queryString}`
    );
    const data = await response.json();
    if (response.ok) {
      Cookie.remove("next-auth.csrf-token");
      sessionStorage.clear();
      localStorage.removeItem("selectedRole");

      await signOut({ redirect: false });
      window.location.href = data.url;
    }
  };

  const renderMenuItems = (items: IMenuItem[]) => {
    return items.map((item) => {
      const isActive = activeItem === item.label;
      const activeClass = isActive ? "bg-mint text-black" : "";
      const isOpen = openDropdown === item.label;

      if (item.children) {
        return (
          <React.Fragment key={item.label}>
            {/* Desktop version */}
            <div className="hidden w-20 lg:block" ref={menuRef}>
              <Menubar
                className={`rounded-none text-white hover:bg-mint hover:text-black ${activeClass}`}
              >
                <MenubarMenu>
                  <MenubarTrigger
                    tabIndex={0}
                    className={`flex h-16 w-full items-center justify-center gap-2 rounded-none text-center cursor-pointer hover:text-black focus:text-black focus:bg-mint ${activeClass}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setOpenDropdown(isOpen ? null : item.label);
                      setIsRoleMenuOpen(false);
                    }}
                  >
                    {item.label}{" "}
                    <IoIosArrowDown
                      className={`h-4 w-4 transition-transform duration-200 ${
                        isOpen ? "rotate-180" : ""
                      }`}
                      role="img"
                      aria-label={isOpen ? "pil opp ikon" : "pil ned ikon"}
                    />
                  </MenubarTrigger>
                  <MenubarContent className="p-0">
                    {item.children.map((child) => (
                      <MenubarItem
                        key={child.label}
                        className={`p-0 ${
                          pathname === child.path
                            ? "bg-mint text-black"
                            : "hover:bg-mint hover:text-black focus:bg-mint focus:text-black"
                        }`}
                        onSelect={() => {
                          setOpenDropdown(null);
                          if (child.path) {
                            router.push(child.path);
                          }
                        }}
                      >
                        <span className="block w-full h-full px-4 py-2 cursor-pointer">
                          {child.label}
                        </span>
                      </MenubarItem>
                    ))}
                  </MenubarContent>
                </MenubarMenu>
              </Menubar>
            </div>
            {/* Mobile version */}
            <div className="w-full lg:hidden">
              <details className="w-full group">
                <summary className="h-12 flex items-center justify-between px-4 py-2 list-none cursor-pointer hover:bg-mint hover:text-black focus:bg-mint focus:text-black">
                  {item.label}
                  <IoIosArrowDown className="w-4 h-4 transition-transform group-open:rotate-180" />
                </summary>
                <ul className="w-full mt-2 bg-stalbla600">
                  {item.children.map((child) => (
                    <li key={child.label} className="w-full">
                      <Link
                        tabIndex={0}
                        className="block w-full px-6 py-2 text-left rounded-none cursor-pointer hover:bg-mint hover:text-black focus:bg-mint focus:text-white"
                        href={child.path || "#"}
                        onClick={() => setIsDropdownOpen(false)}
                      >
                        {child.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </details>
            </div>
          </React.Fragment>
        );
      } else {
        // For links without dropdowns
        return (
          <div
            key={item.label}
            className={`h-12 lg:h-16 hover:bg-mint hover:text-black focus-within:bg-mint focus-within:text-black ${activeClass}`}
          >
            <Link
              tabIndex={0}
              className={`flex h-full items-center justify-start px-4 lg:px-5 lg:justify-center rounded-none cursor-pointer hover:text-black focus:text-black focus:bg-mint hover:bg-mint ${activeClass}`}
              href={item.path || "#"}
              onClick={() => setIsDropdownOpen(false)}
            >
              {item.label}
            </Link>
          </div>
        );
      }
    });
  };

  return (
    <div
      className="navbar justify-between bg-udirSteelBlue-700 text-white p-0 z-[10000]"
      role="navigation"
      aria-label="navigasjonsmeny"
    >
      {/* Logo/Brand section */}
      <div className="flex-1">
        <a href="/" className="text-xl normal-case font-light pl-8 pt-1">
          PGS-admin
        </a>
      </div>

      {/* Mobile menu button and dropdown */}
      <div className="flex-none lg:hidden">
        <div className="dropdown dropdown-end">
          <div className="flex">
            {/* Hamburger menu button for mobile */}
            <button
              type="button"
              aria-label="Åpne navigasjonsmeny"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="btn btn-ghost h-16 lg:hidden hover:bg-mint hover:text-black rounded-none"
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  setIsDropdownOpen(!isDropdownOpen);
                } else if (e.key === "Escape") {
                  setIsDropdownOpen(false);
                }
              }}
            >
              {/* Hamburger icon SVG */}
              <svg
                role="img"
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-label="meny ikon"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h8m-8 6h16"
                />
              </svg>
            </button>

            {/* Mobile menu items dropdown */}
            <div>
              {isDropdownOpen && (
                <ul className="menu absolute px-0 py-0 right-0.5 mt-16 shadow bg-stalbla700 scrollbar-role-dropdown">
                  <div className="mt-0 p-0 max-h-[calc(100vh-64px)] overflow-y-auto">
                    {renderMenuItems(accessibleMenuItems)}
                  </div>
                </ul>
              )}{" "}
            </div>

            {/* Mobile user profile button and dropdown */}
            <div className="lg:hidden">
              <div className="relative list-none" ref={dropdownRefSmall}>
                <div className="w-14 text-black">
                  <button
                    className="flex h-16 w-14 items-center justify-between rounded-none text-white hover:text-black text-black bg-eggskall400 hover:bg-mint hover:text-black"
                    onClick={() => {
                      setIsRoleMenuOpenSmall(!isRoleMenuOpenSmall);
                    }}
                    tabIndex={0}
                    aria-label="Åpne brukermeny"
                  >
                    <div className="pt-1 flex w-full gap-8">
                      <div className="flex items-center ml-3 gap-2 w-full text-left">
                        <div className="flex-shrink-0 w-7 h-7">
                          <AiOutlineUser
                            className="w-full h-full text-black"
                            role="img"
                            aria-label="profilIkon"
                          />
                        </div>
                      </div>
                    </div>
                  </button>

                  {/* Mobile user profile dropdown content */}
                  <div
                    className="p-0  rounded-t-none w-[300px]"
                    style={{ top: "100%" }}
                  >
                    {isRoleMenuOpenSmall && (
                      <div>
                        <div
                          className="mt-0 p-0 rounded-none bg-white dropdown-content z-[1] scrollbar-role-dropdown"
                          style={{
                            marginTop: "0px",
                            padding: "0px",
                            maxHeight: "calc(100vh - 64px)",
                            overflowY: "auto",
                          }}
                        >
                          {/* User info and logout button */}
                          <div className="flex flex-row justify-between p-2 text-left sticky top-0 bg-white z-10">
                            <div className="text-base font-normal pt-3 pl-3">
                              BRUKERINFO
                            </div>
                            <div className="content-end">
                              <Button
                                className="text-base font-normal"
                                onClick={handleLogout}
                              >
                                Logg ut
                              </Button>
                            </div>
                          </div>

                          {/* User name display */}
                          <div className="flex flex-row gap-2 p-4">
                            <AiOutlineUser
                              className="w-7 h-7"
                              role="img"
                              aria-label="profilIkon"
                            />
                            <span className="mt-1">
                              {session && session.user.userInfo.name}
                            </span>
                          </div>

                          {/* User roles section */}
                          <div className="text-base pl-5 text-left  top-16 bg-white z-10">
                            DINE ROLLER
                          </div>
                          <div className="bg-udirSteelBlue-700 text-white p-2">
                            <RadioGroup
                              className="w-full space-y-1"
                              value={`${selectedRole?.role} - ${selectedRole?.orgName}`}
                              onValueChange={(newValue) => {
                                const role = getRoleObject(
                                  session,
                                  schools,
                                  userRoles,
                                  pasxSchools,
                                  enhetsservice,
                                  skoleansvarlig
                                ).find((r) => r.role === newValue);
                                if (role) {
                                  handleRoleSelect(role);
                                }
                              }}
                            >
                              {/* Map through user roles and render role selection options */}
                              {session &&
                                getRoleObject(
                                  session,
                                  schools,
                                  userRoles,
                                  pasxSchools,
                                  enhetsservice,
                                  skoleansvarlig
                                ).map((role) => (
                                  <Label
                                    key={role.role}
                                    htmlFor={role.role}
                                    className="btn btn-outline w-full p-3 h-16 flex hover:text-white hover:border-white items-center text-white hover:bg-slate-600 normal-case"
                                    onClick={() => handleRoleSelect(role)}
                                  >
                                    <div
                                      className="flex-1 min-w-0 flex flex-col justify-center items-start gap-1"
                                      title={`${role.displayRoleName} ${role.orgName}`}
                                    >
                                      <div className="font-medium truncate w-full text-left">
                                        {role.displayRoleName}
                                      </div>
                                      <div className="text-sm text-gray-300 max-w-40 text-left">
                                        <span className="block truncate">
                                          {role.orgName}
                                        </span>
                                      </div>
                                    </div>
                                    <RadioGroupItem
                                      className="text-white border-white flex-shrink-0"
                                      value={`${role.role} - ${role.orgName}`}
                                      id={`${role.role} - ${role.orgName}`}
                                      key={`${role.role} - ${role.orgName}`}
                                    />
                                  </Label>
                                ))}
                            </RadioGroup>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop menu */}
      <div className="flex-none hidden lg:flex">
        <div className="menu menu-horizontal py-0 mr-4">
          {/* Desktop menu items */}
          <div className="flex-row flex">
            {renderMenuItems(accessibleMenuItems)}
          </div>

          {/* Desktop user profile button and dropdown */}
          <div className="relative" ref={dropdownRef}>
            <div className="cursor-pointer hover:bg-mint text-black rounded-none bg-udirEggshell-200 hover:bg-mint rounded-none">
              <button
                className="flex h-16 w-72 items-center rounded-none text-black"
                tabIndex={0}
                onClick={() => setIsRoleMenuOpen(!isRoleMenuOpen)}
                aria-label={`Brukermeny for ${session?.user?.userInfo?.name}`}>
                <div className="pt-1 flex items-center justify-between w-full">
                  {/* User info display */}
                  <div className="flex items-center ml-3 gap-2 text-left">
                    <div className="flex-shrink-0 w-7 h-7">
                      <AiOutlineUser
                        className="w-full h-full"
                        aria-hidden="true"
                      />
                    </div>
                    <div className="flex flex-col justify-center">
                      <div>{session && session.user.userInfo.name}</div>
                      <div className="text-xs">
                        {selectedRole?.displayRoleName} {selectedRole?.orgName}
                      </div>
                    </div>
                  </div>
                  {/* Dropdown arrow icon */}
                  <div className="pr-2">
                    <IoIosArrowDown
                      className={`w-5 h-5 transition-transform duration-200 ${
                        isRoleMenuOpen ? "rotate-180" : ""
                      }`}
                      role="img"
                      aria-label={
                        isRoleMenuOpen ? "pil opp ikon" : "pil ned ikon"
                      }
                    />
                  </div>
                </div>
              </button>

              {/* Desktop user profile dropdown content */}
              <div
                className="p-0 absolute text-right w-[285px] right-1 rounded-t-none"
                style={{ top: "100%" }}
              >
                {isRoleMenuOpen && (
                  <div>
                    <div
                      className="mt-0 p-0 rounded-none bg-white dropdown-content z-[1] scrollbar-role-dropdown"
                      style={{
                        marginTop: "0px",
                        padding: "0px",
                        maxHeight: "calc(100vh - 64px)",
                        overflowY: "auto",
                      }}
                    >
                      {/* User info and logout button */}
                      <div className="flex flex-row justify-between p-2 text-left sticky top-0 bg-white z-10">
                        <div className="text-base font-normal pt-3 pl-3">
                          BRUKERINFO
                        </div>
                        <div className="content-end">
                          <Button
                            className="text-base font-normal"
                            onClick={handleLogout}
                          >
                            Logg ut
                          </Button>
                        </div>
                      </div>

                      {/* User name display */}
                      <div className="flex flex-row gap-2 px-4 py-2">
                        <AiOutlineUser
                          className="w-7 h-7"
                          role="img"
                          aria-label="profilIkon"
                        />
                        <span className="mt-1">
                          {session && session.user.userInfo.name}
                        </span>
                      </div>

                      {/* User roles section */}
                      <div className="text-base pl-5 text-left mb-1 top-16 bg-white z-10">
                        DINE ROLLER
                      </div>
                      <div className="bg-udirSteelBlue-700 text-white p-2">
                        <RadioGroup
                          className="w-full space-y-1"
                          value={`${selectedRole?.role} - ${selectedRole?.orgName}`}
                          onValueChange={(newValue) => {
                            const role = getRoleObject(
                              session,
                              schools,
                              userRoles,
                              pasxSchools,
                              enhetsservice,
                              skoleansvarlig
                            ).find((r) => r.role === newValue);
                            if (role) {
                              handleRoleSelect(role);
                            }
                          }}
                        >
                          {/* Map through user roles and render role selection options */}
                          {session &&
                            getRoleObject(
                              session,
                              schools,
                              userRoles,
                              pasxSchools,
                              enhetsservice,
                              skoleansvarlig
                            ).map((role) => (
                              <Label
                                key={role.role}
                                htmlFor={role.role}
                                className="btn btn-outline w-full p-3 h-16 flex hover:text-white hover:border-white items-center text-white hover:bg-udirSteelBlue-600 normal-case"
                                onClick={() => handleRoleSelect(role)}
                              >
                                <div
                                  className="flex-1 min-w-0 flex flex-col justify-center items-start gap-1"
                                  title={`${role.displayRoleName} ${role.orgName}`}
                                >
                                  <div className="font-medium truncate w-full text-left">
                                    {role.displayRoleName}
                                  </div>
                                  <div className="text-sm text-gray-300 max-w-52 text-left">
                                    <span className="block truncate">
                                      {role.orgName}
                                    </span>
                                  </div>
                                </div>
                                <RadioGroupItem
                                  className="text-white border-white flex-shrink-0"
                                  value={`${role.role} - ${role.orgName}`}
                                  id={`${role.role} - ${role.orgName}`}
                                  key={`${role.role} - ${role.orgName}`}
                                />
                              </Label>
                            ))}
                        </RadioGroup>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
export default NavBar;
