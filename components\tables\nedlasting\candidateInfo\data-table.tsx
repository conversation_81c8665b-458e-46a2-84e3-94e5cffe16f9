import React, { useState } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { IExamDocument } from "@/interface/IExamDocument";
import { Loader2 } from "lucide-react";
import useFileHandler from "@/hooks/useFileHandler";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function DataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<IExamDocument, TValue>) {
  const [downloadingFiles, setDownloadingFiles] = useState<Set<string>>(
    new Set()
  );
  const [errors, setErrors] = useState<{ [key: string]: string | null }>({});
  const [sorting, setSorting] = useState<SortingState>([
    { id: "fileName", desc: false } // Initial sorting: anonymisert filnavn stigende
  ]);

  const { downloadFile } = useFileHandler();

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  async function downloadSingleFile(documentCode: string, fileName: string) {
    setDownloadingFiles((prev) => new Set(prev).add(documentCode));
    setErrors((prev) => ({ ...prev, [documentCode]: null }));
    try {
      await downloadFile(documentCode, fileName);
    } catch (err) {
      setErrors((prev) => ({
        ...prev,
        [documentCode]: "Nedlasting feilet. Prøv igjen senere.",
      }));
    } finally {
      setDownloadingFiles((prev) => {
        const newSet = new Set(prev);
        newSet.delete(documentCode);
        return newSet;
      });
    }
  }

  return (
    <div className="rounded-md border overflow-x-auto">
      <Table aria-label="Tabell med kandidatens filer" aria-describedby="files-table-description">
        <caption id="files-table-description" className="sr-only">
          Oversikt over alle filer levert av kandidaten. Klikk på anonymisert filnavn for å laste ned enkeltstående fil.
        </caption>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id} scope="col">
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {cell.column.id === "fileName" ? (
                      <div className="flex items-center space-x-2">
                        <div
                          className="cursor-pointer font-medium hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                          onClick={() =>
                            downloadSingleFile(
                              row.original.documentCode,
                              row.original.fileName
                            )
                          }
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                              e.preventDefault();
                              downloadSingleFile(
                                row.original.documentCode,
                                row.original.fileName
                              );
                            }
                          }}
                          tabIndex={0}
                          role="button"
                          aria-label={`Last ned fil ${row.original.fileName}`}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </div>
                        {downloadingFiles.has(row.original.documentCode) && (
                          <Loader2
                            className="animate-spin h-5 w-5"
                            role="img"
                            aria-label="laster ned ikon"
                          />
                        )}
                        {errors[row.original.documentCode] && (
                          <span className="text-red-500 text-sm">
                            {errors[row.original.documentCode]}
                          </span>
                        )}
                      </div>
                    ) : (
                      flexRender(cell.column.columnDef.cell, cell.getContext())
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                Ingen resultater
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
