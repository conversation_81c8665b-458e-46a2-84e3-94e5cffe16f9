"use client";

import React, {
  createContext,
  useEffect,
  useState,
  useContext,
  ReactNode,
  useCallback,
} from "react";
import dayjs from "dayjs";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { TestPartsEnum } from "@/enums/TestPart";
import { IUpdateFilePayload } from "@/interface/IUpdateFilePayload";
import { toast } from "@/components/ui/use-toast";
import { BlobClient } from "@azure/storage-blob";
import { ExamStatusEnum } from "@/enums/ExamStatusEnum";
import { logException } from "@/lib/client/appInsightsClient";

// Constants
const API_CONFIG = {
  baseUrl: window.location.origin,
  endpoints: {
    uploadBlobSas: "/api/getuploadblobsastoken",
    blobSas: "/api/getblobsastoken",
    deleteFile: "/api/deleteFile",
    downloadSingleSubmission: "/api/downloadSingleSubmission",
    updateFileStatus: "/api/updateFileStatus",
  },
  container: "pgsx-documents",
} as const;

// Types
interface FileContextType {
  uploadedFiles: IUploadedFile[];
  addFile: (newFile: IUploadedFile) => Promise<void>;
  deleteFile: (fileGuid: string) => Promise<void>;
  clearFiles: () => void;
  setTestPart: (fileGuid: string, testPart: TestPartsEnum) => Promise<void>;
  error: Error | null;
  clearError: () => void;
  updateFile: (fileGuid: string, updatedFields: Partial<IUploadedFile>) => void;
  deliverFile: (
    file: IUploadedFile,
    requireCheckedBeforeDelivery: boolean
  ) => Promise<void>;
  clearSubmittedFiles: () => void;
  downloadFile: (documentCode: string, filename: string) => Promise<void>;
  isUploading: boolean;
  isLoading: boolean;
  candidateNumber: string;
  setCandidateNumber: React.Dispatch<React.SetStateAction<string>>;
  setUploadedFiles: React.Dispatch<React.SetStateAction<IUploadedFile[]>>;
  setStatusChecked: (status: boolean, fileGuid: string) => void;
}

interface ErrorContext {
  component: string;
  action: string;
  [key: string]: unknown;
}

// Error handling utility
class ApiError extends Error {
  constructor(message: string, public status?: number, public data?: any) {
    super(message);
    this.name = "ApiError";
  }
}

const FileContext = createContext<FileContextType | undefined>(undefined);

// API Service
const apiService = {
  async fetchWithErrorHandling<T>(
    url: string,
    options?: RequestInit
  ): Promise<T> {
    const response = await fetch(url, options);
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.error || `HTTP error! status: ${response.status}`,
        response.status,
        errorData
      );
    }
    return response.json();
  },

  async downloadBlob(url: string): Promise<Blob> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new ApiError(`Download failed! status: ${response.status}`);
    }
    return response.blob();
  },
};

function useFileHandler(): FileContextType {
  const [uploadedFiles, setUploadedFiles] = useState<IUploadedFile[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [candidateNumber, setCandidateNumber] = useState("");

  useEffect(() => {
    try {
      console.log(uploadedFiles);
      setIsUploading(uploadedFiles.some((f) => !f.UploadFinished));
    } catch (error) {
      handleError(error as Error, {
        component: "useFileHandler",
        action: "checkUploadStatus",
      });
    }
  }, [uploadedFiles]);

  const handleError = useCallback(
    async (error: Error, context: ErrorContext) => {
      console.error("Error:", error);
      setError(error);
      toast({
        variant: "destructive",
        title: "En feil oppstod",
        description: error.message || "Ukjent feil",
      });
      await logException(error, context);
    },
    []
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const updateFile = useCallback(
    (fileGuid: string, updatedFields: Partial<IUploadedFile>) => {
      setUploadedFiles((prevFiles) =>
        prevFiles.map((f) =>
          f.FileGuid === fileGuid ? { ...f, ...updatedFields } : f
        )
      );
    },
    []
  );

  const uploadFileToBlob = async (
    uploadedFile: IUploadedFile
  ): Promise<void> => {
    const sasTokenResponse = await apiService.fetchWithErrorHandling<{
      sastoken: string;
    }>(`${API_CONFIG.endpoints.uploadBlobSas}?guid=${uploadedFile.FileGuid}`);
    const blobClient = new BlobClient(sasTokenResponse.sastoken);
    const blockBlobClient = blobClient.getBlockBlobClient();

    if (uploadedFile.File) {
      await blockBlobClient.uploadData(uploadedFile.File, {
        metadata: {
          filesize: uploadedFile.Size.toString(),
          filename: encodeURIComponent(uploadedFile.Name),
        },
      });
    }
  };

  const addFile = async (newFile: IUploadedFile) => {
    try {
      setUploadedFiles((prevFiles) => [
        ...prevFiles,
        {
          ...newFile,
          Name: newFile.Name.toUpperCase(),
          IsLoading: true,
          UploadFinished: false,
        },
      ]);

      if (!newFile.IsRejected) {
        await uploadFileToBlob(newFile);

        updateFile(newFile.FileGuid, {
          UploadFinished: true,
          IsLoading: false,
        });
      } else {
        updateFile(newFile.FileGuid, {
          IsRejected: true,
          UploadFinished: true,
          IsLoading: false,
        });
      }
    } catch (error) {
      updateFile(newFile.FileGuid, {
        IsRejected: true,
        UploadFinished: true,
        IsLoading: false,
        Errors: ["Opplasting feilet, prøv igjen!"],
      });

      await handleError(error as Error, {
        component: "UseFileHandler",
        action: "addFile",
        fileName: newFile.Name,
        candidate: newFile.Candididate,
        groupCode: newFile.Groupcode,
        subjectCode: newFile.SubjectCode,
      });

      try {
        await apiService.fetchWithErrorHandling(
          `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.deleteFile}/${newFile.FileGuid}`,
          { method: "DELETE" }
        );
      } catch (deleteError) {
        console.error(
          "Feil ved sletting av mislykket opplasting:",
          deleteError
        );
      }
    }
  };

  const downloadFile = async (documentCode: string, filename: string) => {
    const element = document.createElement("a");
    try {
      setIsLoading(true);
      updateFile(documentCode, { IsLoading: true });

      const blob = await apiService.downloadBlob(
        `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.downloadSingleSubmission}?documentCode=${documentCode}`
      );

      const url = window.URL.createObjectURL(blob);
      element.style.display = "none";
      element.href = url;
      element.download = filename;
      document.body.appendChild(element);
      element.click();
    } catch (error) {
      await handleError(error as Error, {
        component: "UseFileHandler",
        action: "downloadFile",
        documentCode,
        filename,
      });
    } finally {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
      window.URL.revokeObjectURL(element.href);
      setIsLoading(false);
      updateFile(documentCode, { IsLoading: false });
    }
  };

  const deleteFile = async (fileGuid: string): Promise<void> => {
    try {
      setIsLoading(true);
      updateFile(fileGuid, { IsDeleting: true });

      const fileToDelete = uploadedFiles.find((f) => f.FileGuid === fileGuid);
      if (!fileToDelete) {
        throw new Error("File not found");
      }

      if (!fileToDelete.IsRejected) {
        await apiService.fetchWithErrorHandling(
          `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.deleteFile}/${fileGuid}`,
          { method: "DELETE" }
        );
      }

      setUploadedFiles((prevFiles) =>
        prevFiles.filter((f) => f.FileGuid !== fileGuid)
      );
    } catch (error) {
      await handleError(error as Error, {
        component: "UseFileHandler",
        action: "deleteFile",
        fileGuid,
      });
      updateFile(fileGuid, { IsDeleting: false });
    } finally {
      setIsLoading(false);
    }
  };

  const clearFiles = useCallback(() => {
    setUploadedFiles([]);
  }, []);

  const clearSubmittedFiles = useCallback(() => {
    setUploadedFiles((prevData) =>
      prevData.filter((item) => item.Delivered === false)
    );
  }, []);

  const setStatusChecked = useCallback(
    (status: boolean, fileGuid: string) => {
      updateFile(fileGuid, { Checked: status });
    },
    [updateFile]
  );

  const deliverFile = async (
    file: IUploadedFile,
    requireCheckedBeforeDelivery: boolean
  ) => {
    if (requireCheckedBeforeDelivery && !file.Checked) {
      toast({
        variant: "destructive",
        title: "Feil ved levering av fil",
        description: "Du må markere at filen er sjekket for å få levert den.",
      });
      return;
    }

    try {
      setIsLoading(true);
      updateFile(file.FileGuid, { IsSubmitting: true });

      const body: IUpdateFilePayload = {
        testPartId: file.TestPartId,
        status: ExamStatusEnum.Submitted,
        candidateNumber: file.Candididate,
        groupCode: file.Groupcode,
        subjectCode: file.SubjectCode || "",
        fileExtension: file.Name.split(".").pop() || "",
        docCode: file.FileGuid,
      };

      await apiService.fetchWithErrorHandling(
        `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.updateFileStatus}`,
        {
          method: "POST",
          body: JSON.stringify(body),
        }
      );

      updateFile(file.FileGuid, {
        Delivered: true,
        SubmittedDate: dayjs(),
      });
    } catch (error) {
      await handleError(error as Error, {
        component: "UseFileHandler",
        action: "deliverFile",
        fileGuid: file.FileGuid,
        fileName: file.Name,
        candidate: file.Candididate,
        groupCode: file.Groupcode,
        subjectCode: file.SubjectCode,
      });
    } finally {
      setIsLoading(false);
      updateFile(file.FileGuid, { IsSubmitting: false });
    }
  };

  const setTestPart = async (fileGuid: string, testPart: TestPartsEnum) => {
    const file = uploadedFiles.find((f) => f.FileGuid === fileGuid);
    if (!file || file.TestPartId === 1) return;

    try {
      setIsLoading(true);
      updateFile(fileGuid, { IsLoading: true });

      const oldTestPartId = file.TestPartId;
      const body: IUpdateFilePayload = {
        testPartId: testPart,
        status: ExamStatusEnum.Uploaded,
        candidateNumber: file.Candididate,
        groupCode: file.Groupcode,
        subjectCode: file.SubjectCode || "",
        fileExtension: file.Name.split(".").pop() || "",
        previousTestPartId: oldTestPartId,
        docCode: fileGuid,
      };

      await apiService.fetchWithErrorHandling(
        `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.updateFileStatus}`,
        {
          method: "POST",
          body: JSON.stringify(body),
        }
      );

      updateFile(fileGuid, { TestPartId: testPart });
    } catch (error) {
      await handleError(error as Error, {
        component: "UseFileHandler",
        action: "setTestPart",
        fileGuid,
        testPart,
        candidate: file.Candididate,
        groupCode: file.Groupcode,
        subjectCode: file.SubjectCode,
      });
    } finally {
      setIsLoading(false);
      updateFile(fileGuid, { IsLoading: false });
    }
  };

  return {
    downloadFile,
    uploadedFiles,
    addFile,
    deleteFile,
    clearFiles,
    setTestPart,
    error,
    clearError,
    updateFile,
    deliverFile,
    clearSubmittedFiles,
    isUploading,
    isLoading,
    candidateNumber,
    setCandidateNumber,
    setUploadedFiles,
    setStatusChecked,
  };
}

export const FileProvider = ({ children }: { children: ReactNode }) => {
  const fileHandler = useFileHandler();
  return (
    <FileContext.Provider value={fileHandler}>{children}</FileContext.Provider>
  );
};

const useFileContext = (): FileContextType => {
  const context = useContext(FileContext);
  if (context === undefined) {
    throw new Error("useFileContext må brukes innenfor en FileProvider");
  }
  return context;
};

export default useFileContext;
