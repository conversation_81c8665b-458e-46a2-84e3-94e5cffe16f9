import { BlobClient } from "@azure/storage-blob";
import { logException } from "@/lib/client/appInsightsClient";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { IUpdateFilePayload } from "@/interface/IUpdateFilePayload";
import { TestPartsEnum } from "@/enums/TestPart";
import { ExamStatusEnum } from "@/enums/ExamStatusEnum";
import { IActivityLogV2 } from "@/interface/IActivityLogV2";

// --- API Call Utilities ---

/** Generic fetch wrapper with error handling */
async function fetchApi(
  url: string,
  options: RequestInit = {},
  errorMessagePrefix: string
) {
  const response = await fetch(url, options);
  if (!response.ok) {
    let errorDetails = "Ukjent feil";
    try {
      const errorJson = await response.json();
      errorDetails = errorJson.error || JSON.stringify(errorJson);
    } catch (e) {
      // If response is not JSON or empty
      errorDetails = response.statusText;
    }
    throw new Error(`${errorMessagePrefix}: ${errorDetails}`);
  }
  // For DELETE or PUT requests that might not return JSON
  if (
    response.status === 204 ||
    response.headers.get("content-length") === "0"
  ) {
    return null; // Or return response object if needed
  }
  return response.json();
}

export const apiUtils = {
  getUploadToken: async (guid: string): Promise<{ sastoken: string }> => {
    return fetchApi(
      `/api/getuploadblobsastoken?guid=${guid}`,
      {},
      "Kunne ikke hente upload token"
    );
  },

  getBlobToken: async (
    blobname: string,
    container: string = "pgsx-documents"
  ): Promise<{ sastoken: string }> => {
    return fetchApi(
      `/api/getblobsastoken?blobname=${blobname}&container=${container}`,
      {},
      "Kunne ikke hente blob token"
    );
  },

  validateUpload: async (
    fileGuid: string,
    testPartId: TestPartsEnum,
    endpoint: string
  ): Promise<any> => {
    // Using fetch directly as we need the Response object or handle non-JSON success
    const response = await fetch(
      endpoint
        .replace("{fileGuid}", fileGuid)
        .replace("{testPartId}", testPartId.toString()),
      { method: "PUT" }
    );
    if (!response.ok) {
      let errorDetails = "Ukjent feil";
      try {
        const errorJson = await response.json();
        errorDetails = errorJson.error || JSON.stringify(errorJson);
      } catch (e) {
        errorDetails = response.statusText;
      }
      throw new Error(`Validering feilet: ${errorDetails}`);
    }

    const responseData = await response.json();

    return responseData;
  },

  deleteFileApi: async (fileGuid: string, endpoint: string): Promise<null> => {
    return fetchApi(
      endpoint.replace("{fileGuid}", fileGuid),
      { method: "DELETE" },
      "Sletting feilet (API)"
    );
  },

  updateStatusApi: async (
    payload: IUpdateFilePayload,
    endpoint: string
  ): Promise<Response | null> => {
    // Using fetch directly as we need the Response object or handle non-JSON success
    const response = await fetch(endpoint, {
      method: "POST",
      body: JSON.stringify(payload),
      headers: { "Content-Type": "application/json" },
    });
    if (!response.ok) {
      let errorDetails = "Ukjent feil";
      try {
        const errorJson = await response.json();
        errorDetails = errorJson.error || JSON.stringify(errorJson);
      } catch (e) {
        errorDetails = response.statusText;
      }
      throw new Error(`Status oppdatering feilet: ${errorDetails}`);
    }
    // Check for empty response before trying to parse JSON
    if (
      response.status === 204 ||
      response.headers.get("content-length") === "0"
    ) {
      return null;
    }
    try {
      await response.json(); // Consume potential JSON body if present, even if not used
    } catch (e) {
      // Ignore JSON parsing errors if content type isn't application/json or if body is empty
      if (!response.headers.get("content-type")?.includes("application/json")) {
        console.warn(
          "Received non-JSON response for updateStatusApi, but request was successful."
        );
      } else {
        throw new Error("Status oppdatering returnerte ugyldig JSON");
      }
    }
    return response; // Return the response object
  },

  downloadFileApi: async (
    documentCode: string,
    endpoint: string
  ): Promise<Blob> => {
    const response = await fetch(
      endpoint.replace("{documentCode}", documentCode)
    );
    if (!response.ok) {
      let errorDetails = "Ukjent feil";
      try {
        const errorJson = await response.json();
        errorDetails = errorJson.error || JSON.stringify(errorJson);
      } catch (e) {
        errorDetails = response.statusText;
      }
      throw new Error(`Nedlasting feilet: ${errorDetails}`);
    }
    return response.blob();
  },
};

// --- Blob Storage Utilities ---

export const blobUtils = {
  upload: async (file: IUploadedFile): Promise<void> => {
    if (!file.File) throw new Error("Ingen fil tilgjengelig for opplasting");

    const { sastoken } = await apiUtils.getUploadToken(file.FileGuid);
    const blobClient = new BlobClient(sastoken);
    const blockBlobClient = blobClient.getBlockBlobClient();

    const buffer = await file.File.arrayBuffer();
    await blockBlobClient.upload(buffer, buffer.byteLength, {
      metadata: {
        filesize: file.Size.toString(),
        filename: encodeURIComponent(file.Name),
      },
    });
  },

  download: (blob: Blob, filename: string): void => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link); // Required for Firefox
    link.click();
    document.body.removeChild(link); // Clean up
    URL.revokeObjectURL(url);
  },
};

// --- General Utilities ---

export const generalUtils = {
  handleError: async (
    error: unknown,
    context: {
      component: string; // e.g., 'UseFileHandler' or 'UseFileHandlerForMonitor'
      action: string;
      fileInfo?: Partial<IUploadedFile & { candidate?: string }>; // Allow adding candidate info
      [key: string]: any; // Allow additional context
    }
  ): Promise<Error> => {
    const err =
      error instanceof Error
        ? error
        : new Error(String(error) || "Ukjent feil");
    console.error(
      `[${context.component}] Error during ${context.action}:`,
      err,
      context
    );
    await logException(err, context);
    return err;
  },

  createUpdatePayload: (
    file: IUploadedFile,
    status: ExamStatusEnum,
    previousTestPartId?: number
  ): IUpdateFilePayload => ({
    testPartId: file.TestPartId,
    status,
    candidateNumber: file.Candididate, // Ensure this property exists or is handled if optional
    groupCode: file.Groupcode,
    subjectCode: file.SubjectCode || "",
    fileExtension: file.Name.split(".").pop() || "",
    previousTestPartId,
    docCode: file.FileGuid,
  }),

  updateFileState: <T extends IUploadedFile>(
    setUploadedFiles: React.Dispatch<React.SetStateAction<T[]>>,
    fileGuid: string,
    updatedFields: Partial<T>
  ) => {
    setUploadedFiles((prev) =>
      prev.map((f) =>
        f.FileGuid === fileGuid ? { ...f, ...updatedFields } : f
      )
    );
  },
};
