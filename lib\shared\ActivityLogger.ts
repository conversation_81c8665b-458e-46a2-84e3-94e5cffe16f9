import { logActivity } from "@/lib/client/logActivity";
import { getTestPart } from "@/lib/shared/getTestPart";
import { getTestPartForDescription } from "@/lib/shared/getTestPartForDescription";
import { OperationEnum } from "@/enums/OperationEnum";
import { TestPartsEnum } from "@/enums/TestPart";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { IUploadedFile } from "@/interface/IUploadedFile";

export interface CandidateInfo {
  userId: string;
  candidateName?: string;
  candidateNumber: string;
  candidateRegistrationId?: string;
}

export interface ActivityContext {
  roleName?: string;
  fileName?: string;
  testPartId?: TestPartsEnum;
  parameters?: Record<string, any>;
}

export class ActivityLogger {
  /**
   * Parse candidate name into first and last name
   */
  private static parseCandidateName(candidateName?: string): {
    firstName: string;
    lastName: string;
  } {
    if (!candidateName) {
      return { firstName: "", lastName: "" };
    }

    // Handle format "LastName, FirstName" or "FirstName LastName"
    if (candidateName.includes(",")) {
      const [lastName, firstName] = candidateName
        .split(",")
        .map((name) => name.trim());
      return { firstName: firstName || "", lastName: lastName || "" };
    } else {
      const nameParts = candidateName.split(" ");
      return {
        firstName: nameParts[0] || "",
        lastName: nameParts.slice(1).join(" ") || "",
      };
    }
  }

  /**
   * Generic log activity method
   */
  static async logActivity(
    operation: OperationEnum,
    candidateInfo: CandidateInfo,
    context: ActivityContext = {}
  ): Promise<{ success: boolean; error?: any }> {
    try {
      const { firstName, lastName } = this.parseCandidateName(
        candidateInfo.candidateName
      );

      const result = await logActivity(
        candidateInfo.userId,
        context.fileName || "",
        context.testPartId ? getTestPart(context.testPartId) : "",
        context.roleName || "",
        operation,
        firstName,
        lastName,
        candidateInfo.candidateRegistrationId || candidateInfo.userId,
        candidateInfo.candidateNumber,
        context.parameters
      );

      return { success: true };
    } catch (error) {
      console.error("Activity logging failed:", error);
      return { success: false, error };
    }
  }

  /**
   * Log test part change activity
   */
  static async logTestPartChange(
    file: IUploadedFile,
    newTestPart: TestPartsEnum,
    candidateInfo: CandidateInfo,
    roleName: string,
    isGroupUpload: boolean = false
  ): Promise<void> {
    const operation = isGroupUpload
      ? OperationEnum.StatusChangedTestPartIdGoupUpload
      : OperationEnum.StatusChangedTestPartId;

    await this.logActivity(operation, candidateInfo, {
      roleName,
      fileName: file.Name,
      testPartId: newTestPart,
      parameters: { partNumber: getTestPartForDescription(newTestPart) },
    });
  }

  /**
   * Log file download activity
   */
  static async logFileDownload(
    fileName: string,
    testPartId: TestPartsEnum,
    candidateInfo: CandidateInfo,
    roleName: string
  ): Promise<void> {
    await this.logActivity(OperationEnum.StatusFileDownloaded, candidateInfo, {
      roleName,
      fileName,
      testPartId,
    });
  }

  /**
   * Log file checked status change (monitor specific)
   */
  static async logFileChecked(
    fileName: string,
    testPartId: TestPartsEnum,
    candidateInfo: CandidateInfo,
    roleName: string
  ): Promise<void> {
    await this.logActivity(OperationEnum.StatusFileChecked, candidateInfo, {
      roleName,
      fileName,
      testPartId,
    });
  }

  /**
   * Log monitor file delivery activity with appropriate operation based on test part
   */
  static async logMonitorFileDelivery(
    file: IUploadedFile,
    candidateInfo: CandidateInfo,
    roleName: string
  ): Promise<void> {
    let operation: OperationEnum;

    switch (file.TestPartId) {
      case TestPartsEnum.EksamenDel1:
        operation = OperationEnum.StatusFileDeliveredPart1;
        break;
      case TestPartsEnum.EksamenDel2:
        operation = OperationEnum.StatusFileDeliveredPart2;
        break;
      default:
        operation = OperationEnum.StatusFileDelivered;
    }

    await this.logActivity(operation, candidateInfo, {
      roleName,
      fileName: file.Name,
      testPartId: file.TestPartId,
    });
  }

  /**
   * Log monitor file delivery for both parts (special case)
   */
  static async logMonitorFileDeliveryBothParts(
    file: IUploadedFile,
    candidateInfo: CandidateInfo,
    roleName: string
  ): Promise<void> {
    // Log for both parts
    const results = await Promise.allSettled([
      this.logActivity(OperationEnum.StatusFileDeliveredPart1, candidateInfo, {
        roleName,
        fileName: file.Name,
        testPartId: TestPartsEnum.EksamenDel1,
      }),
      this.logActivity(OperationEnum.StatusFileDeliveredPart2, candidateInfo, {
        roleName,
        fileName: file.Name,
        testPartId: TestPartsEnum.EksamenDel2,
      }),
    ]);

    // Check for failures
    const failures = results.filter((r) => r.status === "rejected");
    if (failures.length > 0) {
      console.warn(
        `${failures.length} audit log(s) failed for dual part delivery`
      );
    }
  }

  /**
   * Log exam submission actions - Open for new delivery
   */
  static async logOpenForNewDelivery(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    isOnePartExam: boolean
  ): Promise<void> {
    const testPartId = isOnePartExam
      ? TestPartsEnum.Eksamen
      : TestPartsEnum.EksamenDel2;
    const operation = isOnePartExam
      ? OperationEnum.StatusOpenedForNewDeliveryEksamen
      : OperationEnum.StatusOpenedForNewDelivery;

    await this.logActivity(
      operation,
      {
        userId: candidateInfo.userId,
        candidateName: candidateInfo.candidateName,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.candidateRegistrationId,
      },
      {
        roleName,
        testPartId,
        parameters: { partNumber: getTestPartForDescription(testPartId) },
      }
    );
  }

  /**
   * Log exam submission actions - Set delivered
   */
  static async logSetDelivered(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    isOnePartExam: boolean
  ): Promise<void> {
    const operation = isOnePartExam
      ? OperationEnum.StatusDeliveredExam
      : OperationEnum.StatusDeliveredPart2;
    const testPartId = isOnePartExam
      ? TestPartsEnum.Eksamen
      : TestPartsEnum.EksamenDel2;

    await this.logActivity(
      operation,
      {
        userId: candidateInfo.userId,
        candidateName: candidateInfo.candidateName,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.candidateRegistrationId,
      },
      {
        roleName,
        testPartId,
      }
    );
  }

  /**
   * Log deliver on paper activity
   */
  static async logDeliverOnPaper(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    testPartId: TestPartsEnum
  ): Promise<{ success: boolean; error?: any }> {
    try {
      if (testPartId === TestPartsEnum.EksamenDel1ogDel2) {
        // Log for both parts
        const results = await Promise.allSettled([
          this.logActivity(
            OperationEnum.StatusDeliveredOnPaperPart1,
            {
              userId: candidateInfo.userId,
              candidateName: candidateInfo.candidateName,
              candidateNumber: candidateInfo.candidateNumber,
              candidateRegistrationId: candidateInfo.candidateRegistrationId,
            },
            {
              roleName,
              testPartId: TestPartsEnum.EksamenDel1,
            }
          ),
          this.logActivity(
            OperationEnum.StatusDeliveredOnPaperPart2,
            {
              userId: candidateInfo.userId,
              candidateName: candidateInfo.candidateName,
              candidateNumber: candidateInfo.candidateNumber,
              candidateRegistrationId: candidateInfo.candidateRegistrationId,
            },
            {
              roleName,
              testPartId: TestPartsEnum.EksamenDel2,
            }
          ),
        ]);

        const failures = results.filter((r) => r.status === "rejected");
        if (failures.length > 0) {
          console.warn(
            `${failures.length} audit log(s) failed for deliver on paper action`
          );
        }
      } else {
        let operation: OperationEnum;
        switch (testPartId) {
          case TestPartsEnum.EksamenDel1:
            operation = OperationEnum.StatusDeliveredOnPaperPart1;
            break;
          case TestPartsEnum.EksamenDel2:
            operation = OperationEnum.StatusDeliveredOnPaperPart2;
            break;
          default:
            operation = OperationEnum.StatusSendInPost;
        }

        const result = await this.logActivity(
          operation,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId,
          }
        );

        if (!result.success) {
          console.warn(
            "Audit log failed for deliver on paper action:",
            result.error
          );
        }
      }

      return { success: true };
    } catch (error) {
      console.error(
        "Critical error in audit logging for deliver on paper:",
        error
      );
      return { success: false, error };
    }
  }

  /**
   * Log undo deliver on paper activity
   */
  static async logUndoDeliverOnPaper(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    testPartId: TestPartsEnum
  ): Promise<{ success: boolean; error?: any }> {
    try {
      if (testPartId === TestPartsEnum.EksamenDel1ogDel2) {
        // Log for both parts
        const results = await Promise.allSettled([
          this.logActivity(
            OperationEnum.StatusUndoDeliveredOnPaperPart1,
            {
              userId: candidateInfo.userId,
              candidateName: candidateInfo.candidateName,
              candidateNumber: candidateInfo.candidateNumber,
              candidateRegistrationId: candidateInfo.candidateRegistrationId,
            },
            {
              roleName,
              testPartId,
            }
          ),
          this.logActivity(
            OperationEnum.StatusUndoDeliveredOnPaperPart2,
            {
              userId: candidateInfo.userId,
              candidateName: candidateInfo.candidateName,
              candidateNumber: candidateInfo.candidateNumber,
              candidateRegistrationId: candidateInfo.candidateRegistrationId,
            },
            {
              roleName,
              testPartId,
            }
          ),
        ]);

        const failures = results.filter((r) => r.status === "rejected");
        if (failures.length > 0) {
          console.warn(
            `${failures.length} audit log(s) failed for undo deliver on paper action`
          );
        }
      } else {
        let operation: OperationEnum;
        switch (testPartId) {
          case TestPartsEnum.Eksamen:
            operation = OperationEnum.StatusUndoDeliveredOnPaper;
            break;
          case TestPartsEnum.EksamenDel1:
            operation = OperationEnum.StatusUndoDeliveredOnPaperPart1;
            break;
          case TestPartsEnum.EksamenDel2:
            operation = OperationEnum.StatusUndoDeliveredOnPaperPart2;
            break;
          default:
            operation = OperationEnum.StatusUndoDeliveredOnPaper;
        }

        const result = await this.logActivity(
          operation,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId,
          }
        );

        if (!result.success) {
          console.warn(
            "Audit log failed for undo deliver on paper action:",
            result.error
          );
        }
      }

      return { success: true };
    } catch (error) {
      console.error(
        "Critical error in audit logging for undo deliver on paper:",
        error
      );
      return { success: false, error };
    }
  }
}
