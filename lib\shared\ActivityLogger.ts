import { logActivity } from "@/lib/client/logActivity";
import { getTestPart } from "@/lib/shared/getTestPart";
import { getTestPartForDescription } from "@/lib/shared/getTestPartForDescription";
import { OperationEnum } from "@/enums/OperationEnum";
import { TestPartsEnum } from "@/enums/TestPart";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { IStatusInfo } from "@/interface/IStatusInfo";

export interface CandidateInfo {
  userId: string;
  candidateName?: string;
  candidateNumber: string;
  candidateRegistrationId?: string;
}

export interface ActivityContext {
  roleName?: string;
  fileName?: string;
  testPartId?: TestPartsEnum;
  parameters?: Record<string, any>;
}

export class ActivityLogger {
  /**
   * Parse candidate name into first and last name
   */
  private static parseCandidateName(candidateName?: string): {
    firstName: string;
    lastName: string;
  } {
    if (!candidateName) {
      return { firstName: "", lastName: "" };
    }

    // Handle format "LastName, FirstName" or "FirstName LastName"
    if (candidateName.includes(",")) {
      const [lastName, firstName] = candidateName
        .split(",")
        .map((name) => name.trim());
      return { firstName: firstName || "", lastName: lastName || "" };
    } else {
      const nameParts = candidateName.split(" ");
      return {
        firstName: nameParts[0] || "",
        lastName: nameParts.slice(1).join(" ") || "",
      };
    }
  }

  /**
   * Generic log activity method
   */
  static async logActivity(
    operation: OperationEnum,
    candidateInfo: CandidateInfo,
    context: ActivityContext = {}
  ): Promise<{ success: boolean; error?: any }> {
    try {
      const { firstName, lastName } = this.parseCandidateName(
        candidateInfo.candidateName
      );

      const result = await logActivity(
        candidateInfo.userId,
        context.fileName || "",
        context.testPartId ? getTestPart(context.testPartId) : "",
        context.roleName || "",
        operation,
        firstName,
        lastName,
        candidateInfo.candidateRegistrationId || candidateInfo.userId,
        candidateInfo.candidateNumber,
        context.parameters
      );

      return { success: true };
    } catch (error) {
      console.error("Activity logging failed:", error);
      return { success: false, error };
    }
  }

  static async logDeliveryActivity(uploadedFiles: IUploadedFile[]) {
    console.log("Logging delivery activity for files:", uploadedFiles);

    // Group files by candidate to process each candidate only once
    const candidateFileMap = new Map<string, IUploadedFile[]>();
    
    for (const file of uploadedFiles) {
      const candidateKey = file.candidateRegistrationId || file.Candididate || "";
      if (!candidateFileMap.has(candidateKey)) {
        candidateFileMap.set(candidateKey, []);
      }
      candidateFileMap.get(candidateKey)!.push(file);
    }

    // Process each candidate once
    for (const [candidateKey, candidateFiles] of candidateFileMap) {
      // Use the first file to get candidate info (all files from same candidate should have same info)
      const firstFile = candidateFiles[0];
      const candidateInfo = {
        userId: firstFile.candidateRegistrationId || "",
        candidateName: firstFile.CandidateName,
        candidateNumber: firstFile.Candididate || "",
        candidateRegistrationId: firstFile.candidateRegistrationId,
      };

      let statusInfo: IStatusInfo | undefined = undefined;

      try {
        const statusResponse = await fetch("/api/getStatusInfo", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            userId: candidateInfo.userId,
          }),
        });

        statusInfo = statusResponse.ok
          ? await statusResponse.json()
          : console.error("Failed to get status info from PGSA");
      } catch (error) {
        console.error("Error fetching status info:", error);
      }

      // Log activities if statusInfo is null(Happens if candidate is not logged in) OR if status is not LastetOpp
      if (
        !statusInfo ||
        (statusInfo && statusInfo.Status !== CandidateStatusEnum.LastetOpp)
      ) {
        // Determine test parts from all files for this candidate
        const testParts = new Set(candidateFiles.map(file => file.TestPartId));
        
        // Check if this is a one-part exam
        const onePartExam = testParts.has(TestPartsEnum.Eksamen);

        if (onePartExam) {
          await ActivityLogger.logActivity(
            OperationEnum.StatusLevertDigitalt,
            candidateInfo,
            {
              roleName: "PGS",
              testPartId: TestPartsEnum.EksamenDel1,
            }
          );

          await ActivityLogger.logActivity(
            OperationEnum.StatusDeliveredDigitalAccessBlocked,
            candidateInfo,
            {
              roleName: "PGS",
              testPartId: TestPartsEnum.EksamenDel1,
            }
          );
        } else {
          // Handle multi-part exams
          if (testParts.has(TestPartsEnum.EksamenDel1ogDel2)) {
            await ActivityLogger.logActivity(
              OperationEnum.StatusLevertDigitaltDel1,
              candidateInfo,
              {
                roleName: "PGS",
                testPartId: TestPartsEnum.EksamenDel1,
              }
            );

            await ActivityLogger.logActivity(
              OperationEnum.StatusLevertDigitaltDel2,
              candidateInfo,
              {
                roleName: "PGS",
                testPartId: TestPartsEnum.EksamenDel2,
              }
            );

            await ActivityLogger.logActivity(
              OperationEnum.StatusDeliveredDigitalAccessBlockedPart2,
              candidateInfo,
              {
                roleName: "PGS",
                testPartId: TestPartsEnum.EksamenDel2,
              }
            );
          } else {
            // Handle individual parts
            if (testParts.has(TestPartsEnum.EksamenDel1)) {
              await ActivityLogger.logActivity(
                OperationEnum.StatusLevertDigitaltDel1,
                candidateInfo,
                {
                  roleName: "PGS",
                  testPartId: TestPartsEnum.EksamenDel1,
                }
              );
            }
            
            if (testParts.has(TestPartsEnum.EksamenDel2)) {
              await ActivityLogger.logActivity(
                OperationEnum.StatusLevertDigitaltDel2,
                candidateInfo,
                {
                  roleName: "PGS",
                  testPartId: TestPartsEnum.EksamenDel2,
                }
              );

              await ActivityLogger.logActivity(
                OperationEnum.StatusDeliveredDigitalAccessBlockedPart2,
                candidateInfo,
                {
                  roleName: "PGS",
                  testPartId: TestPartsEnum.EksamenDel2,
                }
              );
            }
          }
        }
      }
    }
  }

  /**
   * Log test part change activity
   */
  static async logTestPartChange(
    file: IUploadedFile,
    newTestPart: TestPartsEnum,
    candidateInfo: CandidateInfo,
    roleName: string,
    isGroupUpload: boolean = false
  ): Promise<void> {
    const operation = isGroupUpload
      ? OperationEnum.StatusChangedTestPartIdGoupUpload
      : OperationEnum.StatusChangedTestPartId;

    if (newTestPart === TestPartsEnum.EksamenDel1ogDel2) {
      // Log for both parts
      await this.logActivity(
        isGroupUpload
          ? OperationEnum.StatusChangedTestPartIdPart1TwoPartsGroupUpload
          : OperationEnum.StatusChangedTestPartIdPart1TwoPartsMonitor,
        candidateInfo,
        {
          roleName,
          fileName: file.Name,
          testPartId: TestPartsEnum.EksamenDel1,
        }
      );

      await this.logActivity(
        isGroupUpload
          ? OperationEnum.StatusChangedTestPartIdPart2TwoPartsGroupUpload
          : OperationEnum.StatusChangedTestPartIdPart2TwoPartsMonitor,
        candidateInfo,
        {
          roleName,
          fileName: file.Name,
          testPartId: TestPartsEnum.EksamenDel2,
          parameters: {
            partNumber: getTestPartForDescription(TestPartsEnum.EksamenDel2),
          },
        }
      );
    } else {
      await this.logActivity(operation, candidateInfo, {
        roleName,
        fileName: file.Name,
        testPartId: newTestPart,
        parameters: { partNumber: getTestPartForDescription(newTestPart) },
      });
    }
  }

  /**
   * Log file download activity
   */
  static async logFileDownload(
    fileName: string,
    testPartId: TestPartsEnum,
    candidateInfo: CandidateInfo,
    roleName: string
  ): Promise<void> {
    await this.logActivity(OperationEnum.StatusFileDownloaded, candidateInfo, {
      roleName,
      fileName,
      testPartId,
    });
  }

  /**
   * Log file checked status change (monitor specific)
   */
  static async logFileChecked(
    fileName: string,
    testPartId: TestPartsEnum,
    candidateInfo: CandidateInfo,
    roleName: string
  ): Promise<void> {
    await this.logActivity(OperationEnum.StatusFileChecked, candidateInfo, {
      roleName,
      fileName,
      testPartId,
    });
  }

  /**
   * Log monitor file delivery activity with appropriate operation based on test part
   */
  static async logMonitorFileDelivery(
    file: IUploadedFile,
    candidateInfo: CandidateInfo,
    roleName: string
  ): Promise<void> {
    let operation: OperationEnum;

    switch (file.TestPartId) {
      case TestPartsEnum.EksamenDel1:
        operation = OperationEnum.StatusFileDeliveredPart1;
        break;
      case TestPartsEnum.EksamenDel2:
        operation = OperationEnum.StatusFileDeliveredPart2;
        break;
      default:
        operation = OperationEnum.StatusFileDelivered;
    }

    await this.logActivity(operation, candidateInfo, {
      roleName,
      fileName: file.Name,
      testPartId: file.TestPartId,
    });
  }

  /**
   * Log monitor file delivery for both parts (special case)
   */
  static async logMonitorFileDeliveryBothParts(
    file: IUploadedFile,
    candidateInfo: CandidateInfo,
    roleName: string,
    isGroupUpload: boolean
  ): Promise<void> {
    if (isGroupUpload) {
      // Log for group upload two parts
      // Log for both parts
      await Promise.allSettled([
        this.logActivity(
          OperationEnum.StatusDeliverForGroupUploadTwoPartsPart1,
          candidateInfo,
          {
            roleName,
            fileName: file.Name,
            testPartId: TestPartsEnum.EksamenDel1,
          }
        ),
        this.logActivity(
          OperationEnum.StatusDeliverForGroupUploadTwoPartsPart2,
          candidateInfo,
          {
            roleName,
            fileName: file.Name,
            testPartId: TestPartsEnum.EksamenDel2,
          }
        ),
      ]);
    } else {
      await Promise.allSettled([
        this.logActivity(
          OperationEnum.StatusDeliverForMonitorTwoPartsPart1,
          candidateInfo,
          {
            roleName,
            fileName: file.Name,
            testPartId: TestPartsEnum.EksamenDel1,
          }
        ),
        this.logActivity(
          OperationEnum.StatusDeliverForMonitorTwoPartsPart2,
          candidateInfo,
          {
            roleName,
            fileName: file.Name,
            testPartId: TestPartsEnum.EksamenDel2,
          }
        ),
      ]);
    }
  }

  /**
   * Log exam submission actions - Open for new delivery
   */
  static async logOpenForNewDelivery(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    isOnePartExam: boolean
  ): Promise<void> {
    const testPartId = isOnePartExam
      ? TestPartsEnum.Eksamen
      : TestPartsEnum.EksamenDel2;
    const operation = isOnePartExam
      ? OperationEnum.StatusOpenedForNewDeliveryEksamen
      : OperationEnum.StatusOpenedForNewDelivery;

    await this.logActivity(
      operation,
      {
        userId: candidateInfo.userId,
        candidateName: candidateInfo.candidateName,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.candidateRegistrationId,
      },
      {
        roleName,
        testPartId,
        parameters: { partNumber: getTestPartForDescription(testPartId) },
      }
    );
  }

  /**
   * Log exam submission actions - Set delivered
   */
  static async logSetDelivered(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    isOnePartExam: boolean
  ): Promise<void> {
    const operation = isOnePartExam
      ? OperationEnum.StatusDeliveredExam
      : OperationEnum.StatusDeliveredPart2;

    const testPartId = isOnePartExam
      ? TestPartsEnum.Eksamen
      : TestPartsEnum.EksamenDel2;

    await this.logActivity(
      operation,
      {
        userId: candidateInfo.userId,
        candidateName: candidateInfo.candidateName,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.candidateRegistrationId,
      },
      {
        roleName,
        testPartId,
      }
    );
  }

  /**
   * Log exam submission actions - Digital access blocked
   */
  static async logDigitalAccessBlocked(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    isOnePartExam: boolean
  ): Promise<void> {
    const operation = isOnePartExam
      ? OperationEnum.StatusDeliveredDigitalAccessBlocked
      : OperationEnum.StatusDeliveredDigitalAccessBlockedPart2;

    const testPartId = isOnePartExam
      ? TestPartsEnum.Eksamen
      : TestPartsEnum.EksamenDel2;

    await this.logActivity(
      operation,
      {
        userId: candidateInfo.userId,
        candidateName: candidateInfo.candidateName,
        candidateNumber: candidateInfo.candidateNumber,
        candidateRegistrationId: candidateInfo.candidateRegistrationId,
      },
      {
        roleName: "PGS",
        testPartId,
      }
    );
  }

  /**
   * Log deliver on paper activity
   */
  static async logDeliverOnPaper(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    testPartId: TestPartsEnum
  ): Promise<{ success: boolean; error?: any }> {
    try {
      if (testPartId === TestPartsEnum.EksamenDel1ogDel2) {
        // Log for both parts

        this.logActivity(
          OperationEnum.StatusSendInPostPart1,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId: TestPartsEnum.EksamenDel1,
          }
        );

        this.logActivity(
          OperationEnum.StatusSendInPostPart2,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId: TestPartsEnum.EksamenDel2,
          }
        );

        await this.logActivity(
          OperationEnum.StatusSendInPostDigitalAccessBlockedPart2,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName: "PGS",
            testPartId: TestPartsEnum.EksamenDel2,
          }
        );
      } else {
        let operation: OperationEnum;
        let operationAccessBlocked: OperationEnum;
        switch (testPartId) {
          case TestPartsEnum.EksamenDel1:
            operation = OperationEnum.StatusSendInPostPart1;
            operationAccessBlocked =
              OperationEnum.StatusSendInPostDigitalAccessBlockedPart1;
            break;
          case TestPartsEnum.EksamenDel2:
            operation = OperationEnum.StatusSendInPostPart2;
            operationAccessBlocked =
              OperationEnum.StatusSendInPostDigitalAccessBlockedPart2;
            break;
          default:
            operation = OperationEnum.StatusSendInPost;
            operationAccessBlocked =
              OperationEnum.StatusSendInPostDigitalAccessBlocked;
        }

        await this.logActivity(
          operation,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId,
          }
        );

        await this.logActivity(
          operationAccessBlocked,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName: "PGS",
            testPartId: TestPartsEnum.EksamenDel1,
          }
        );
      }

      return { success: true };
    } catch (error) {
      console.error(
        "Critical error in audit logging for deliver on paper:",
        error
      );
      return { success: false, error };
    }
  }

  /**
   * Log undo deliver on paper activity
   */
  static async logUndoDeliverOnPaper(
    candidateInfo: ICandidateMonitor,
    roleName: string,
    testPartId: TestPartsEnum
  ): Promise<{ success: boolean; error?: any }> {
    try {
      if (testPartId === TestPartsEnum.EksamenDel1ogDel2) {
        // Log for both parts

        this.logActivity(
          OperationEnum.StatusUndoDeliveredOnPaperPart1,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId: TestPartsEnum.EksamenDel1,
          }
        );

        this.logActivity(
          OperationEnum.StatusUndoDeliveredOnPaperPart2,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId: TestPartsEnum.EksamenDel2,
          }
        );
      } else {
        let operation: OperationEnum;
        switch (testPartId) {
          case TestPartsEnum.Eksamen:
            operation = OperationEnum.StatusUndoDeliveredOnPaper;
            break;
          case TestPartsEnum.EksamenDel1:
            operation = OperationEnum.StatusUndoDeliveredOnPaperPart1;
            break;
          case TestPartsEnum.EksamenDel2:
            operation = OperationEnum.StatusUndoDeliveredOnPaperPart2;
            break;
          default:
            operation = OperationEnum.StatusUndoDeliveredOnPaper;
        }

        const result = await this.logActivity(
          operation,
          {
            userId: candidateInfo.userId,
            candidateName: candidateInfo.candidateName,
            candidateNumber: candidateInfo.candidateNumber,
            candidateRegistrationId: candidateInfo.candidateRegistrationId,
          },
          {
            roleName,
            testPartId,
          }
        );

        if (!result.success) {
          console.warn(
            "Audit log failed for undo deliver on paper action:",
            result.error
          );
        }
      }

      return { success: true };
    } catch (error) {
      console.error(
        "Critical error in audit logging for undo deliver on paper:",
        error
      );
      return { success: false, error };
    }
  }
}
