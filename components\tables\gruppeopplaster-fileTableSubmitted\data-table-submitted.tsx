"use client";

import React, { useState } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { IUploadedFile } from "@/interface/IUploadedFile";
import useFileHandler from "@/hooks/useFileHandler";

interface DataTableProps<TData> {
  columns: ColumnDef<TData>[];
  data: TData[];
}

export function DataTableSubmitted<TData extends IUploadedFile>({
  data,
  columns,
}: DataTableProps<TData>) {
  // Sett initial sortering på SubmittedDate i synkende rekkefølge (desc)
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "SubmittedDate",
      desc: true,
    },
  ]);

  const { clearSubmittedFiles } = useFileHandler();

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
    initialState: {
      sorting: [
        {
          id: "SubmittedDate",
          desc: true,
        },
      ],
    },
  });

  return (
    <div className="bg-green-50 p-4" aria-labelledby="submitted-files-heading">
      <div className="flex sm:flex-row justify-end sm:items-center mb-4 gap-4">
        <Button
          variant="outline"
          className="border-2 h-12 rounded-sm normal-case font-normal w-full sm:w-auto"
          onClick={() => clearSubmittedFiles()}
          aria-label="Tøm listen over leverte filer"
        >
          Tøm listen
        </Button>
      </div>
      <div className="overflow-x-auto rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="text-left font-bold">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-left">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Ingen leverte filer
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
