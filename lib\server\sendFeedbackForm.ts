"use server";

import { ISendFeedback } from "@/interface/ISendFeedback";
import { getServerSession } from "next-auth";
import { getAccessToken } from "./getAccessToken";
import { IRoleObject } from "@/interface/IRoleObject";

export async function sendFeedBack(prevState: any, formData: FormData) {
  const feedbackFromUser = formData.get("tilbakemelding");

  const role: IRoleObject | null = formData.get("role")
    ? (JSON.parse(formData.get("role") as string) as IRoleObject)
    : null;

  const path = formData.get("path");

  const match = path?.toString().match(/^(\/[^\/]+)/);
  const firstpath = match ? match[0] : "PGS-admin";

  const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
  const clientSecret: string =
    process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
  const scope: string =
    process.env.UDIR_PGS_ADMIN_RESOURCE_TILBAKEMELDING_SCOPE || "";
  const accesstokenKey: string = "PGSE:TilbakeMelding:AccessToken";

  try {
    if (!feedbackFromUser) {
      return {
        status: "error",
        message: "Feltet kan ikke være tom",
      };
    }

    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );
    const body: ISendFeedback = {
      User: role?.displayRoleName || "Ukjent",
      Application: firstpath,
      Project: 4,
      Message: feedbackFromUser as string,
    };

    const response = await fetch(
      `${process.env.PASX_TILBAKEMELDING_API_URL}/api/external/tilbakemeldinger`,
      {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (!response.ok) {
      console.log(
        `Klarte ikke å sende tilbakemeldingen. Feilkode:${response.status}. Feilmelding: ${response.statusText}`
      );
      return {
        status: "error",
        message: "Klarte ikke å sende tilbakemeldingen. Prøv igjen senere.",
      };
    }

    return {
      status: "success",
      message: "Tilbakemelding sendt!",
    };
  } catch (error) {
    console.error("Klarte ikke å sende tilbakemeldingen:", error);
    return {
      status: "error",
      message: "Klarte ikke å sende tilbakemeldingen. Prøv igjen senere.",
    };

    //throw new Error("Klarte ikke å sende tilbakemeldingen");
  }
}
