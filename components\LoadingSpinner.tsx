"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ImSpinner2 } from "react-icons/im";

interface LoadingSpinnerProps {
  message?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = "Laster inn..."
}) => {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-auto min-w-[320px] max-w-md shadow-xl border-2">
        <CardContent className="flex flex-col items-center justify-center p-10 space-y-6">
          <div className="relative">
            <ImSpinner2
              className="animate-spin text-7xl text-primary drop-shadow-sm"
              aria-hidden="true"
              role="img"
              aria-label="loading spinner"
            />
            {/* Optional: Add a subtle pulse effect */}
            <div className="absolute inset-0 animate-ping">
              <ImSpinner2
                className="text-7xl text-primary/20"
                aria-hidden="true"
              />
            </div>
          </div>
          <div className="text-center space-y-2">
            <p className="text-xl font-semibold text-foreground">
              {message}
            </p>
            <p className="text-sm text-muted-foreground">
              Vennligst vent...
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoadingSpinner;
