import * as React from "react";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, X } from "lucide-react";
import { IFagkodeShared } from "@/interface/IFagkodeShared";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  perioder: Array<{ value: string; label: string }>;
  selectedPeriode: string;
  showFiles?: boolean;
  onFilteredDataChange?: (filteredData: TData[]) => void;
}

export function FagkodeDataTable<TData, TValue>({
  columns,
  data,
  perioder,
  selectedPeriode,
  showFiles = false,
  onFilteredDataChange,
}: DataTableProps<TData, TValue>) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "fagkode",
      desc: false, // ascending
    },
  ]);
  const [eksamensdatoFilter, setEksamensdatoFilter] = useState<
    Date | undefined
  >(undefined);
  const [fagkodeFilter, setFagkodeFilter] = useState<string>("");
  const [showOnlyWithoutFiles, setShowOnlyWithoutFiles] = useState<boolean>(false);
  const [fileSizeFilter, setFileSizeFilter] = useState<string>("all");

  // Initialize filters from URL params on mount
  useEffect(() => {
    const fagkodeParam = searchParams.get('fagkode');
    const dateParam = searchParams.get('eksamensdato');
    const noFilesParam = searchParams.get('uten-filer');
    const fileSizeParam = searchParams.get('filstorrelse');

    if (fagkodeParam) {
      setFagkodeFilter(fagkodeParam);
    }

    if (dateParam) {
      try {
        const [year, month, day] = dateParam.split('-').map(Number);
        const date = new Date(year, month - 1, day); // month is 0-indexed
        setEksamensdatoFilter(date);
      } catch (error) {
        console.warn('Invalid date format in URL params:', dateParam);
      }
    }

    if (noFilesParam === 'true') {
      setShowOnlyWithoutFiles(true);
    }

    if (fileSizeParam && ['medium', 'large', 'xlarge'].includes(fileSizeParam)) {
      setFileSizeFilter(fileSizeParam);
    }
  }, [searchParams]);

  // Update URL when filters change
  const updateURL = React.useCallback((newFagkodeFilter: string, newDateFilter: Date | undefined, newShowOnlyWithoutFiles: boolean, newFileSizeFilter: string) => {
    const params = new URLSearchParams(searchParams.toString());

    // Update fagkode filter
    if (newFagkodeFilter.trim()) {
      params.set('fagkode', newFagkodeFilter.trim());
    } else {
      params.delete('fagkode');
    }

    // Update date filter
    if (newDateFilter) {
      const year = newDateFilter.getFullYear();
      const month = (newDateFilter.getMonth() + 1).toString().padStart(2, '0');
      const day = newDateFilter.getDate().toString().padStart(2, '0');
      params.set('eksamensdato', `${year}-${month}-${day}`);
    } else {
      params.delete('eksamensdato');
    }

    // Update no files filter
    if (newShowOnlyWithoutFiles) {
      params.set('uten-filer', 'true');
    } else {
      params.delete('uten-filer');
    }

    // Update file size filter
    if (newFileSizeFilter && newFileSizeFilter !== 'all') {
      params.set('filstorrelse', newFileSizeFilter);
    } else {
      params.delete('filstorrelse');
    }

    // Update URL without causing a page reload
    const newURL = params.toString() ? `?${params.toString()}` : window.location.pathname;
    router.replace(newURL, { scroll: false });
  }, [router, searchParams]);

  // Handler functions that update both state and URL
  const handleFagkodeFilterChange = React.useCallback((value: string) => {
    setFagkodeFilter(value);
    updateURL(value, eksamensdatoFilter, showOnlyWithoutFiles, fileSizeFilter);
  }, [updateURL, eksamensdatoFilter, showOnlyWithoutFiles, fileSizeFilter]);

  const handleDateFilterChange = React.useCallback((date: Date | undefined) => {
    setEksamensdatoFilter(date);
    updateURL(fagkodeFilter, date, showOnlyWithoutFiles, fileSizeFilter);
  }, [updateURL, fagkodeFilter, showOnlyWithoutFiles, fileSizeFilter]);

  const handleShowOnlyWithoutFilesChange = React.useCallback((checked: boolean) => {
    setShowOnlyWithoutFiles(checked);
    updateURL(fagkodeFilter, eksamensdatoFilter, checked, fileSizeFilter);
  }, [updateURL, fagkodeFilter, eksamensdatoFilter, fileSizeFilter]);

  const handleFileSizeFilterChange = React.useCallback((value: string) => {
    setFileSizeFilter(value);
    updateURL(fagkodeFilter, eksamensdatoFilter, showOnlyWithoutFiles, value);
  }, [updateURL, fagkodeFilter, eksamensdatoFilter, showOnlyWithoutFiles]);

  const handleClearFagkodeFilter = React.useCallback(() => {
    setFagkodeFilter("");
    updateURL("", eksamensdatoFilter, showOnlyWithoutFiles, fileSizeFilter);
  }, [updateURL, eksamensdatoFilter, showOnlyWithoutFiles, fileSizeFilter]);

  const handleClearDateFilter = React.useCallback(() => {
    setEksamensdatoFilter(undefined);
    updateURL(fagkodeFilter, undefined, showOnlyWithoutFiles, fileSizeFilter);
  }, [updateURL, fagkodeFilter, showOnlyWithoutFiles, fileSizeFilter]);

  // Calculate min and max dates from data
  const dateRange = React.useMemo(() => {
    const validDateStrings = data
      .map((item: any) => item.eksamensdato)
      .filter((date) => date && date !== null)
      .sort(); // Sort date strings in YYYY-MM-DD format

    if (validDateStrings.length === 0)
      return { minDate: undefined, maxDate: undefined };

    // Convert first and last date strings to Date objects for DatePicker
    const minDateStr = validDateStrings[0]; // "2025-02-07"
    const maxDateStr = validDateStrings[validDateStrings.length - 1];

    // Create Date objects using local date construction to avoid timezone conversion
    const [minYear, minMonth, minDay] = minDateStr.split("-").map(Number);
    const [maxYear, maxMonth, maxDay] = maxDateStr.split("-").map(Number);

    const minDate = new Date(minYear, minMonth - 1, minDay); // month is 0-indexed
    const maxDate = new Date(maxYear, maxMonth - 1, maxDay);

    return { minDate, maxDate };
  }, [data]);

  // Helper function to check if fagkode has any files
  const hasNoFiles = React.useCallback((fagkode: IFagkodeShared): boolean => {
    const eksamensdeler = fagkode.eksamensdeler || [];
    return !eksamensdeler.some((del: any) =>
      del.eksamensmateriell && del.eksamensmateriell.length > 0 &&
      del.eksamensmateriell.some((mat: any) => mat.filnavn && mat.blobReferanseEksamensmateriell)
    );
  }, []);

  // Helper function to check file sizes for fagkode
  const checkFileSize = React.useCallback((fagkode: IFagkodeShared, sizeCategory: string): boolean => {
    const eksamensdeler = fagkode.eksamensdeler || [];

    for (const del of eksamensdeler) {
      if (del.eksamensmateriell && del.eksamensmateriell.length > 0) {
        for (const mat of del.eksamensmateriell) {
          if (mat.fileSize) {
            const sizeInMB = mat.fileSize / (1024 * 1024);

            switch (sizeCategory) {
              case "small":
                if (sizeInMB < 1) return true;
                break;
              case "medium":
                if (sizeInMB >= 1 && sizeInMB < 5) return true;
                break;
              case "large":
                if (sizeInMB >= 5 && sizeInMB < 25) return true;
                break;
              case "xlarge":
                if (sizeInMB >= 25) return true;
                break;
            }
          }
        }
      }
    }
    return false;
  }, []);

  // Filter data manually before passing to table
  const filteredData = React.useMemo(() => {
    return data.filter((item: any) => {
      // Date filter
      if (eksamensdatoFilter) {
        const itemDate = item.eksamensdato;
        if (!itemDate) return false;

        // Database date is already in "YYYY-MM-DD" format
        const itemDateStr = itemDate; // "2025-09-16"

        // Convert filter date to same format without timezone conversion
        const year = eksamensdatoFilter.getFullYear();
        const month = (eksamensdatoFilter.getMonth() + 1)
          .toString()
          .padStart(2, "0");
        const day = eksamensdatoFilter.getDate().toString().padStart(2, "0");
        const filterDateStr = `${year}-${month}-${day}`;

        if (itemDateStr !== filterDateStr) return false;
      }

      // Fagkode filter
      if (fagkodeFilter.trim()) {
        const fagkode = item.fagkode?.toLowerCase() || "";
        const variantkode = item.variantkode?.toLowerCase() || "";
        const searchTerm = fagkodeFilter.toLowerCase().trim();

        const matchesFagkode = fagkode.includes(searchTerm);
        const matchesVariantkode = variantkode.includes(searchTerm);

        if (!matchesFagkode && !matchesVariantkode) return false;
      }

      // No files filter
      if (showOnlyWithoutFiles) {
        if (!hasNoFiles(item)) return false;
      }

      // File size filter
      if (fileSizeFilter !== "all") {
        if (!checkFileSize(item, fileSizeFilter)) return false;
      }

      return true;
    });
  }, [data, eksamensdatoFilter, fagkodeFilter, showOnlyWithoutFiles, fileSizeFilter, hasNoFiles, checkFileSize]);

  // Notify parent component when filtered data changes
  React.useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredData);
    }
  }, [filteredData, onFilteredDataChange]);

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
    },
    initialState: {
      pagination: {
        pageSize: 50,
      },
      sorting: [
        {
          id: "fagkode",
          desc: false, // ascending
        },
      ],
    },
  });

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
        {/* Filter controls */}
        <div className="space-y-3">
          <div className="text-sm font-medium text-gray-700">
            Filtrer fagkodene for valgt eksamensperiode:
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
            <div className="relative w-full sm:w-64">
              <div className="flex items-center relative">
                {/* Søkeikon */}
                <div className="absolute inset-y-0 left-0 ml-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-500" />
                </div>

                {/* Input-felt */}
                <Input
                  type="text"
                  placeholder="Søk fagkode..."
                  className="h-10 w-full pl-10 pr-10 bg-udirGray-100 py-2 text-sm border-0 border-b-2 border-black rounded-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0"
                  value={fagkodeFilter}
                  onChange={(e) => handleFagkodeFilterChange(e.target.value)}
                  aria-label="Søk fagkode for valgt eksamensperiode"
                />

                {/* X-knapp */}
                {fagkodeFilter && (
                  <button
                    onClick={handleClearFagkodeFilter}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-600"
                    aria-label="Tøm søk"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
            <DatePicker
              date={eksamensdatoFilter}
              onSelect={handleDateFilterChange}
              placeholder="Filter på eksamensdato"
              className="w-full sm:w-64"
              showClear={true}
              onClear={handleClearDateFilter}
              minDate={dateRange.minDate}
              maxDate={dateRange.maxDate}
            />
            {showFiles && (
              <Select value={fileSizeFilter} onValueChange={handleFileSizeFilterChange}>
                <SelectTrigger className="w-full sm:w-64 border-black border-[2px] h-10">
                  <SelectValue placeholder="Alle filstørrelser" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle filstørrelser</SelectItem>
                  <SelectItem value="medium">Medium filer (1-5MB)</SelectItem>
                  <SelectItem value="large">Store filer (5-25MB)</SelectItem>
                  <SelectItem value="xlarge">Meget store filer (&gt;25MB)</SelectItem>
                </SelectContent>
              </Select>
            )}
            {showFiles && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-only-without-files"
                  checked={showOnlyWithoutFiles}
                  onCheckedChange={handleShowOnlyWithoutFilesChange}
                />
                <label
                  htmlFor="show-only-without-files"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Vis fagkoder uten opplastede filer
                </label>
              </div>
            )}
          </div>
        </div>

        <div className="text-sm text-gray-600 text-center">
          Viser {filteredData.length} fagkoder for{" "}
          {perioder.find((p) => p.value === selectedPeriode)?.label}
        </div>
      </div>
      <div className="rounded-md border overflow-x-auto">
        <Table
          className="min-w-full"
          aria-label="Tabell med fagkoder og eksamensinformasjon"
          aria-describedby="fagkode-table-description"
        >
          <caption id="fagkode-table-description" className="sr-only">
            Oversikt over fagkoder i PGS med fagkode, variant, fagnavn, eksamensdato og lenke til eksamensplan.
            Tabellen kan sorteres ved å klikke på kolonneoverskriftene.
          </caption>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} scope="col">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="even:bg-gray-100 hover:bg-gray-50 focus-within:bg-gray-50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="whitespace-nowrap">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Ingen resultater
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-2">
        <div></div> {/* Empty div for spacing */}
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Side {table.getState().pagination.pageIndex + 1} av{" "}
          {table.getPageCount()}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="h-8 px-3"
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            aria-label="Gå til første side"
          >
            Første
          </Button>
          <Button
            variant="outline"
            className="h-8 px-3"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            aria-label="Gå til forrige side"
          >
            Forrige
          </Button>
          <Button
            variant="outline"
            className="h-8 px-3"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            aria-label="Gå til neste side"
          >
            Neste
          </Button>
          <Button
            variant="outline"
            className="h-8 px-3"
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
            aria-label="Gå til siste side"
          >
            Siste
          </Button>
        </div>
      </div>
    </div>
  );
}