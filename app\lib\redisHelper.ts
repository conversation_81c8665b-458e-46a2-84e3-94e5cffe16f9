import { ICandidate } from "@/interface/ICandidate";
import { ICandidateSession } from "@/interface/ICandidateSession";
import { IStatusInfo } from "@/interface/IStatusInfo";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import { DefaultAzureCredential } from "@azure/identity";
import dayjs from "dayjs";
import Redis from "ioredis";

const telemetryClient = getAppInsightsServer();
const useManagedIdentity = process.env.USE_AZURE_REDIS_MANAGED_IDENTITY === "true";

// Azure Redis token management
let redisTokenExpiresAt = 0;
const REDIS_TOKEN_REFRESH_MARGIN_MS = 3 * 60 * 1000; // Refresh 3 minutes before expiry (Microsoft recommendation)
let tokenRefreshPromise: Promise<void> | null = null; // Mutex for token refresh

/**
 * Gets Azure Redis access token and principal ID using managed identity
 */
async function getAzureRedisAuth(): Promise<{ token: string; principalId: string }> {
  try {
    const credential = new DefaultAzureCredential();

    // Get token with correct scope for Azure Cache for Redis
    const tokenResponse = await credential.getToken("https://redis.azure.com/.default");

    if (!tokenResponse?.token || !tokenResponse.expiresOnTimestamp) {
      throw new Error("Invalid token response from Azure");
    }

    redisTokenExpiresAt = tokenResponse.expiresOnTimestamp;

    // Decode JWT token to extract oid (Object ID / Principal ID)
    const tokenParts = tokenResponse.token.split('.');
    if (tokenParts.length !== 3) {
      throw new Error("Invalid JWT token format");
    }

    // Decode the payload (middle part of JWT)
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());

    // The 'oid' claim contains the Object ID (Principal ID)
    const principalId = payload.oid;

    if (!principalId) {
      throw new Error("No oid (Object ID) found in token payload");
    }

    return { token: tokenResponse.token, principalId };
  } catch (error) {
    console.error("Failed to get Azure Redis authentication:", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getAzureRedisAuth",
      },
    });
    throw error;
  }
}

/**
 * Checks if Redis token needs refresh and re-authenticates if necessary
 * Microsoft recommends refreshing token at least 3 minutes before expiry
 * Uses mutex pattern to prevent concurrent refresh attempts under high load
 */
async function refreshRedisTokenIfNeeded() {
  if (!useManagedIdentity || !redisClient) return;

  const now = Date.now();
  const timeUntilExpiry = redisTokenExpiresAt - now;

  if (timeUntilExpiry < REDIS_TOKEN_REFRESH_MARGIN_MS) {
    // If refresh is already in progress, wait for it to complete
    if (tokenRefreshPromise) {
      return tokenRefreshPromise;
    }

    // Start new refresh with mutex protection
    tokenRefreshPromise = (async () => {
      try {
        const { token, principalId } = await getAzureRedisAuth();
        await redisClient.auth(principalId, token);
      } catch (error) {
        console.error("Failed to refresh Redis token:", error);
        telemetryClient?.trackException({
          exception: error as Error,
          properties: {
            component: "RedisHelper",
            action: "refreshRedisTokenIfNeeded",
          },
        });
        throw error;
      } finally {
        // Release mutex
        tokenRefreshPromise = null;
      }
    })();

    return tokenRefreshPromise;
  }
}

/**
 * Creates and configures Redis client based on environment
 */
async function createRedisClient(): Promise<Redis> {
  if (useManagedIdentity) {
    // Azure production - use managed identity with Entra ID authentication
    const host = process.env.REDIS_HOST;
    const port = parseInt(process.env.REDIS_PORT || "6380");

    if (!host) {
      throw new Error("REDIS_HOST not configured for managed identity");
    }

    const { token, principalId } = await getAzureRedisAuth();

    const client = new Redis({
      host,
      port,
      username: principalId, // Managed Identity Object ID (from token's oid claim)
      password: token,        // Access token for Azure Cache for Redis
      tls: {
        servername: host,
      },
      retryStrategy(times) {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
      maxRetriesPerRequest: 3,
      enableReadyCheck: false, // Disabled because managed identity user has no INFO permissions
    });

    return client;
  } else {
    // Local development - use connection string
    const connectionString =
      process.env.CUSTOMCONNSTR_PGS_REDIS_CONNECTIONSTRING;

    if (!connectionString) {
      throw new Error(
        "CUSTOMCONNSTR_PGS_REDIS_CONNECTIONSTRING not configured"
      );
    }

    return new Redis(connectionString, {
      retryStrategy(times) {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
      maxRetriesPerRequest: 3,
      enableReadyCheck: false, // Disabled to avoid INFO permission issues
    });
  }
}

// Lazy initialization of Redis client
let redisClient: Redis | null = null;
let redisClientPromise: Promise<Redis> | null = null;

/**
 * Gets or initializes the Redis client (lazy initialization)
 * Also checks and refreshes token if needed before returning client
 */
async function getRedisClient(): Promise<Redis> {
  // First-time initialization
  if (!redisClient) {
    // If initialization is in progress, wait for it
    if (redisClientPromise) {
      redisClient = await redisClientPromise;
    } else {
      redisClientPromise = createRedisClient();

      try {
        redisClient = await redisClientPromise;

        // Setup error handling for Redis client
        redisClient.on("error", (error) => {
          console.error("Redis Client Error:", error);
          telemetryClient?.trackException({
            exception: error,
            properties: {
              component: "RedisHelper",
              action: "connection",
            },
          });
        });
      } catch (error) {
        redisClientPromise = null; // Reset so it can be retried
        throw error;
      }
    }
  }

  // Check and refresh token if needed before every operation
  await refreshRedisTokenIfNeeded();

  return redisClient;
}

/**
 * Gracefully closes Redis connection and cleans up resources
 */
export async function closeRedisConnection(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    redisClientPromise = null;
    console.log("Redis connection closed");
  }
}

export async function setValueInRedis(
  key: string,
  value: string,
  expirationInSeconds: number
) {
  const client = await getRedisClient();
  await client.set(key, value, "EX", expirationInSeconds);
}

export async function getValueFromRedis(key: string) {
  const client = await getRedisClient();
  const data = await client.get(key);
  return data ? data : null;
}

// Hash-funksjoner
export async function setHashFieldInRedis(
  key: string,
  field: string,
  value: string,
  expirationInSeconds?: number
) {
  const client = await getRedisClient();
  await client.hset(key, field, value);
  if (expirationInSeconds) {
    await client.expire(key, expirationInSeconds);
  }
}

export async function getHashFromRedis(key: string, field: string) {
  const client = await getRedisClient();
  return await client.hget(key, field);
}

export async function getAllHashFromRedis(key: string) {
  const client = await getRedisClient();
  return await client.hgetall(key);
}

export async function deleteHashFieldFromRedis(key: string, field: string) {
  const client = await getRedisClient();
  return await client.hdel(key, field);
}

export async function getHashFieldsFromRedis(key: string) {
  const client = await getRedisClient();
  return await client.hkeys(key);
}

// Funksjon for å sjekke om en hash-nøkkel eksisterer
export async function hashKeyExists(key: string) {
  const client = await getRedisClient();
  return await client.exists(key);
}

// Generisk pipeline funksjon for gjenbruk
export async function executeBatch<T>(
  commands: { command: string; args: any[] }[]
): Promise<T[]> {
  if (commands.length === 0) return [];

  const client = await getRedisClient();
  const pipeline = client.pipeline();

  commands.forEach(({ command, args }) => {
    (pipeline as any)[command](...args);
  });

  try {
    const results = await pipeline.exec();
    return (
      results
        ?.map(([err, data]) => {
          if (err) {
            console.error("Pipeline execution error:", err);
            telemetryClient?.trackException({
              exception: err,
              properties: {
                component: "RedisHelper",
                action: "executeBatch",
                command: commands[0].command,
              },
            });
            return null;
          }
          return data;
        })
        .filter((data): data is T => data !== null) || []
    );
  } catch (error) {
    console.error("Pipeline execution error:", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "executeBatch",
      },
    });
    return [];
  }
}

export async function getSessionDataWithPipeline(
  examGroupCode: string,
  crId: string
) {
  try {
    // Hent alle medlemmene i settet for CrIdSessionLog
    const sessionEntries = await getSessionSetMembers(examGroupCode, crId);

    if (sessionEntries.length === 0) {
      return [];
    }

    // Opprett kommandoer for pipeline
    const commands = sessionEntries.map((entry) => ({
      command: "hgetall",
      args: [entry],
    }));

    // Bruk den generiske pipeline funksjonen
    const results = await executeBatch<Record<string, string>>(commands);

    // Filtrer ut tomme resultater
    return results.filter((data) => Object.keys(data).length > 0);
  } catch (error) {
    console.error("Error in getSessionDataWithPipeline:", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getSessionDataWithPipeline",
        examGroupCode,
        crId,
      },
    });
    return [];
  }
}

// Legg til disse funksjonene i din redisHelper.ts fil

export async function getSessionSetMembers(
  examGroupCode: string,
  crId: string
): Promise<string[]> {
  const client = await getRedisClient();
  const setKey = `CrIdSessionLog:${examGroupCode}:${crId}`;
  return await client.smembers(setKey);
}

export async function getSessionHashData(
  entry: string
): Promise<Record<string, string> | null> {
  const client = await getRedisClient();
  const data = await client.hgetall(entry);
  return Object.keys(data).length > 0 ? data : null;
}

// Funksjon for å sette flere hash-felter samtidig
export async function setMultipleHashFields<T extends Record<string, any>>(
  key: string,
  fieldValues: T,
  expirationInSeconds?: number
) {
  if (!key || typeof key !== "string") {
    throw new Error("Invalid key");
  }

  const client = await getRedisClient();
  const entries = Object.entries(fieldValues).map(([field, value]) => [
    field,
    typeof value === "object" ? JSON.stringify(value) : String(value),
  ]);

  await client.hmset(key, Object.fromEntries(entries));

  if (expirationInSeconds) {
    await client.expire(key, expirationInSeconds);
  }
}

// Funksjon for å hente flere spesifikke hash-felter
export async function getMultipleHashFields(key: string, fields: string[]) {
  const client = await getRedisClient();
  return await client.hmget(key, ...fields);
}

export async function getPgsaStatusInfoFromRedis(
  userId: string
): Promise<IStatusInfo | null> {
  if (!userId) {
    throw new Error("UserId is missing");
  }

  const client = await getRedisClient();
  const key = `CandidateInfoStatusCache_CandidateInfoStatusCache:${userId}`;
  try {
    const value = await client.get(key);
    if (!value) {
      return null;
    }
    return JSON.parse(value);
  } catch (error) {
    console.error("Error in getPgsaStatusInfoFromRedis:", error);
    return null;
  }
}

export async function getExpirationTime(key: string) {
  try {
    const client = await getRedisClient();
    return await client.ttl(key); // TTL returns time in seconds
  } catch (error) {
    console.error("Error getting TTL:", error);
  }
}

export async function getMultipleValues(
  keys: string[]
): Promise<(string | null)[]> {
  if (keys.length === 0) return [];

  const commands = keys.map((key) => ({
    command: "get",
    args: [key],
  }));

  return await executeBatch<string | null>(commands);
}

interface RedisKeyValue {
  key: string;
  value: string | null;
}

/**
 * Generic function to retrieve hash objects with a specific prefix
 * @template T The type of hash objects to retrieve
 * @param prefix The prefix to scan for in Redis
 * @param transform Optional function to transform the hash data into the desired type
 * @returns Array of objects of type T
 */
export async function getHashObjectsWithPrefix<T>(
  prefix: string,
  transform?: (hashData: Record<string, string>, key: string) => T | null
): Promise<T[]> {
  const results: T[] = [];
  let cursor = "0";

  try {
    const client = await getRedisClient();
    do {
      // Get matching keys
      const [nextCursor, matchedKeys] = (await client.scan(
        cursor,
        "MATCH",
        `${prefix}*`,
        "COUNT",
        1000
      )) as [string, string[]];

      cursor = nextCursor;

      if (matchedKeys.length > 0) {
        // Use executeBatch to fetch hash objects
        const commands = matchedKeys.map((key) => ({
          command: "hgetall",
          args: [key],
        }));

        const hashObjects = await executeBatch<Record<string, string>>(
          commands
        );

        // Process the hash objects
        matchedKeys.forEach((key, index) => {
          const hashData = hashObjects[index];

          if (hashData && Object.keys(hashData).length > 0) {
            try {
              if (transform) {
                // Use the provided transform function
                const transformed = transform(hashData, key);
                if (transformed !== null) {
                  results.push(transformed);
                }
              } else {
                // Default transform: assume hashData can be directly cast to T
                results.push(hashData as unknown as T);
              }
            } catch (error) {
              console.error("Error transforming Redis hash object:", error);
              telemetryClient?.trackException({
                exception: error as Error,
                properties: {
                  component: "RedisHelper",
                  action: "getHashObjectsWithPrefix-transform",
                  key,
                },
              });
            }
          }
        });
      }
    } while (cursor !== "0");

    return results;
  } catch (error) {
    console.error("Error in getHashObjectsWithPrefix", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getHashObjectsWithPrefix",
        prefix,
      },
    });
    return [];
  }
}

/**
 * Legger til ett eller flere medlemmer i et Redis set
 * @param key Set-nøkkel
 * @param members Enkeltmedlem eller array av medlemmer som skal legges til
 * @param expirationUnix Unix timestamp for når settet skal utløpe (valgfritt)
 * @returns Antall medlemmer som ble lagt til
 */
export async function addToSet(
  key: string,
  members: string | string[],
  expirationUnix?: number
): Promise<number> {
  const client = await getRedisClient();
  // Konverter til array hvis det er en enkelt streng
  const memberArray = Array.isArray(members) ? members : [members];

  // Bruk spread operator for å sende inn array elementer som separate argumenter
  const response = await client.sadd(key, ...memberArray);

  // Sett utløpstid (default til slutten av dagen)
  const expiration = expirationUnix || dayjs().endOf("day").unix();
  await client.expireat(key, expiration);

  return response;
}

/**
 * Henter alle medlemmer i et set
 * @param key Set-nøkkel
 */
export async function getSetMembers(key: string): Promise<string[]> {
  const client = await getRedisClient();
  return await client.smembers(key);
}

/**
 * Fjerner medlemmer fra et set
 * @param key Set-nøkkel
 * @param members Medlemmer som skal fjernes
 */
export async function removeFromSet(
  key: string,
  ...members: string[]
): Promise<number> {
  const client = await getRedisClient();
  return await client.srem(key, members);
}

/**
 * Sletter et helt set fra Redis
 * @param key Set-nøkkel som skal slettes
 */
export async function removeSet(key: string): Promise<number> {
  const client = await getRedisClient();
  return await client.del(key);
}
