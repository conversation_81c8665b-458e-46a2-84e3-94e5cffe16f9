import { ICandidate } from "@/interface/ICandidate";
import { ICandidateSession } from "@/interface/ICandidateSession";
import { IStatusInfo } from "@/interface/IStatusInfo";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import dayjs from "dayjs";
import Redis from "ioredis";

// Initialize Redis client with configuration
const redisClient = new Redis(
  process.env.CUSTOMCONNSTR_PGS_REDIS_CONNECTIONSTRING || "",
  {
    retryStrategy(times) {
      const delay = Math.min(times * 50, 2000);
      return delay;
    },
    maxRetriesPerRequest: 3,
    enableReadyCheck: true,
  }
);

const telemetryClient = getAppInsightsServer();

// Setup error handling for Redis client
redisClient.on("error", (error) => {
  console.error("Redis Client Error:", error);
  telemetryClient?.trackException({
    exception: error,
    properties: {
      component: "RedisHelper",
      action: "connection",
    },
  });
});

export async function setValueInRedis(
  key: string,
  value: string,
  expirationInSeconds: number
) {
  await redisClient.set(key, value, "EX", expirationInSeconds);
}

export async function getValueFromRedis(key: string) {
  const data = await redisClient.get(key);
  return data ? data : null;
}

// Hash-funksjoner
export async function setHashFieldInRedis(
  key: string,
  field: string,
  value: string,
  expirationInSeconds?: number
) {
  await redisClient.hset(key, field, value);
  if (expirationInSeconds) {
    await redisClient.expire(key, expirationInSeconds);
  }
}

export async function getHashFromRedis(key: string, field: string) {
  return await redisClient.hget(key, field);
}

export async function getAllHashFromRedis(key: string) {
  return await redisClient.hgetall(key);
}

export async function deleteHashFieldFromRedis(key: string, field: string) {
  return await redisClient.hdel(key, field);
}

export async function getHashFieldsFromRedis(key: string) {
  return await redisClient.hkeys(key);
}

// Funksjon for å sjekke om en hash-nøkkel eksisterer
export async function hashKeyExists(key: string) {
  return await redisClient.exists(key);
}

// Generisk pipeline funksjon for gjenbruk
export async function executePipeline<T>(
  commands: { command: string; args: any[] }[]
): Promise<T[]> {
  if (commands.length === 0) return [];

  const pipeline = redisClient.pipeline();

  commands.forEach(({ command, args }) => {
    (pipeline as any)[command](...args);
  });

  try {
    const results = await pipeline.exec();
    return (
      results
        ?.map(([err, data]) => {
          if (err) {
            console.error("Pipeline execution error:", err);
            telemetryClient?.trackException({
              exception: err,
              properties: {
                component: "RedisHelper",
                action: "executePipeline",
                command: commands[0].command,
              },
            });
            return null;
          }
          return data;
        })
        .filter((data): data is T => data !== null) || []
    );
  } catch (error) {
    console.error("Pipeline execution error:", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "executePipeline",
      },
    });
    return [];
  }
}

export async function getSessionDataWithPipeline(
  examGroupCode: string,
  crId: string
) {
  try {
    // Hent alle medlemmene i settet for CrIdSessionLog
    const sessionEntries = await getSessionSetMembers(examGroupCode, crId);

    if (sessionEntries.length === 0) {
      return [];
    }

    // Opprett kommandoer for pipeline
    const commands = sessionEntries.map((entry) => ({
      command: "hgetall",
      args: [entry],
    }));

    // Bruk den generiske pipeline funksjonen
    const results = await executePipeline<Record<string, string>>(commands);

    // Filtrer ut tomme resultater
    return results.filter((data) => Object.keys(data).length > 0);
  } catch (error) {
    console.error("Error in getSessionDataWithPipeline:", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getSessionDataWithPipeline",
        examGroupCode,
        crId,
      },
    });
    return [];
  }
}

// Legg til disse funksjonene i din redisHelper.ts fil

export async function getSessionSetMembers(
  examGroupCode: string,
  crId: string
): Promise<string[]> {
  const setKey = `CrIdSessionLog:${examGroupCode}:${crId}`;
  return await redisClient.smembers(setKey);
}

export async function getSessionHashData(
  entry: string
): Promise<Record<string, string> | null> {
  const data = await redisClient.hgetall(entry);
  return Object.keys(data).length > 0 ? data : null;
}

// Funksjon for å sette flere hash-felter samtidig
export async function setMultipleHashFields<T extends Record<string, any>>(
  key: string,
  fieldValues: T,
  expirationInSeconds?: number
) {
  if (!key || typeof key !== "string") {
    throw new Error("Invalid key");
  }

  const entries = Object.entries(fieldValues).map(([field, value]) => [
    field,
    typeof value === "object" ? JSON.stringify(value) : String(value),
  ]);

  await redisClient.hmset(key, Object.fromEntries(entries));

  if (expirationInSeconds) {
    await redisClient.expire(key, expirationInSeconds);
  }
}

// Funksjon for å hente flere spesifikke hash-felter
export async function getMultipleHashFields(key: string, fields: string[]) {
  return await redisClient.hmget(key, ...fields);
}

export async function getPgsaStatusInfoFromRedis(
  userId: string
): Promise<IStatusInfo | null> {
  if (!userId) {
    throw new Error("UserId is missing");
  }

  const key = `CandidateInfoStatusCache_CandidateInfoStatusCache:${userId}`;
  try {
    const value = await redisClient.get(key);
    if (!value) {
      return null;
    }
    return JSON.parse(value);
  } catch (error) {
    console.error("Error in getPgsaStatusInfoFromRedis:", error);
    return null;
  }
}

export async function getExpirationTime(key: string) {
  try {
    return await redisClient.ttl(key); // TTL returns time in seconds
  } catch (error) {
    console.error("Error getting TTL:", error);
  }
}

export async function getMultipleValues(
  keys: string[]
): Promise<(string | null)[]> {
  if (keys.length === 0) return [];

  const commands = keys.map((key) => ({
    command: "get",
    args: [key],
  }));

  return await executePipeline<string | null>(commands);
}

interface RedisKeyValue {
  key: string;
  value: string | null;
}

/**
 * Generic function to retrieve hash objects with a specific prefix
 * @template T The type of hash objects to retrieve
 * @param prefix The prefix to scan for in Redis
 * @param transform Optional function to transform the hash data into the desired type
 * @returns Array of objects of type T
 */
export async function getHashObjectsWithPrefix<T>(
  prefix: string,
  transform?: (hashData: Record<string, string>, key: string) => T | null
): Promise<T[]> {
  const results: T[] = [];
  let cursor = "0";

  try {
    do {
      // Get matching keys
      const [nextCursor, matchedKeys] = (await redisClient.scan(
        cursor,
        "MATCH",
        `${prefix}*`,
        "COUNT",
        1000
      )) as [string, string[]];

      cursor = nextCursor;

      if (matchedKeys.length > 0) {
        // Use executePipeline to fetch hash objects
        const commands = matchedKeys.map((key) => ({
          command: "hgetall",
          args: [key],
        }));

        const hashObjects = await executePipeline<Record<string, string>>(
          commands
        );

        // Process the hash objects
        matchedKeys.forEach((key, index) => {
          const hashData = hashObjects[index];

          if (hashData && Object.keys(hashData).length > 0) {
            try {
              if (transform) {
                // Use the provided transform function
                const transformed = transform(hashData, key);
                if (transformed !== null) {
                  results.push(transformed);
                }
              } else {
                // Default transform: assume hashData can be directly cast to T
                results.push(hashData as unknown as T);
              }
            } catch (error) {
              console.error("Error transforming Redis hash object:", error);
              telemetryClient?.trackException({
                exception: error as Error,
                properties: {
                  component: "RedisHelper",
                  action: "getHashObjectsWithPrefix-transform",
                  key,
                },
              });
            }
          }
        });
      }
    } while (cursor !== "0");

    return results;
  } catch (error) {
    console.error("Error in getHashObjectsWithPrefix", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getHashObjectsWithPrefix",
        prefix,
      },
    });
    return [];
  }
}

/**
 * Legger til ett eller flere medlemmer i et Redis set
 * @param key Set-nøkkel
 * @param members Enkeltmedlem eller array av medlemmer som skal legges til
 * @param expirationUnix Unix timestamp for når settet skal utløpe (valgfritt)
 * @returns Antall medlemmer som ble lagt til
 */
export async function addToSet(
  key: string,
  members: string | string[],
  expirationUnix?: number
): Promise<number> {
  // Konverter til array hvis det er en enkelt streng
  const memberArray = Array.isArray(members) ? members : [members];

  // Bruk spread operator for å sende inn array elementer som separate argumenter
  const response = await redisClient.sadd(key, ...memberArray);

  // Sett utløpstid (default til slutten av dagen)
  const expiration = expirationUnix || dayjs().endOf("day").unix();
  await redisClient.expireat(key, expiration);

  return response;
}

/**
 * Henter alle medlemmer i et set
 * @param key Set-nøkkel
 */
export async function getSetMembers(key: string): Promise<string[]> {
  return await redisClient.smembers(key);
}

/**
 * Fjerner medlemmer fra et set
 * @param key Set-nøkkel
 * @param members Medlemmer som skal fjernes
 */
export async function removeFromSet(
  key: string,
  ...members: string[]
): Promise<number> {
  return await redisClient.srem(key, members);
}

/**
 * Sletter et helt set fra Redis
 * @param key Set-nøkkel som skal slettes
 */
export async function removeSet(key: string): Promise<number> {
  return await redisClient.del(key);
}
