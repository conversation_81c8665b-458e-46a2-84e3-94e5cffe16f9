import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/authOptions';
import { ISession } from '@/interface/ISession';
import { getDbConnection } from '@/db/connection';
import { Kandidateksamen } from '@/db/models/Kandidateksamen';
import { Kandidatgruppe } from '@/db/models/Kandidatgruppe';
import { Fagkodeeksamen } from '@/db/models/Fagkodeeksamen';
import { Person } from '@/db/models/Person';

interface StatisticsResponse {
  eksamensperiode: string;
  generell: {
    totaltAntallKandidater: number;
    totaltAntallGrupper: number;
    gjennomsnittKandidaterPerGruppe: number;
    kandidaterUtenGruppe: number;
    kandidaterIGrupperUtenFagkode: number;
    grupperUtenKandidater: number;
    antallUnikeSkoler: number;
    kandidaterDetaljer?: Array<{ kandidateksamenID: number; kandidatpameldingsID: string; fodselsnummer: string; kandidatnummer: string | null; fornavn: string | null; etternavn: string | null }>;
    grupperDetaljer?: Array<{ kandidatgruppeID: number; kandidatgruppekode: string; kandidatgruppenavn: string | null }>;
    kandidaterUtenGruppeDetaljer?: Array<{ kandidateksamenID: number; kandidatpameldingsID: string; fodselsnummer: string; kandidatnummer: string | null; fornavn: string | null; etternavn: string | null }>;
    grupperUtenKandidaterDetaljer?: Array<{ kandidatgruppeID: number; kandidatgruppekode: string; kandidatgruppenavn: string | null }>;
    skolerDetaljer?: Array<{ skoleID: string; antallKandidater: number; antallGrupper: number }>;
  };
  perSkole: Array<{
    skoleID: string;
    antallKandidater: number;
    antallGrupper: number;
  }>;
  perFagkode: Array<{
    fagkode: string;
    fagnavn: string;
    antallKandidater: number;
    antallGrupper: number;
    eksamensdato: string | null;
  }>;
  gruppestorrelser: Array<{
    storrelse: string;
    antall: number;
  }>;
  tidsmessig: {
    nyesteOpprettetDato: string | null;
    eldsteOpprettetDato: string | null;
    opprettelserPerDag: Array<{
      dato: string;
      antallKandidater: number;
      antallGrupper: number;
    }>;
  };
}

export async function GET(request: NextRequest) {
  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Ikke autorisert' }, { status: 401 });
    }

    // Authorization check - verify administrator role
    const userRoles = Array.isArray(session.user.role) ? session.user.role : [session.user.role];
    const hasAdminRole = userRoles.some(role => role.includes('urn:udir:pgsa:administrator'));

    if (!hasAdminRole) {
      return NextResponse.json({ error: 'Ikke tilgang' }, { status: 403 });
    }

    // Get eksamensperiode and date filters from query params
    const { searchParams } = new URL(request.url);
    const eksamensperiode = searchParams.get('eksamensperiode');
    const fraDatoParam = searchParams.get('fraDato');
    const tilDatoParam = searchParams.get('tilDato');

    if (!eksamensperiode) {
      return NextResponse.json({ error: 'Eksamensperiode er påkrevd' }, { status: 400 });
    }

    const fraDato = fraDatoParam ? new Date(fraDatoParam) : undefined;
    const tilDato = tilDatoParam ? new Date(tilDatoParam) : undefined;

    // Helper function to add date filters to query
    const addDateFilters = (query: any, alias: string, fraDato?: Date, tilDato?: Date) => {
      // Only apply date filtering if both from and to dates are provided
      if (fraDato && tilDato) {
        query.andWhere(`${alias}.CreatedDate >= :fraDato`, { fraDato });
        // Add one day to include the entire end date
        const tilDatoEnd = new Date(tilDato);
        tilDatoEnd.setDate(tilDatoEnd.getDate() + 1);
        query.andWhere(`${alias}.CreatedDate < :tilDato`, { tilDato: tilDatoEnd });
      }
      return query;
    };

    const AppDataSource = await getDbConnection();
    const kandidateksamenRepo = AppDataSource.getRepository(Kandidateksamen);
    const kandidatgruppeRepo = AppDataSource.getRepository(Kandidatgruppe);
    const fagkodeeksamenRepo = AppDataSource.getRepository(Fagkodeeksamen);

    // Get all kandidatgrupper for the period
    const fagkodeeksamenList = await fagkodeeksamenRepo.find({
      where: { Eksamensperiode: eksamensperiode },
      select: ['FagkodeeksamensID', 'Fagkode', 'Fagnavn']
    });

    const fagkodeeksamensIds = fagkodeeksamenList.map(f => f.FagkodeeksamensID);

    // Get kandidatgrupper with fagkodeeksamen
    let kandidatgrupperQuery = kandidatgruppeRepo
      .createQueryBuilder('kg')
      .where('kg.FagkodeeksamensID IN (:...ids)', { ids: fagkodeeksamensIds });
    kandidatgrupperQuery = addDateFilters(kandidatgrupperQuery, 'kg', fraDato, tilDato);
    let kandidatgrupper: Kandidatgruppe[] = await kandidatgrupperQuery.getMany();

    // Also get kandidatgrupper without fagkodeeksamen that match the period in their gruppekode
    // Gruppekode format is typically: SKOLEID_FAGKODE_PERIODE (e.g., 12345_MAT1234_H-2025)
    let kandidatgrupperUtenFagkodeQuery = kandidatgruppeRepo
      .createQueryBuilder('kg')
      .where('kg.FagkodeeksamensID IS NULL')
      .andWhere('kg.Kandidatgruppekode LIKE :periode', { periode: `%${eksamensperiode}%` });
    kandidatgrupperUtenFagkodeQuery = addDateFilters(kandidatgrupperUtenFagkodeQuery, 'kg', fraDato, tilDato);
    const kandidatgrupperUtenFagkode = await kandidatgrupperUtenFagkodeQuery.getMany();

    // Combine both sets (avoid duplicates)
    const kandidatgruppeIdsSet = new Set<number>();
    kandidatgrupper.forEach(kg => {
      if (kg.KandidatgruppeID) kandidatgruppeIdsSet.add(kg.KandidatgruppeID);
    });
    kandidatgrupperUtenFagkode.forEach(kg => {
      if (kg.KandidatgruppeID) {
        kandidatgruppeIdsSet.add(kg.KandidatgruppeID);
        // Add to kandidatgrupper array if not already there
        if (!kandidatgrupper.some(k => k.KandidatgruppeID === kg.KandidatgruppeID)) {
          kandidatgrupper.push(kg);
        }
      }
    });

    const kandidatgruppeIds = Array.from(kandidatgruppeIdsSet);

    if (kandidatgruppeIds.length === 0) {
      return NextResponse.json({
        error: 'Ingen kandidatgrupper funnet for denne eksamensperioden'
      }, { status: 404 });
    }



    // 1. GENERELL STATISTIKK
    const totaltAntallGrupper = kandidatgrupper.length;
    const filteredKandidatgruppeIds = kandidatgrupper.map(kg => kg.KandidatgruppeID).filter(id => id !== undefined) as number[];

    // Tell kandidater opprettet i perioden (kun innenfor alle grupper i eksamensperioden)
    let kandidaterQuery = kandidateksamenRepo
      .createQueryBuilder('k')
      .where('k.KandidatgruppeID IN (:...ids)', { ids: kandidatgruppeIds });
    kandidaterQuery = addDateFilters(kandidaterQuery, 'k', fraDato, tilDato);
    const totaltAntallKandidater = await kandidaterQuery.getCount();

    // Kandidater uten gruppe (KandidatgruppeID IS NULL)
    let kandidaterUtenGruppeQuery = kandidateksamenRepo
      .createQueryBuilder('k')
      .where('k.KandidatgruppeID IS NULL');
    kandidaterUtenGruppeQuery = addDateFilters(kandidaterUtenGruppeQuery, 'k', fraDato, tilDato);
    const kandidaterUtenGruppe = await kandidaterUtenGruppeQuery.getCount();

    // Kandidater i grupper uten fagkodeeksamen (for informasjon)
    let kandidaterIGrupperUtenFagkodeQuery = kandidateksamenRepo
      .createQueryBuilder('k')
      .innerJoin('Kandidatgruppe', 'kg', 'k.KandidatgruppeID = kg.KandidatgruppeID')
      .where('kg.FagkodeeksamensID IS NULL')
      .andWhere('kg.Kandidatgruppekode LIKE :periode', { periode: `%${eksamensperiode}%` });
    kandidaterIGrupperUtenFagkodeQuery = addDateFilters(kandidaterIGrupperUtenFagkodeQuery, 'k', fraDato, tilDato);
    const kandidaterIGrupperUtenFagkode = await kandidaterIGrupperUtenFagkodeQuery.getCount();

    // Finn hvor mange av de filtrerte gruppene som har kandidater (opprettet i perioden)
    let grupperUtenKandidater = totaltAntallGrupper;

    if (filteredKandidatgruppeIds.length > 0) {
      let grupperMedKandidaterQuery = kandidateksamenRepo
        .createQueryBuilder('k')
        .select('COUNT(DISTINCT k.KandidatgruppeID)', 'count')
        .where('k.KandidatgruppeID IN (:...ids)', { ids: filteredKandidatgruppeIds });
      grupperMedKandidaterQuery = addDateFilters(grupperMedKandidaterQuery, 'k', fraDato, tilDato);
      const grupperMedKandidater = await grupperMedKandidaterQuery.getRawOne();

      grupperUtenKandidater = totaltAntallGrupper - (grupperMedKandidater?.count || 0);
    }

    const gjennomsnittKandidaterPerGruppe = totaltAntallGrupper > 0
      ? Math.round((totaltAntallKandidater / totaltAntallGrupper) * 100) / 100
      : 0;

    // Hent detaljer for generell statistikk med Person-data
    let kandidaterDetaljerQuery = kandidateksamenRepo
      .createQueryBuilder('k')
      .leftJoinAndSelect('Person', 'p', 'p.Fodselsnummer = k.Fodselsnummer')
      .select([
        'k.KandidateksamenID',
        'k.KandidatpameldingsID',
        'k.Fodselsnummer',
        'k.Kandidatnummer',
        'p.Fornavn',
        'p.Etternavn'
      ])
      .orderBy('k.Kandidatnummer', 'ASC');
    kandidaterDetaljerQuery = addDateFilters(kandidaterDetaljerQuery, 'k', fraDato, tilDato);
    const kandidaterDetaljerRaw = await kandidaterDetaljerQuery.getRawMany();

    const grupperDetaljer = kandidatgrupper.map(kg => ({
      kandidatgruppeID: kg.KandidatgruppeID!,
      kandidatgruppekode: kg.Kandidatgruppekode,
      kandidatgruppenavn: kg.Kandidatgruppenavn || null
    }));

    let kandidaterUtenGruppeDetaljerQuery = kandidateksamenRepo
      .createQueryBuilder('k')
      .leftJoinAndSelect('Person', 'p', 'p.Fodselsnummer = k.Fodselsnummer')
      .select([
        'k.KandidateksamenID',
        'k.KandidatpameldingsID',
        'k.Fodselsnummer',
        'k.Kandidatnummer',
        'p.Fornavn',
        'p.Etternavn'
      ])
      .where('k.KandidatgruppeID IS NULL')
      .orderBy('k.Kandidatnummer', 'ASC');
    kandidaterUtenGruppeDetaljerQuery = addDateFilters(kandidaterUtenGruppeDetaljerQuery, 'k', fraDato, tilDato);
    const kandidaterUtenGruppeDetaljerRaw = await kandidaterUtenGruppeDetaljerQuery.getRawMany();

    // Finn grupper uten kandidater
    let grupperUtenKandidaterDetaljer: Array<{ kandidatgruppeID: number; kandidatgruppekode: string; kandidatgruppenavn: string | null }> = [];

    if (filteredKandidatgruppeIds.length > 0) {
      let grupperMedKandidaterQuery2 = kandidateksamenRepo
        .createQueryBuilder('k')
        .select('DISTINCT k.KandidatgruppeID', 'id')
        .where('k.KandidatgruppeID IN (:...ids)', { ids: filteredKandidatgruppeIds });
      grupperMedKandidaterQuery2 = addDateFilters(grupperMedKandidaterQuery2, 'k', fraDato, tilDato);
      const grupperMedKandidaterSet = new Set(
        (await grupperMedKandidaterQuery2.getRawMany()).map((r: any) => r.id)
      );

      grupperUtenKandidaterDetaljer = kandidatgrupper
        .filter(kg => kg.KandidatgruppeID && !grupperMedKandidaterSet.has(kg.KandidatgruppeID))
        .map(kg => ({
          kandidatgruppeID: kg.KandidatgruppeID!,
          kandidatgruppekode: kg.Kandidatgruppekode,
          kandidatgruppenavn: kg.Kandidatgruppenavn || null
        }));
    } else {
      // Hvis ingen filtrerte grupper, alle er uten kandidater
      grupperUtenKandidaterDetaljer = kandidatgrupper.map(kg => ({
        kandidatgruppeID: kg.KandidatgruppeID!,
        kandidatgruppekode: kg.Kandidatgruppekode,
        kandidatgruppenavn: kg.Kandidatgruppenavn || null
      }));
    }

    // 2. PER SKOLE
    const perSkole = await kandidatgruppeRepo
      .createQueryBuilder('kg')
      .select('kg.SkoleID', 'skoleID')
      .addSelect('COUNT(DISTINCT kg.KandidatgruppeID)', 'antallGrupper')
      .leftJoin('Kandidateksamen', 'k', 'k.KandidatgruppeID = kg.KandidatgruppeID')
      .addSelect('COUNT(DISTINCT k.KandidateksamenID)', 'antallKandidater')
      .where('kg.FagkodeeksamensID IN (:...ids)', { ids: fagkodeeksamensIds })
      .andWhere('kg.SkoleID IS NOT NULL')
      .groupBy('kg.SkoleID')
      .orderBy('COUNT(DISTINCT k.KandidateksamenID)', 'DESC')
      .limit(20)
      .getRawMany();

    // 4. PER FAGKODE
    const perFagkode = await fagkodeeksamenRepo
      .createQueryBuilder('f')
      .select('f.Fagkode', 'fagkode')
      .addSelect('f.Fagnavn', 'fagnavn')
      .addSelect('f.Eksamensdato', 'eksamensdato')
      .leftJoin('Kandidatgruppe', 'kg', 'kg.FagkodeeksamensID = f.FagkodeeksamensID')
      .addSelect('COUNT(DISTINCT kg.KandidatgruppeID)', 'antallGrupper')
      .leftJoin('Kandidateksamen', 'k', 'k.KandidatgruppeID = kg.KandidatgruppeID')
      .addSelect('COUNT(DISTINCT k.KandidateksamenID)', 'antallKandidater')
      .where('f.Eksamensperiode = :periode', { periode: eksamensperiode })
      .groupBy('f.Fagkode')
      .addGroupBy('f.Fagnavn')
      .addGroupBy('f.Eksamensdato')
      .orderBy('COUNT(DISTINCT k.KandidateksamenID)', 'DESC')
      .getRawMany();

    // Antall unike skoler (kun de som har grupper)
    const antallUnikeSkoler = perSkole.length;

    // Skoler detaljer for generell statistikk (sortert etter antall kandidater)
    const skolerDetaljer = perSkole.map((s: any) => ({
      skoleID: s.skoleID || 'Ukjent',
      antallKandidater: parseInt(s.antallKandidater) || 0,
      antallGrupper: parseInt(s.antallGrupper) || 0
    }));

    // 5. GRUPPESTØRRELSER
    const gruppestorrelserRaw = await kandidateksamenRepo
      .createQueryBuilder('k')
      .select('k.KandidatgruppeID', 'gruppeId')
      .addSelect('COUNT(*)', 'antall')
      .where('k.KandidatgruppeID IN (:...ids)', { ids: kandidatgruppeIds })
      .groupBy('k.KandidatgruppeID')
      .getRawMany();

    // Kategoriser gruppestørrelser
    const gruppestorrelser = [
      { storrelse: '1-5', antall: 0 },
      { storrelse: '6-10', antall: 0 },
      { storrelse: '11-15', antall: 0 },
      { storrelse: '16-20', antall: 0 },
      { storrelse: '21-30', antall: 0 },
      { storrelse: '31+', antall: 0 }
    ];

    gruppestorrelserRaw.forEach((gruppe: any) => {
      const antall = parseInt(gruppe.antall);
      if (antall <= 5) gruppestorrelser[0].antall++;
      else if (antall <= 10) gruppestorrelser[1].antall++;
      else if (antall <= 15) gruppestorrelser[2].antall++;
      else if (antall <= 20) gruppestorrelser[3].antall++;
      else if (antall <= 30) gruppestorrelser[4].antall++;
      else gruppestorrelser[5].antall++;
    });

    // 6. TIDSMESSIG STATISTIKK
    const tidsmessigStats = await kandidateksamenRepo
      .createQueryBuilder('k')
      .select('MIN(k.CreatedDate)', 'eldste')
      .addSelect('MAX(k.CreatedDate)', 'nyeste')
      .where('k.KandidatgruppeID IN (:...ids)', { ids: kandidatgruppeIds })
      .getRawOne();

    // Hent kandidater og grupper per eksamensdato
    const opprettelserPerDag = await fagkodeeksamenRepo
      .createQueryBuilder('f')
      .select('CAST(f.Eksamensdato AS DATE)', 'dato')
      .leftJoin('Kandidatgruppe', 'kg', 'kg.FagkodeeksamensID = f.FagkodeeksamensID')
      .leftJoin('Kandidateksamen', 'k', 'k.KandidatgruppeID = kg.KandidatgruppeID')
      .addSelect('COUNT(DISTINCT k.KandidateksamenID)', 'antallKandidater')
      .addSelect('COUNT(DISTINCT kg.KandidatgruppeID)', 'antallGrupper')
      .where('f.Eksamensperiode = :periode', { periode: eksamensperiode })
      .andWhere('f.Eksamensdato IS NOT NULL')
      .groupBy('CAST(f.Eksamensdato AS DATE)')
      .orderBy('CAST(f.Eksamensdato AS DATE)', 'ASC')
      .getRawMany()
      .then((results: any[]) => results.map(r => ({
        dato: r.dato.toString(),
        antallKandidater: parseInt(r.antallKandidater) || 0,
        antallGrupper: parseInt(r.antallGrupper) || 0
      })));

    const response: StatisticsResponse = {
      eksamensperiode,
      generell: {
        totaltAntallKandidater,
        totaltAntallGrupper,
        gjennomsnittKandidaterPerGruppe,
        kandidaterUtenGruppe,
        kandidaterIGrupperUtenFagkode,
        grupperUtenKandidater,
        antallUnikeSkoler,
        kandidaterDetaljer: kandidaterDetaljerRaw.map((k: any) => ({
          kandidateksamenID: k.k_KandidateksamenID,
          kandidatpameldingsID: k.k_KandidatpameldingsID,
          fodselsnummer: k.k_Fodselsnummer,
          kandidatnummer: k.k_Kandidatnummer || null,
          fornavn: k.p_Fornavn || null,
          etternavn: k.p_Etternavn || null
        })),
        grupperDetaljer,
        kandidaterUtenGruppeDetaljer: kandidaterUtenGruppeDetaljerRaw.map((k: any) => ({
          kandidateksamenID: k.k_KandidateksamenID,
          kandidatpameldingsID: k.k_KandidatpameldingsID,
          fodselsnummer: k.k_Fodselsnummer,
          kandidatnummer: k.k_Kandidatnummer || null,
          fornavn: k.p_Fornavn || null,
          etternavn: k.p_Etternavn || null
        })),
        grupperUtenKandidaterDetaljer,
        skolerDetaljer
      },
      perSkole: perSkole.map((s: any) => ({
        skoleID: s.skoleID || 'Ukjent',
        antallKandidater: parseInt(s.antallKandidater) || 0,
        antallGrupper: parseInt(s.antallGrupper) || 0
      })),
      perFagkode: perFagkode.map((f: any) => ({
        fagkode: f.fagkode,
        fagnavn: f.fagnavn,
        antallKandidater: parseInt(f.antallKandidater) || 0,
        antallGrupper: parseInt(f.antallGrupper) || 0,
        eksamensdato: f.eksamensdato || null
      })),
      gruppestorrelser,
      tidsmessig: {
        nyesteOpprettetDato: tidsmessigStats?.nyeste || null,
        eldsteOpprettetDato: tidsmessigStats?.eldste || null,
        opprettelserPerDag
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Feil ved henting av statistikk:', error);
    return NextResponse.json({
      error: 'En feil oppstod under henting av statistikk',
      details: error instanceof Error ? error.message : 'Ukjent feil'
    }, { status: 500 });
  }
}
