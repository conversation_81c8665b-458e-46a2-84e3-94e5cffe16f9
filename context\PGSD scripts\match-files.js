import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Les begge JSON-filene
const metadataPath = path.join(__dirname, 'PGSD-test-metadata.json');
const copiedFilesPath = path.join(__dirname, 'copied-files.json');

console.log('Leser metadata fil...');
const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));

console.log('Leser copied files fil...');
const copiedFiles = JSON.parse(fs.readFileSync(copiedFilesPath, 'utf8'));

console.log(`Fant ${metadata.length} metadata entries og ${copiedFiles.length} copied files`);

// Opprett index av metadata basert på ulike nøkler for rask oppslag
const metadataByGenFileName = new Map();
const metadataByComposite = new Map();

metadata.forEach(item => {
    // Index by GenFileName hvis den finnes
    if (item.GenFileName) {
        metadataByGenFileName.set(item.GenFileName, item);
    }

    // Index by composite key (fagkode + eksamensperiode + materiellkategori + eksamensdel)
    const compositeKey = `${item.Fagkode}|${item.Eksamensperiode}|${item.Materiellkategori}|${item.Eksamensdel}`;
    if (!metadataByComposite.has(compositeKey)) {
        metadataByComposite.set(compositeKey, []);
    }
    metadataByComposite.get(compositeKey).push(item);

    // Også index by OrginaltFilnavn for fallback matching
    const originalFileKey = `${item.Fagkode}|${item.Eksamensperiode}|${item.OrginaltFilnavn}`;
    if (!metadataByComposite.has(originalFileKey)) {
        metadataByComposite.set(originalFileKey, []);
    }
    metadataByComposite.get(originalFileKey).push(item);
});

// Match copied files med metadata
const enhancedCopiedFiles = copiedFiles.map(copiedFile => {
    let match = null;
    let matchType = 'none';

    // Først: Prøv å matche på GenFileName
    if (copiedFile.GenFileName && metadataByGenFileName.has(copiedFile.GenFileName)) {
        match = metadataByGenFileName.get(copiedFile.GenFileName);
        matchType = 'GenFileName';
    }

    // Fallback 1: Match på composite key (fagkode + eksamensperiode + materiellkategori + eksamensdel)
    if (!match && copiedFile.Fagkode && copiedFile.Eksamensperiode && copiedFile.Materiellkategori && copiedFile.Eksamensdel) {
        const compositeKey = `${copiedFile.Fagkode}|${copiedFile.Eksamensperiode}|${copiedFile.Materiellkategori}|${copiedFile.Eksamensdel}`;
        const matches = metadataByComposite.get(compositeKey);
        if (matches && matches.length > 0) {
            match = matches[0]; // Ta første match
            matchType = 'Composite';
        }
    }

    // Bygg eksamensperiode fra År og Semester_Id hvis ikke finnes
    let eksamensperiode = copiedFile.Eksamensperiode;
    if (!eksamensperiode && copiedFile.År && copiedFile.Semester_Id) {
        eksamensperiode = `${copiedFile.Semester_Id}-${copiedFile.År}`;
    }

    // Fallback 2: Match på OrginaltFilnavn kombinert med fagkode og eksamensperiode
    if (!match && copiedFile.OrginaltFilnavn && copiedFile.Fagkode && eksamensperiode) {
        const originalFileKey = `${copiedFile.Fagkode}|${eksamensperiode}|${copiedFile.OrginaltFilnavn}`;
        const matches = metadataByComposite.get(originalFileKey);
        if (matches && matches.length > 0) {
            match = matches[0];
            matchType = 'OriginalFileName';
        }
    }

    // Fallback 3: Match kun på fagkode og origanlt filnavn
    if (!match && copiedFile.OrginaltFilnavn && copiedFile.Fagkode) {
        // Finn alle metadata entries som matcher fagkode
        const fagkodeMatches = metadata.filter(m =>
            m.Fagkode === copiedFile.Fagkode &&
            m.OrginaltFilnavn === copiedFile.OrginaltFilnavn
        );
        if (fagkodeMatches.length > 0) {
            match = fagkodeMatches[0];
            matchType = 'FagkodeAndOriginalFile';
        }
    }

    // Fallback 4: Match kun på fagkode og år/semester
    if (!match && copiedFile.Fagkode && eksamensperiode) {
        const periodMatches = metadata.filter(m =>
            m.Fagkode === copiedFile.Fagkode &&
            m.Eksamensperiode === eksamensperiode
        );
        if (periodMatches.length > 0) {
            match = periodMatches[0];
            matchType = 'FagkodeAndPeriod';
        }
    }

    // Returner copied file med tillegg av opphavsrett og vedlegg
    const enhanced = {
        ...copiedFile,
        Opphavsrett: match ? match.Opphavsrett : null,
        Vedlegg: match ? match.Vedlegg : null
    };

    return enhanced;
});

// Statistikk
const totalMatched = enhancedCopiedFiles.filter(f => f.Opphavsrett !== null || f.Vedlegg !== null).length;
const unmatched = enhancedCopiedFiles.length - totalMatched;

console.log('Matching statistikk:');
console.log(`Total filer: ${copiedFiles.length}`);
console.log(`Matchet med metadata: ${totalMatched}`);
console.log(`Umatchet: ${unmatched}`);

// Filtrer kun filer som har opphavsrett eller vedlegg
const filteredFiles = enhancedCopiedFiles.filter(file =>
    file.Opphavsrett === "1" || file.Vedlegg === "1"
);

console.log(`\nFiltrering: ${enhancedCopiedFiles.length} -> ${filteredFiles.length} filer (kun med opphavsrett eller vedlegg)`);

// Skriv resultatet til ny fil
const outputPath = path.join(__dirname, 'enhanced-copied-files.json');
fs.writeFileSync(outputPath, JSON.stringify(filteredFiles, null, 2), 'utf8');

console.log(`\nResultat skrevet til: ${outputPath}`);
console.log(`Totalt ${filteredFiles.length} filer med opphavsrett eller vedlegg inkludert`);

// Vis eksempler på filtrerte filer
console.log('\nEksempler på filer med opphavsrett eller vedlegg:');
filteredFiles
    .slice(0, 5)
    .forEach(f => {
        console.log(`- ${f.OrginaltFilnavn} -> Opphavsrett: ${f.Opphavsrett}, Vedlegg: ${f.Vedlegg}`);
    });

// Vis statistikk for opphavsrett og vedlegg
const opphavsrettCount = filteredFiles.filter(f => f.Opphavsrett === "1").length;
const vedleggCount = filteredFiles.filter(f => f.Vedlegg === "1").length;
const bothCount = filteredFiles.filter(f => f.Opphavsrett === "1" && f.Vedlegg === "1").length;

console.log(`\nOpphavsrett/Vedlegg statistikk:`);
console.log(`- Kun opphavsrett: ${opphavsrettCount - bothCount}`);
console.log(`- Kun vedlegg: ${vedleggCount - bothCount}`);
console.log(`- Både opphavsrett og vedlegg: ${bothCount}`);
console.log(`- Totalt: ${filteredFiles.length}`);