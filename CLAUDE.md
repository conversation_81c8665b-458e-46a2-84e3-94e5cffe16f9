# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server (port 4000)
- `npm run build` - Build production bundle
- `npm run start` - Start production server (port 4000)
- `npm run lint` - Run ESLint

## Testing & Quality Assurance

No specific test framework is configured. Always check with the user for testing preferences before implementing tests.

## Project Architecture

### Framework & Technologies
- **Next.js 15** with App Router, TypeScript, and standalone output for Azure deployment
- **Authentication**: NextAuth.js with Duende IdentityServer6 (OIDC)
- **Database**: TypeORM with SQL Server, Azure Active Directory authentication
- **Styling**: Tailwind CSS with shadcn/ui components and custom Udir design system
- **Internationalization**: next-intl with Norwegian (nb, nn) locales
- **State Management**: React Context for school data and candidate monitoring
- **Real-time**: SignalR for live updates in monitoring features

### Key System Components

#### Authentication & Authorization
- Role-based access control with URN-based roles (urn:udir:eksamen:*, urn:udir:pgsa:*)
- Session management with 8-hour expiry
- Middleware handles route protection based on user roles
- Access control defined in `app/lib/accessControl.ts`

#### Database Architecture
- TypeORM entities: AuditLog, Operation, Fagkodeeksamen, Eksamensdel
- Composite primary keys for exam plan data (e.g., "H-2025_LIM3102")
- Connection pooling with Azure SQL authentication
- Audit logging for all operations

#### Core Features
- **PGS Monitor**: Real-time candidate monitoring with SignalR
- **Group Upload**: Bulk file uploads with validation
- **Download System**: Role-based file downloads
- **Admin Functions**: Exam plan import from external APIs
- **Candidate Management**: Status tracking and session monitoring

#### File Structure
- `/app/[locale]/` - Internationalized pages
- `/app/api/` - API routes
- `/components/` - Reusable components and tables
- `/context/` - React contexts (SchoolData, CandidateMonitor, etc.)
- `/db/` - TypeORM models and services
- `/hooks/` - Custom React hooks
- `/interface/` - TypeScript interfaces
- `/lib/` - Utility functions and services

### Azure Deployment
- Configured for Azure App Service with Linux
- Uses standalone Next.js build output
- Startup command: `npm start`
- Static assets cached with immutable headers
- Application Insights integration for monitoring

### Development Notes
- Norwegian is the primary language (nb locale)
- Custom Udir color scheme defined in Tailwind config
- SignalR for real-time updates in monitoring features
- Role-based menu system with hierarchical permissions
- Comprehensive error handling and logging

## Important Technical Details

### Environment-Specific Behavior
- **Local Development**: Uses standard database connection with username/password
- **Azure Production**: Uses managed identity with automatic token refresh every 2 hours
- Development server runs on port 4000 (both dev and production modes)

### MDX Integration
- Supports both `.md` and `.mdx` files with rehype-slug and remark-gfm plugins
- User guides and documentation are stored as MDX files in `/app/[locale]/markdown/`
- Uses `@next/mdx` for seamless integration with Next.js pages

### Custom File Upload System
- Multi-part file upload with validation
- Integration with Azure Blob Storage using SAS tokens
- Real-time status updates via SignalR during upload process
- File status tracking with database persistence