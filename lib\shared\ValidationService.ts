import { IUploadedFile } from "@/interface/IUploadedFile";
import { IPgsaValidateFilePayload } from "@/interface/IPgsaValidateFilePayload";
import { IBulkUploadResponse } from "@/interface/IBulkUploadResponse";
import { FileRejection } from "react-dropzone";
import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";

// Configuration constants
const FILE_CONFIG = {
  MAX_SIZE: 40 * 1024 * 1024,
  MIN_SIZE: 5,
  NAME_REGEX: /^\d{3}[A-Za-z]{3}-[vVhH]-\d{2,3}$/,
} as const;

export interface ValidationError {
  message: string;
  code?: string;
}

export interface ValidationResult {
  isValid: boolean;
  file: IUploadedFile;
  errors?: string[];
}

export interface FileValidationOptions {
  existingFiles: IUploadedFile[];
  mimeTypes: IAllowedMimeTypes[];
  selectedRoleSchoolId: string;
}

export class ValidationService {
  /**
   * Main validation method for accepted files in group upload
   */
  static async validateAcceptedFile(
    file: File,
    options: FileValidationOptions
  ): Promise<ValidationResult> {
    const { existingFiles, mimeTypes, selectedRoleSchoolId } = options;
    
    // Get existing file names for duplicate check
    const fileNames = new Set(
      existingFiles
        .filter((file) => !file.IsRejected)
        .map((file) => file.Name.replace(/\.[^.]+$/, "").toLowerCase())
    );

    // Run basic validations
    const errors = [
      ...this.validateFileName(file, fileNames),
      ...this.validateFileSize(file),
      ...this.validateFileType(file, mimeTypes),
    ];

    if (errors.length > 0) {
      return {
        isValid: false,
        file: await this.createRejectedFile(file, errors),
        errors,
      };
    }

    // Run PGSA validation
    return await this.validateWithPgsa(file, mimeTypes, selectedRoleSchoolId);
  }

  /**
   * Validation for dropzone rejected files
   */
  static async validateRejectedFile(rejection: FileRejection): Promise<IUploadedFile> {
    const errors = rejection.errors.map((error) => error.message);
    return await this.createRejectedFile(rejection.file, errors);
  }

  /**
   * General file validator (used in other contexts)
   */
  static validateFileGeneral(
    file: File,
    mimeTypes: IAllowedMimeTypes[],
    uploadedFiles: IUploadedFile[],
    selectedSchoolId?: string
  ): ValidationError[] | null {
    if (!file || typeof file.name !== "string") {
      return [
        {
          message: "Ugyldig fil: Filen mangler navn eller er ikke definert.",
          code: "INVALID_FILE",
        },
      ];
    }

    // Check if already rejected
    const isAlreadyRejected = uploadedFiles.some(
      (uploadedFile) => uploadedFile.Name === file.name && uploadedFile.IsRejected
    );

    if (isAlreadyRejected) return null;

    const errors: ValidationError[] = [];

    // School ID validation
    if (!selectedSchoolId) {
      errors.push({
        message: "Rollen du har valgt har ikke tilknyttet en skole",
        code: "ROLE_MISSING_SCHOOL_ID",
      });
    }

    // File name validation
    const fileName = file.name.replace(/\.[^.]+$/, "");
    if (!FILE_CONFIG.NAME_REGEX.test(fileName)) {
      errors.push({
        message: "Filen har et ugyldig filnavn (Se beskrivelsen øverst på siden)",
        code: "INVALID_FILE_NAME",
      });
    }

    // File type validation
    const fileExtension = `.${file.name.split(".").pop()}`;
    if (!this.isValidFileType(fileExtension, mimeTypes)) {
      errors.push({
        message: "Filen har en ugyldig filtype",
        code: "FILE_TYPE_NOT_SUPPORTED",
      });
    }

    // File size validation
    if (typeof file.size !== "number") {
      errors.push({
        message: "Ugyldig fil: Filstørrelsen kan ikke bestemmes.",
        code: "INVALID_FILE_SIZE",
      });
    } else {
      const sizeErrors = this.validateFileSize(file);
      errors.push(...sizeErrors.map(msg => ({ message: msg, code: "FILE_SIZE_ERROR" })));
    }

    return errors.length > 0 ? errors : null;
  }

  /**
   * Validate file name
   */
  private static validateFileName(file: File, existingFileNames: Set<string>): string[] {
    const errors: string[] = [];
    const fileName = file.name.replace(/\.[^.]+$/, "").toLowerCase();

    if (existingFileNames.has(fileName)) {
      errors.push("En fil med dette navnet er allerede lastet opp.");
    }

    if (!FILE_CONFIG.NAME_REGEX.test(fileName)) {
      errors.push(
        "Filen har et ugyldig filnavn (Se beskrivelsen øverst på siden)"
      );
    }

    return errors;
  }

  /**
   * Validate file size
   */
  private static validateFileSize(file: File): string[] {
    const errors: string[] = [];

    if (file.size > FILE_CONFIG.MAX_SIZE) {
      errors.push("Filen overstiger maksimal filstørrelse på 40 MB.");
    }
    if (file.size < FILE_CONFIG.MIN_SIZE) {
      errors.push("Filen er for liten. Minimum filstørrelse er 5 bytes.");
    }

    return errors;
  }

  /**
   * Validate file type
   */
  private static validateFileType(file: File, mimeTypes: IAllowedMimeTypes[]): string[] {
    const errors: string[] = [];
    const fileExtension = `.${file.name.split(".").pop()}`;

    if (!this.isValidFileType(fileExtension, mimeTypes)) {
      errors.push("Filen har en ugyldig filtype");
    }

    return errors;
  }

  /**
   * Check if file type is valid
   */
  private static isValidFileType(fileExtension: string, mimeTypes: IAllowedMimeTypes[]): boolean {
    return mimeTypes.some(
      (type) => type.FileExtension.toLowerCase() === fileExtension.toLowerCase()
    );
  }

  /**
   * Validate file with PGSA API
   */
  private static async validateWithPgsa(
    file: File,
    mimeTypes: IAllowedMimeTypes[],
    selectedRoleSchoolId: string
  ): Promise<ValidationResult> {
    const payload: IPgsaValidateFilePayload = {
      CandidateNumber: this.getCandidateNumber(file),
      FileName: file.name.toUpperCase(),
      SchoolId: selectedRoleSchoolId,
      MimeType: this.getMimeTypeFromFile(file, mimeTypes) || "",
      Size: file.size,
    };

    try {
      const uploadedFile: Partial<IUploadedFile> = {
        File: file,
        UploadedDate: dayjs(),
        Name: file.name,
        Size: file.size,
        IsRejected: false,
        IsLoading: true,
        UploadFinished: false,
        Candididate: this.getCandidateNumber(file),
        Delivered: false,
        IsSubmitting: false,
        IsDeleting: false,
        SchoolId: selectedRoleSchoolId,
      };

      const response = await fetch(`${window.location.origin}/api/pgsaValidate`, {
        method: "POST",
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText =
          (await response.text()) || "Ukjent feil under validering av fil";
        return {
          isValid: false,
          file: await this.createRejectedFile(file, [errorText?.replace(/"/g, "")]),
          errors: [errorText?.replace(/"/g, "")],
        };
      }

      const result: IBulkUploadResponse = await response.json();

      return {
        isValid: true,
        file: {
          ...uploadedFile,
          TestPartId: result.testParts === 1 ? 1 : 0,
          SubjectCode: result.subjectCode,
          FileGuid: result.documentCode,
          Groupcode: result.groupCode,
          CandidateName: result.candidateName,
          candidateRegistrationId: result.candiateRegistrationId,
        } as IUploadedFile,
      };
    } catch (error) {
      console.error("Ikke validert", error);
      return {
        isValid: false,
        file: await this.createRejectedFile(file, ["Feil under validering av fil"]),
        errors: ["Feil under validering av fil"],
      };
    }
  }

  /**
   * Create rejected file object
   */
  private static async createRejectedFile(
    file: File,
    errors: string[]
  ): Promise<IUploadedFile> {
    return {
      File: file,
      TestPartId: 0,
      UploadedDate: dayjs(),
      Name: file.name,
      Size: file.size,
      FileGuid: uuidv4(),
      Errors: errors,
      IsRejected: true,
      IsLoading: false,
      UploadFinished: true,
      Groupcode: "",
      Candididate: "",
      Delivered: false,
      IsSubmitting: false,
      IsDeleting: false,
      SchoolId: "",
    };
  }

  /**
   * Extract candidate number from filename
   */
  static getCandidateNumber(file: File): string {
    const hyphenIndex = file.name.lastIndexOf("-");
    return hyphenIndex !== -1
      ? file.name.substring(0, hyphenIndex).toUpperCase()
      : "";
  }

  /**
   * Get MIME type from file extension
   */
  static getMimeTypeFromFile(
    file: File,
    mimeTypes: IAllowedMimeTypes[]
  ): string | undefined {
    const fileExtension = `.${file.name.split(".").pop()}`;
    return mimeTypes.find(
      (type) => type.FileExtension.toLowerCase() === fileExtension.toLowerCase()
    )?.MimeType;
  }

  /**
   * Update total size and file count
   */
  static updateTotalSizeAndFiles(
    uploadedFiles: IUploadedFile[]
  ): { totalSize: number; totalFiles: number } {
    const validFiles = uploadedFiles.filter(
      (file) => !file.IsRejected && !file.Delivered
    );
    return {
      totalSize: validFiles.reduce((sum, file) => sum + file.Size, 0),
      totalFiles: validFiles.length,
    };
  }

  /**
   * Get file count for tabs
   */
  static getFileCount(uploadedFiles: IUploadedFile[], tab: string): string {
    const count = uploadedFiles.filter(
      (file) =>
        (tab === "opplastedeFiler" ? !file.Delivered : file.Delivered) &&
        !file.IsRejected
    ).length;

    return count > 0 ? ` (${count})` : "";
  }
}
