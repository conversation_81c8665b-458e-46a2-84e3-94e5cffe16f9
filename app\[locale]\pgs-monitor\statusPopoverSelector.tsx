"use client";

import { useState } from "react";
import { Check, ChevronDown } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { useRouter, useSearchParams } from "next/navigation";

interface Props {}

const StatusPopover: React.FC<Props> = () => {
  const [openStatus, setOpenStatus] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const statusFilterLabel = "statuses";

  const uniqueStatuses = [
    "Ikke lastet opp",
    "Laster opp",
    "Levert digitalt",
    "Sendes i posten",
  ] as const;

  // Get selected statuses from URL
  const getSelectedStatuses = (): string[] => {
    const statuses = searchParams.get(statusFilterLabel)?.split(",") || [];
    return statuses.filter((status) => uniqueStatuses.includes(status as any));
  };

  // Update URL with selected statuses
  const updateQueryString = (statuses: string[]) => {
    const params = new URLSearchParams(searchParams.toString());

    if (statuses.length > 0) {
      params.set(statusFilterLabel, statuses.join(","));
    } else {
      params.delete(statusFilterLabel);
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleStatusChange = (status: string, checked: boolean) => {
    const currentStatuses = getSelectedStatuses();
    const newStatuses = checked
      ? [...currentStatuses, status]
      : currentStatuses.filter((s) => s !== status);

    updateQueryString(newStatuses);
  };

  const selectedStatuses = getSelectedStatuses();

  return (
    <div className="w-full">
      <Popover open={openStatus} onOpenChange={setOpenStatus}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-between transition-colors duration-200 ${
              openStatus ? "bg-udirLightAzure-100" : ""
            }`}
            aria-controls="status"
            aria-expanded={openStatus}
            aria-haspopup="dialog"
            aria-label={`Fremdriftsstatusfilter. ${selectedStatuses.length > 0 ? `${selectedStatuses.length} statuser valgt` : 'Ingen statuser valgt'}`}
          >
            <div className="flex items-center truncate">
              {selectedStatuses.length > 0 ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm">Fremdriftsstatus</span>
                  <Badge variant="secondary" className="bg-mint">
                    {selectedStatuses.length}
                  </Badge>
                </div>
              ) : (
                "Fremdriftsstatus"
              )}
            </div>
            <ChevronDown
              role="img"
              aria-label="åpne/lukke"
              className="ml-2 h-5 w-5 shrink-0 opacity-70 transition-transform duration-200"
              style={{
                transform: openStatus ? "rotate(180deg)" : "rotate(0deg)",
              }}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0 shadow-lg"
          sideOffset={4}
          style={{
            width:
              typeof window !== "undefined" && window.innerWidth < 768
                ? "var(--radix-popover-trigger-width)"
                : "auto",
          }}
        >
          <Command className="rounded-sm border border-gray-200 bg-udirLightAzure-100">
            <CommandList
              className="max-h-64 overflow-auto"
              role="listbox"
              aria-label="Fremdriftsstatusalternativer"
            >
              <CommandEmpty className="py-6 text-center text-sm text-gray-500">
                Ingen resultater funnet
              </CommandEmpty>
              <CommandGroup className="p-1">
                {uniqueStatuses.map((status) => (
                  <CommandItem
                    key={status}
                    value={status}
                    onSelect={() => {
                      const isSelected = selectedStatuses.includes(status);
                      handleStatusChange(status, !isSelected);
                    }}
                    className="flex items-center gap-2 px-2 py-1.5 hover:bg-udirLightAzure-300 rounded-md cursor-pointer transition-colors duration-150"
                  >
                    <div className="flex items-center flex-1 gap-2">
                      <Checkbox
                        id={`status-${status.replace(/\s+/g, '-').toLowerCase()}`}
                        checked={selectedStatuses.includes(status)}
                        onCheckedChange={(checked) => {
                          handleStatusChange(status, checked as boolean);
                        }}
                        className="border-udirLightAzure-500"
                        onClick={(e) => e.stopPropagation()}
                        aria-describedby={`status-label-${status.replace(/\s+/g, '-').toLowerCase()}`}
                      />
                      <label
                        htmlFor={`status-${status.replace(/\s+/g, '-').toLowerCase()}`}
                        id={`status-label-${status.replace(/\s+/g, '-').toLowerCase()}`}
                        className="text-sm cursor-pointer"
                      >
                        {status}
                      </label>
                    </div>
                    {selectedStatuses.includes(status) && (
                      <Check className="h-4 w-4 text-mint" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default StatusPopover;
