import { Skeleton } from "./ui/skeleton";

interface TableSkeletonFagkodeProps {
  showFiles?: boolean;
  showPlagiarism?: boolean;
  showAdditionalColumns?: boolean;
}

export function TableSkeletonFagkode({
  showFiles = true,
  showPlagiarism = true,
  showAdditionalColumns = true
}: TableSkeletonFagkodeProps) {
  // Calculate number of columns based on options
  let columnCount = 5; // Base columns: fagkode, variant, fagnavn, eksamensdato, todelt, lenke

  if (!showFiles || !showAdditionalColumns) {
    columnCount += 1; // Add eksamenstid column
  }

  if (showPlagiarism) {
    columnCount += 1; // Add plagiarism column
  }

  if (showFiles) {
    columnCount += 1; // Add files column
  }

  if (showAdditionalColumns) {
    columnCount += 2; // Add læreplan and siste eksamen columns
  }

  return (
    <div className="space-y-4">
      {/* Responsive filter area skeleton */}
      <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
        <div className="space-y-3">
          <Skeleton className="h-4 w-64" />
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 w-full sm:w-64" />
            <Skeleton className="h-10 w-full sm:w-64" />
          </div>
        </div>
        <Skeleton className="h-4 w-48 mx-auto md:mx-0" />
      </div>

      {/* Responsive table skeleton */}
      <div className="border rounded-md overflow-x-auto">
        <div className="min-w-full">
          {/* Header */}
          <div className={`hidden md:grid gap-4 p-4 border-b`} style={{ gridTemplateColumns: `repeat(${columnCount}, 1fr)` }}>
            {Array.from({ length: columnCount }).map((_, i) => (
              <Skeleton key={i} className="h-6 w-20" />
            ))}
          </div>

          {/* Mobile header */}
          <div className="md:hidden grid grid-cols-2 gap-4 p-4 border-b">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-24" />
          </div>

          {/* Table rows */}
          <div className="flex flex-col gap-2 p-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i}>
                {/* Desktop row */}
                <div className={`hidden md:grid gap-4`} style={{ gridTemplateColumns: `repeat(${columnCount}, 1fr)` }}>
                  {Array.from({ length: columnCount }).map((_, j) => (
                    <Skeleton key={j} className="h-5 w-16" />
                  ))}
                </div>

                {/* Mobile row */}
                <div className="md:hidden space-y-2 p-3 border rounded">
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-12" />
                    <Skeleton className="h-4 w-4" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Responsive pagination skeleton */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 px-2">
        <div></div>
        <Skeleton className="h-5 w-24" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
    </div>
  );
}