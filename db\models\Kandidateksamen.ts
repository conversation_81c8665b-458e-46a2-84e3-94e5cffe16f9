import "reflect-metadata";
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from "typeorm";
import { Kandidatgruppe } from "./Kandidatgruppe";
import { Besvarelsesdel } from "./Besvarelsesdel";

@Entity('Kandidateksamen')
export class Kandidateksamen {
    @PrimaryGeneratedColumn({ type: 'int' })
    KandidateksamenID!: number;

    @Column({ type: 'varchar', nullable: false })
    KandidatpameldingsID!: string;

    @Column({ type: 'varchar', nullable: false })
    Fodselsnummer!: string;

    @Column({ type: 'varchar', nullable: true })
    Kandidatnummer?: string;

    @Column({ type: 'varchar', nullable: true })
    Malform?: string;

    @Column({ type: 'varchar', nullable: true })
    Oppmotstatus?: string;

    @CreateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    CreatedDate!: Date;

    @UpdateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    ModifiedDate!: Date;

    @Column({ type: 'int', nullable: true })
    KandidatgruppeID?: number;

    // Relationship to Kandidatgruppe
    @ManyToOne(() => Kandidatgruppe)
    @JoinColumn({ name: "KandidatgruppeID" })
    kandidatgruppe?: Kandidatgruppe;

    // Relationship to Besvarelsesdel
    @OneToMany(() => Besvarelsesdel, (del) => del.kandidateksamen)
    besvarelsesdeler?: Besvarelsesdel[];
}
