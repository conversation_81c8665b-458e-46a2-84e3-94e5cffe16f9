import { memo, useCallback } from "react";
import { RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CustomDropdown } from "@/components/ui/CustomDropdown";

interface FormData {
  fagkode: string;
  variant: string;
  eksamensperiode: string;
}

interface SearchFormFieldsProps {
  formData: FormData;
  handleInputChange: (field: keyof FormData, value: string) => void;
  isLoading: boolean;
  onFetchFiles?: () => void;
  canFetch?: boolean;
  isFetching?: boolean;
}

const EKSAMENSPERIODER = ["V-2025", "H-2025"];

export const SearchFormFields = memo(function SearchFormFields({ 
  formData, 
  handleInputChange, 
  isLoading,
  onFetchFiles,
  canFetch = false,
  isFetching = false
}: SearchFormFieldsProps) {
  const formatPeriode = useCallback((periode: string) => 
    `${periode} (${periode.startsWith("H") ? "Høst" : "Vår"} ${periode.split("-")[1]})`, []);
  return (
    <div className="flex flex-col md:flex-row gap-4 md:items-end items-center">
      <div className="flex-[2] space-y-2 w-full md:w-auto">
        <Label htmlFor="fagkode" className="text-sm font-medium">Fagkode *</Label>
        <Input
          id="fagkode"
          type="text"
          value={formData.fagkode}
          onChange={(e) => handleInputChange("fagkode", e.target.value)}
          disabled={isLoading}
          placeholder="Skriv inn fagkode (eks: FSP5960)"
          autoComplete="off"
          aria-required={true}
          className="h-10 w-full bg-udirGray-100 py-2 text-sm border-0 border-b-2 border-black rounded-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0"
        />
      </div>

      <div className="flex-[1] space-y-2 w-full md:w-auto">
        <Label htmlFor="variant" className="text-sm font-medium">Variant</Label>
        <Input
          id="variant"
          type="text"
          value={formData.variant}
          onChange={(e) => handleInputChange("variant", e.target.value)}
          disabled={isLoading}
          placeholder="Skriv inn variant (valgfritt)"
          className="h-10 w-full bg-udirGray-100 py-2 text-sm border-0 border-b-2 border-black rounded-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0"
        />
      </div>

      <div className="flex-[2] space-y-2 w-full md:w-auto">
        <Label htmlFor="eksamensperiode-search" className="text-sm font-medium">Eksamensperiode *</Label>
        <CustomDropdown
          id="eksamensperiode-search"
          value={formData.eksamensperiode}
          onValueChange={(value) => handleInputChange("eksamensperiode", value)}
          options={EKSAMENSPERIODER}
          placeholder="Velg eksamensperiode"
          disabled={isLoading}
          label="Eksamensperiode"
          formatOption={formatPeriode}
          aria-required={true}
        />
      </div>

      {onFetchFiles && (
        <div className="flex-shrink-0 w-full md:w-auto flex justify-center md:justify-start">
          <Button 
            onClick={onFetchFiles} 
            disabled={!canFetch || isFetching}
            variant="outline"
            className="flex items-center gap-2 h-10"
          >
            {isFetching ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                Henter...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4" />
                Hent filer
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
});