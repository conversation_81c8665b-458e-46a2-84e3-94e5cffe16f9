"use client";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TestPartsEnum } from "@/enums/TestPart";
import useFileHandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, CheckIcon, ChevronDown } from "lucide-react";
import { IoWarning } from "react-icons/io5";
import { GoTrash } from "react-icons/go";
import { ImSpinner2 } from "react-icons/im";
import dayjs from "dayjs";

function formatFileSize(bytes: number) {
  const KB = 1024;
  const MB = 1024 * KB;
  if (bytes < MB) {
    return `${(bytes / KB).toFixed(0)} kB`;
  } else {
    return `${(bytes / MB).toFixed(2)} MB`;
  }
}

function formatTestPartId(testpartId: TestPartsEnum) {
  switch (testpartId) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og 2";
    default:
      return (
        <div className="flex items-center gap-1 text-red-600">
          <IoWarning className="text-lg" />
          {"Angi eksamensdel"}
        </div>
      );
  }
}

function minimizeSuffix(filename: string) {
  const parts = filename.split(".");
  if (parts.length > 1) {
    const suffix = parts.pop();
    return parts.join(".") + "." + suffix?.toLowerCase();
  }
  return filename;
}

export const getColumns = ({
  handleFileDelete,
  filesToDelete,
  isUploading,
}: {
  handleFileDelete: (file: IUploadedFile) => Promise<void>;
  filesToDelete: Set<string>;
  isUploading: boolean;
}): ColumnDef<IUploadedFile>[] => {
  const { downloadFile, setStatusChecked, setTestPart } =
    useFileHandlerForMonitor();

  return [
    {
      accessorKey: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          disabled={isUploading}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all files"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={`Select row ${row.original.Name}`}
          disabled={isUploading}
        />
      ),
      size: 40,
    },
    {
      accessorKey: "Name",
      header: ({ table, column }) => {
        const rowCount = table.getRowCount();
        return (
          <div className="text-left w-full">
            <Button
              variant="ghost"
              className="p-0 hover:bg-transparent justify-start w-full"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === "asc")
              }
            >
              <div className="capitalize mr-2">Filer ({rowCount})</div>
              <ArrowUpDown
                className="h-4 w-4"
                role="img"
                aria-label="pil opp ned ikon"
              />
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const file = row.original.File;
        const fileName = file?.name || row.original.Name || "";
        return (
          <div className="text-left">
            <Button
              variant="link"
              className="mt-1 text-current text-left p-0"
              onClick={() =>
                downloadFile(row.original.FileGuid, row.original.Name)
              }
            >
              {minimizeSuffix(fileName)}
            </Button>
          </div>
        );
      },
      size: 300,
    },
    {
      accessorKey: "Size",
      header: ({ table, column }) => (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent hidden lg:flex"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          <div>Størrelse</div>
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="pil opp ned ikon"
          />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="hidden lg:block">
          {formatFileSize(row.getValue("Size"))}
        </div>
      ),
      size: 120,
    },
    {
      accessorKey: "SubmittedDate",
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent hidden lg:flex"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          <div className="capitalize">Opplastet</div>
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="pil opp ned ikon"
          />
        </Button>
      ),
      cell: ({ row }) => {
        const date: string = row.getValue("SubmittedDate");
        const formattedDate = dayjs(date).isValid()
          ? dayjs(date).format("HH:mm")
          : "Ugyldig dato";
        return (
          <div className="capitalize hidden lg:block">{formattedDate}</div>
        );
      },
      size: 120,
    },
    {
      accessorKey: "UploadedBy",
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent hidden lg:flex"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Opplastet av
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="pil opp ned ikon"
          />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-left hidden lg:block">
          {row.original.UploadedBy}
        </div>
      ),
      size: 150,
    },
    {
      accessorKey: "TestPartId",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            <div className="capitalize">Eksamensdel</div>
            <ArrowUpDown
              className="ml-2 h-4 w-4"
              role="img"
              aria-label="pil opp ned ikon"
            />
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const file = row.original;
        const testPart = row.getValue("TestPartId") as TestPartsEnum;

        const handleTestPartChange = (newTestPart: TestPartsEnum) => {
          try {
            setTestPart(file.FileGuid, newTestPart);
          } catch (error) {
            // Handle error if needed
          }
        };

        if (testPart === TestPartsEnum.Eksamen) {
          return (
            <div className="text-center capitalize">
              {formatTestPartId(testPart)}
            </div>
          );
        }

        return (
          <div className="flex justify-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="justify-center min-w-[150px] border border-gray-300"
                  disabled={
                    file.IsLoading ||
                    file.IsSubmitting ||
                    file.IsDeleting ||
                    isUploading
                  }
                >
                  {file.IsLoading ? (
                    <ImSpinner2
                      className="animate-spin text-lg mr-2"
                      role="img"
                      aria-label="spinner"
                    />
                  ) : (
                    <span>{formatTestPartId(testPart)}</span>
                  )}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() =>
                    handleTestPartChange(TestPartsEnum.EksamenDel1)
                  }
                >
                  {formatTestPartId(TestPartsEnum.EksamenDel1)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    handleTestPartChange(TestPartsEnum.EksamenDel2)
                  }
                >
                  {formatTestPartId(TestPartsEnum.EksamenDel2)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    handleTestPartChange(TestPartsEnum.EksamenDel1ogDel2)
                  }
                >
                  {formatTestPartId(TestPartsEnum.EksamenDel1ogDel2)}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
      size: 200,
    },
    {
      accessorKey: "check",
      header: () => <div>Sjekk</div>,
      cell: ({ row }) => (
        <Button
          variant={row.original.Checked ? "ghost" : "outline"}
          className={`${row.original.Checked ? "text-green-600" : ""}`}
          onClick={() => setStatusChecked(true, row.original.FileGuid)}
        >
          {row.original.Checked ? (
            <>
              <CheckIcon
                color="green"
                className="mr-2"
                role="img"
                aria-label="filen er sjekket"
              />
              <span>Sjekket</span>
            </>
          ) : (
            <span>Filen er sjekket</span>
          )}
        </Button>
      ),
      size: 50,
    },
    {
      accessorKey: "Delete",
      header: () => <div></div>,
      cell: ({ row }) => (
        <Button
          variant="outline"
          className="flex items-center gap-1"
          disabled={
            row.original.IsDeleting ||
            row.original.IsSubmitting ||
            row.original.IsLoading ||
            filesToDelete.has(row.original.FileGuid) ||
            isUploading
          }
          onClick={() => handleFileDelete(row.original)}
        >
          {row.original.IsDeleting ||
          filesToDelete.has(row.original.FileGuid) ? (
            <>
              <ImSpinner2
                className="animate-spin text-lg"
                role="img"
                aria-label="spinner"
              />
              <span className="hidden sm:inline">Sletter</span>
            </>
          ) : (
            <>
              <GoTrash role="img" aria-label="slett fil" className="text-lg" />
              <span className="hidden sm:inline">Slett</span>
            </>
          )}
        </Button>
      ),
      size: 50,
    },
  ];
};
