import { getDbConnection } from "../connection";
import { Kandidateksamen } from "../models/Kandidateksamen";
import { Kandidatgruppe } from "../models/Kandidatgruppe";
import { Person } from "../models/Person";
import { generateAccessToken } from "@/lib/server/getAccessTokenForEksamen";

interface AttendanceStatus {
  attendanceStatus: number;
  testPartTypeId: number;
  timestamp: string;
}

interface ExamGroupDetail {
  testPeriodCode: string;
  candidateNumber: string;
  candidateRegistrationId: number;
  dateOfBirth: string;
  sosialSecurityNumber: string;
  candidateFirstname: string;
  candidateSurname: string;
  attendanceStatus: number;
  attendanceStatuses: AttendanceStatus[];
}

interface TestPart {
  continuesTestPart: number;
  genFileNames: string[];
  onlyAllowCompressedFiles: number;
  testEndDate: string;
  testEndTime: string;
  testPartTypeId: number;
  testPartTypeName: string;
  testStartDate: string;
  testStartTime: string;
  allowElectronicLogin: boolean;
  documentGuids: string[];
}

interface ExamGroupApiResponse {
  groupCode: string;
  testTypeCode: string;
  testPeriodCode: string;
  subjectCode: string;
  variantCode: string;
  subjectName: string;
  groupCodeDescription: string;
  schoolId: string;
  testParts: TestPart[];
  examGroupDetailList: ExamGroupDetail[];
}

export class KandidateksamenService {
  /**
   * Henter kandidateksamen data fra ekstern API for en spesifikk kandidatgruppe
   */
  private async fetchKandidateksamenForGroup(groupCode: string): Promise<ExamGroupApiResponse> {
    const accessToken = await generateAccessToken();
    const apiUrl = process.env.EKSAMENAPI_URL || "";

    const response = await fetch(
      `${apiUrl}/pgsa/examgroup/${groupCode}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`Kandidatgruppe ikke funnet: ${groupCode}`);
      }
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  }

  /**
   * Henter alle kandidatgruppekoder for en gitt eksamensperiode
   * Inkluderer også grupper uten fagkodeeksamen hvis de matcher perioden basert på gruppekode
   */
  private async getKandidatgrupperForPeriod(eksamensperiode: string): Promise<string[]> {
    const AppDataSource = await getDbConnection();
    const kandidatgruppeRepository = AppDataSource.getRepository(Kandidatgruppe);

    // Hent grupper med fagkodeeksamen (hovedkategori)
    const kandidatgrupperMedFagkode = await kandidatgruppeRepository
      .createQueryBuilder("k")
      .innerJoin("k.fagkodeeksamen", "f")
      .select("k.Kandidatgruppekode")
      .where("f.Eksamensperiode = :periode", { periode: eksamensperiode })
      .getRawMany();

    const groupCodesSet = new Set<string>(
      kandidatgrupperMedFagkode
        .map(row => row.k_Kandidatgruppekode)
        .filter(code => code !== null)
    );

    // Hent også grupper uten fagkodeeksamen som matcher perioden i gruppekoden
    // Gruppekode-format er typisk: SKOLEID_FAGKODE_PERIODE (f.eks. 12345_MAT1234_H-2025)
    const kandidatgrupperUtenFagkode = await kandidatgruppeRepository
      .createQueryBuilder("k")
      .select("k.Kandidatgruppekode")
      .where("k.FagkodeeksamensID IS NULL")
      .andWhere("k.Kandidatgruppekode LIKE :periode", { periode: `%${eksamensperiode}%` })
      .getRawMany();

    kandidatgrupperUtenFagkode.forEach(row => {
      const code = row.k_Kandidatgruppekode;
      if (code) {
        groupCodesSet.add(code);
      }
    });

    const groupCodes = Array.from(groupCodesSet);

    console.log(`Found ${groupCodes.length} kandidatgrupper for period ${eksamensperiode} (inkluderer grupper uten fagkodeeksamen)`);
    return groupCodes;
  }

  /**
   * Finner KandidatgruppeID basert på kandidatgruppekode
   */
  private async findKandidatgruppeId(kandidatgruppekode: string): Promise<number | null> {
    const AppDataSource = await getDbConnection();
    const kandidatgruppeRepository = AppDataSource.getRepository(Kandidatgruppe);

    const kandidatgruppe = await kandidatgruppeRepository.findOne({
      where: { Kandidatgruppekode: kandidatgruppekode },
      select: ['KandidatgruppeID']
    });

    return kandidatgruppe?.KandidatgruppeID || null;
  }

  /**
   * Upsert persons (insert or update based on Fodselsnummer)
   */
  private async upsertPersons(personRepository: any, persons: any[]): Promise<void> {
    if (persons.length === 0) return;

    console.log(`Upserting ${persons.length} persons`);

    const batchSize = 100;
    for (let i = 0; i < persons.length; i += batchSize) {
      const batch = persons.slice(i, i + batchSize);

      // Bygg MERGE med multiple source rows
      const sourceRows = batch.map(p =>
        `SELECT '${p.Fodselsnummer}' AS Fodselsnummer, '${p.Fornavn}' AS Fornavn, '${p.Etternavn}' AS Etternavn`
      ).join(' UNION ALL ');

      const query = `
        MERGE INTO Person AS target
        USING (${sourceRows}) AS source
        ON (target.Fodselsnummer = source.Fodselsnummer)
        WHEN MATCHED THEN
          UPDATE SET Fornavn = source.Fornavn, Etternavn = source.Etternavn
        WHEN NOT MATCHED THEN
          INSERT (Fodselsnummer, Fornavn, Etternavn)
          VALUES (source.Fodselsnummer, source.Fornavn, source.Etternavn);
      `;

      await personRepository.query(query);
    }
  }

  /**
   * Lagrer kandidateksamen til database
   */
  private async saveKandidateksamen(
    examGroupData: ExamGroupApiResponse
  ): Promise<{ success: number; errors: number; deleted: number; errorDetails: string[] }> {
    const AppDataSource = await getDbConnection();
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let successCount = 0;
    let errorCount = 0;
    let deletedCount = 0;
    const errorDetails: string[] = [];

    try {
      const kandidateksamenRepository = queryRunner.manager.getRepository(Kandidateksamen);
      const personRepository = queryRunner.manager.getRepository(Person);

      // Finn KandidatgruppeID
      const kandidatgruppeId = await this.findKandidatgruppeId(examGroupData.groupCode);

      console.log(`KandidatgruppeID for ${examGroupData.groupCode}: ${kandidatgruppeId} (type: ${typeof kandidatgruppeId})`);

      if (!kandidatgruppeId) {
        throw new Error(`Kunne ikke finne KandidatgruppeID for gruppe: ${examGroupData.groupCode}`);
      }

      // FØRST: Importer Person-data
      console.log(`Importing ${examGroupData.examGroupDetailList.length} persons for group ${examGroupData.groupCode}`);
      const personsToInsert: any[] = [];

      for (const candidate of examGroupData.examGroupDetailList) {
        if (candidate.sosialSecurityNumber && candidate.candidateFirstname && candidate.candidateSurname) {
          // Trim alle felter for å unngå whitespace-problemer
          const fodselsnummer = candidate.sosialSecurityNumber.trim();

          personsToInsert.push({
            Fodselsnummer: fodselsnummer,
            Fornavn: candidate.candidateFirstname.trim().substring(0, 255),
            Etternavn: candidate.candidateSurname.trim().substring(0, 255)
          });
        }
      }

      if (personsToInsert.length > 0) {
        await this.upsertPersons(personRepository, personsToInsert);
      }

      // Få alle eksisterende kandidateksamen for denne kandidatgruppen
      const existingKandidateksamen = await kandidateksamenRepository
        .createQueryBuilder("k")
        .where("k.KandidatgruppeID = :gruppeId", { gruppeId: kandidatgruppeId })
        .getMany();

      console.log(`Found ${existingKandidateksamen.length} existing kandidateksamen for group ${examGroupData.groupCode}`);

      // Opprett lookup map for eksisterende kandidateksamen
      const existingKandidateksamenMap = new Map<string, Kandidateksamen>();
      existingKandidateksamen.forEach(k => {
        existingKandidateksamenMap.set(k.KandidatpameldingsID, k);
      });

      // Samle alle kandidatpameldingsID fra import
      const importedKandidatpameldingsIds = new Set<string>();

      // Arrays for bulk operasjoner
      const kandidateksamenToInsert: Kandidateksamen[] = [];
      const kandidateksamenToUpdate: Kandidateksamen[] = [];

      // Prosesser hver kandidat
      for (const candidate of examGroupData.examGroupDetailList) {
        try {
          const kandidatpameldingsId = candidate.candidateRegistrationId.toString();
          importedKandidatpameldingsIds.add(kandidatpameldingsId);

          const existing = existingKandidateksamenMap.get(kandidatpameldingsId);

          // Trim fødselsnummer
          const fodselsnummer = candidate.sosialSecurityNumber.trim();

          // Konverter attendanceStatus til string
          const oppmotstatus = candidate.attendanceStatus?.toString() || undefined;

          // Validering: KandidatgruppeID må være gyldig
          if (!kandidatgruppeId || kandidatgruppeId === null || kandidatgruppeId === undefined) {
            const errorMsg = `Ugyldig KandidatgruppeID (${kandidatgruppeId}) for kandidat ${candidate.candidateNumber} i gruppe ${examGroupData.groupCode}`;
            console.error(errorMsg);
            errorDetails.push(errorMsg);
            errorCount++;
            continue;
          }

          if (existing) {
            // Oppdater eksisterende
            existing.Fodselsnummer = fodselsnummer;
            existing.Kandidatnummer = candidate.candidateNumber?.trim() || undefined;
            existing.Oppmotstatus = oppmotstatus;
            existing.KandidatgruppeID = kandidatgruppeId;
            kandidateksamenToUpdate.push(existing);
          } else {
            // Opprett ny - Sørg for at alle felter er gyldige
            if (!fodselsnummer || !kandidatpameldingsId) {
              const errorMsg = `Mangler påkrevde felter for kandidat ${candidate.candidateNumber}`;
              console.error(errorMsg);
              errorDetails.push(errorMsg);
              errorCount++;
              continue;
            }

            // Opprett plain object for å unngå TypeORM metadata-problemer
            const newKandidateksamen = {
              KandidatpameldingsID: kandidatpameldingsId,
              Fodselsnummer: fodselsnummer,
              Kandidatnummer: candidate.candidateNumber?.trim() || undefined,
              Malform: undefined,
              Oppmotstatus: oppmotstatus,
              KandidatgruppeID: kandidatgruppeId
            };

            console.log(`Creating kandidateksamen:`, JSON.stringify(newKandidateksamen));
            kandidateksamenToInsert.push(newKandidateksamen as any);
          }

          successCount++;
        } catch (error) {
          const errorMsg = `Feil ved prosessering av kandidat ${candidate.candidateNumber}: ${error instanceof Error ? error.message : String(error)}`;
          console.error(errorMsg, error);
          errorDetails.push(errorMsg);
          errorCount++;
        }
      }

      // Identifiser kandidateksamen som skal slettes
      const kandidateksamenToDelete: Kandidateksamen[] = [];
      existingKandidateksamenMap.forEach((kandidateksamen, id) => {
        if (!importedKandidatpameldingsIds.has(id)) {
          kandidateksamenToDelete.push(kandidateksamen);
        }
      });

      // Utfør bulk operasjoner
      if (kandidateksamenToDelete.length > 0) {
        console.log(`Deleting ${kandidateksamenToDelete.length} kandidateksamen`);
        await this.deleteBatched(kandidateksamenRepository, kandidateksamenToDelete);
        deletedCount = kandidateksamenToDelete.length;
      }

      if (kandidateksamenToInsert.length > 0) {
        console.log(`Inserting ${kandidateksamenToInsert.length} new kandidateksamen`);
        await this.saveBatched(kandidateksamenRepository, kandidateksamenToInsert, "insert");
      }

      if (kandidateksamenToUpdate.length > 0) {
        console.log(`Updating ${kandidateksamenToUpdate.length} kandidateksamen`);
        await this.saveBatched(kandidateksamenRepository, kandidateksamenToUpdate, "update");
      }

      await queryRunner.commitTransaction();
      console.log(`Kandidateksamen processed: ${successCount} success, ${errorCount} errors, ${deletedCount} deleted`);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error in kandidateksamen save operation:", error);
      throw error;
    } finally {
      await queryRunner.release();
    }

    return { success: successCount, errors: errorCount, deleted: deletedCount, errorDetails };
  }

  /**
   * Batch save method to avoid SQL Server parameter limit
   */
  private async saveBatched<T>(
    repository: any,
    entities: T[],
    operation: "insert" | "update",
    batchSize: number = 100
  ): Promise<void> {
    if (entities.length === 0) return;

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      try {
        if (operation === "insert") {
          // Bruk raw INSERT for å unngå type-konvertering problemer
          const values = batch.map((entity: any) => {
            return `('${entity.KandidatpameldingsID}', '${entity.Fodselsnummer}', ${entity.Kandidatnummer ? `'${entity.Kandidatnummer}'` : 'NULL'}, ${entity.Malform ? `'${entity.Malform}'` : 'NULL'}, ${entity.Oppmotstatus ? `'${entity.Oppmotstatus}'` : 'NULL'}, GETDATE(), GETDATE(), ${entity.KandidatgruppeID})`;
          }).join(', ');

          const query = `
            INSERT INTO Kandidateksamen
            (KandidatpameldingsID, Fodselsnummer, Kandidatnummer, Malform, Oppmotstatus, CreatedDate, ModifiedDate, KandidatgruppeID)
            VALUES ${values}
          `;

          await repository.query(query);
        } else {
          // For updates, bruk MERGE for batched upsert
          const sourceRows = batch.map((e: any) => {
            return `SELECT
              ${e.KandidateksamenID} AS KandidateksamenID,
              '${e.KandidatpameldingsID}' AS KandidatpameldingsID,
              '${e.Fodselsnummer}' AS Fodselsnummer,
              ${e.Kandidatnummer ? `'${e.Kandidatnummer}'` : 'NULL'} AS Kandidatnummer,
              ${e.Malform ? `'${e.Malform}'` : 'NULL'} AS Malform,
              ${e.Oppmotstatus ? `'${e.Oppmotstatus}'` : 'NULL'} AS Oppmotstatus,
              ${e.KandidatgruppeID} AS KandidatgruppeID`;
          }).join(' UNION ALL ');

          const query = `
            MERGE INTO Kandidateksamen AS target
            USING (${sourceRows}) AS source
            ON (target.KandidateksamenID = source.KandidateksamenID)
            WHEN MATCHED THEN
              UPDATE SET
                KandidatpameldingsID = source.KandidatpameldingsID,
                Fodselsnummer = source.Fodselsnummer,
                Kandidatnummer = source.Kandidatnummer,
                Malform = source.Malform,
                Oppmotstatus = source.Oppmotstatus,
                ModifiedDate = GETDATE(),
                KandidatgruppeID = source.KandidatgruppeID;
          `;

          await repository.query(query);
        }
      } catch (error) {
        console.error(`Error in batch ${Math.floor(i / batchSize) + 1}:`, error);
        throw error;
      }
    }
  }

  /**
   * Batch delete method
   */
  private async deleteBatched<T>(
    repository: any,
    entities: T[],
    batchSize: number = 100
  ): Promise<void> {
    if (entities.length === 0) return;

    const primaryKey = repository.metadata.primaryColumns[0].propertyName;

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      const ids = batch.map((entity: any) => entity[primaryKey]);

      try {
        await repository
          .createQueryBuilder()
          .delete()
          .where(`${primaryKey} IN (:...ids)`, { ids })
          .execute();
      } catch (error) {
        console.error(`Error in delete batch ${Math.floor(i / batchSize) + 1}:`, error);
        throw error;
      }
    }
  }

  /**
   * Hovedmetode for å importere kandidateksamen for en eksamensperiode
   */
  async importKandidateksamen(
    eksamensperiode: string
  ): Promise<{ success: number; errors: number; deleted: number; message: string; errorDetails?: string[] }> {
    const startTime = Date.now();

    try {
      console.log(`Starting kandidateksamen import for period: ${eksamensperiode}`);

      // Hent alle kandidatgruppekoder for perioden
      const groupCodes = await this.getKandidatgrupperForPeriod(eksamensperiode);

      if (groupCodes.length === 0) {
        return {
          success: 0,
          errors: 0,
          deleted: 0,
          message: `Ingen kandidatgrupper funnet for periode ${eksamensperiode}`,
        };
      }

      console.log(`Processing ${groupCodes.length} kandidatgrupper`);

      let totalSuccess = 0;
      let totalErrors = 0;
      let totalDeleted = 0;
      const allErrorDetails: string[] = [];

      // Prosesser hver kandidatgruppe
      for (const groupCode of groupCodes) {
        try {
          console.log(`Processing kandidatgruppe: ${groupCode}`);
          const examGroupData = await this.fetchKandidateksamenForGroup(groupCode);
          const result = await this.saveKandidateksamen(examGroupData);

          totalSuccess += result.success;
          totalErrors += result.errors;
          totalDeleted += result.deleted;
          allErrorDetails.push(...result.errorDetails);
        } catch (error) {
          const errorMsg = `Feil ved prosessering av gruppe ${groupCode}: ${error instanceof Error ? error.message : String(error)}`;
          console.warn(errorMsg);
          allErrorDetails.push(errorMsg);
          totalErrors++;
        }
      }

      const duration = Date.now() - startTime;
      const successMessage = `Import fullført på ${duration}ms. ${totalSuccess} vellykket, ${totalErrors} feil, ${totalDeleted} slettet.`;

      return {
        success: totalSuccess,
        errors: totalErrors,
        deleted: totalDeleted,
        message: successMessage,
        errorDetails: allErrorDetails.length > 0 ? allErrorDetails : undefined,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Import feilet etter ${duration}ms: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`;

      console.error("Error during kandidateksamen import:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        eksamensperiode,
        duration,
      });

      throw new Error(errorMessage);
    }
  }
}
