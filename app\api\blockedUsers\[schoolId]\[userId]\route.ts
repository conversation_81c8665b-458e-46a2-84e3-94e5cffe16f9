// app/api/schools/[schoolId]/blocked-users/route.ts
import { authOptions } from "@/app/api/auth/authOptions";
import { addToSet, removeFromSet } from "@/app/lib/redisHelper";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { sendAuthorizationRestoredMessage } from "@/lib/server/signalRHelpers";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

// Hjelpefunksjon for å generere Redis-nøkkel
const getRedisKey = (schoolId: string) => `BlockedUsers:${schoolId}`;

const telemetryClient = getAppInsightsServer();

// POST - Blokker en bruker
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ schoolId: string; userId: string }> }
) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { schoolId, userId } = await params;

    if (!userId || !schoolId) {
      return NextResponse.json(
        { error: "userId og schoolId er påkrevd" },
        { status: 400 }
      );
    }

    const key = getRedisKey(schoolId);
    const result = await addToSet(key, userId);

    return NextResponse.json(
      {
        message:
          result === 1 ? "Bruker blokkert" : "Bruker var allerede blokkert",
        schoolId,
        userId,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Feil ved blokkering av bruker:", error);
    return NextResponse.json({ error: "Intern serverfeil" }, { status: 500 });
  }
}

// DELETE - Fjern en blokkert bruker
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ schoolId: string; userId: string }> }
) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { schoolId, userId } = await params;

    if (!userId || !schoolId) {
      return NextResponse.json(
        { error: "userId og schoolId er påkrevd" },
        { status: 400 }
      );
    }

    // Get candidate number and session ID from request body
    let candidateNumber: string | undefined;
    let sessionId: string | undefined;
    try {
      const body = await request.json();
      candidateNumber = body.candidateNumber;
      sessionId = body.sessionId;
    } catch (error) {
      // Body parsing failed, continue without SignalR message
      console.warn("Could not parse request body for candidate number and session ID:", error);
    }

    const key = getRedisKey(schoolId);
    const removed = await removeFromSet(key, userId);

    // Send SignalR message to inform candidate that access has been restored
    if (candidateNumber && removed > 0) {
      try {
        await sendAuthorizationRestoredMessage(candidateNumber, sessionId);
      } catch (signalRError) {
        // Log SignalR error but continue flow
        telemetryClient?.trackException({
          exception: signalRError as Error,
          properties: {
            operation: "SendAuthorizationRestoredMessage",
            candidateNumber,
            userId,
            schoolId,
            sessionId,
            errorMessage: (signalRError as Error).message,
          },
        });
        console.error("Failed to send SignalR message for unblock:", signalRError);
      }
    }

    return NextResponse.json(
      {
        message: removed === 0 ? "Bruker var ikke blokkert" : "Bruker fjernet",
        schoolId,
        userId,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Feil ved fjerning av blokkert bruker:", error);
    return NextResponse.json({ error: "Intern serverfeil" }, { status: 500 });
  }
}
