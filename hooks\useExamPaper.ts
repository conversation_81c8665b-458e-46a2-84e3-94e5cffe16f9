"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import useS<PERSON> from "swr";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { IExamPaperPayload } from "@/interface/IExamPaperPayload";
import { ICandidate } from "@/interface/ICandidate";
import { toast } from "@/components/ui/use-toast";

interface UseExamPaperOptions {
  schoolId?: string;
  period?: string;
  fromSessionStorage?: boolean;
  sessionStorageKey?: string;
}

interface ExamPaperSummary {
  totalCandidates: number;
  deliveredCandidates: number;
  totalFiles: number;
  totalSize: number;
}

interface DownloadState {
  downloading: boolean;
  progress: number;
  missingFiles: string[];
  noFilesFound: boolean;
}

interface UseExamPaperReturn {
  data: IExamPaperInternal[] | IExamPaperInternal | null;
  isLoading: boolean;
  error: string | null;
  summary: ExamPaperSummary;
  refetch: () => void;
  download: {
    state: DownloadState;
    downloadGroup: (examPaper: IExamPaperInternal) => Promise<void>;
    downloadCandidate: (
      candidate: ICandidate,
      subjectCode: string,
      subjectName: string,
      testPeriod: string
    ) => Promise<void>;
  };
}

const fetcher = (url: string, body: IExamPaperPayload) =>
  fetch(url, {
    method: "POST",
    body: JSON.stringify(body),
    headers: { "Content-Type": "application/json" },
  }).then((res) => {
    if (!res.ok) throw new Error(res.statusText);
    return res.json();
  });

// Helper functions for file downloads
function getFilenameFromHeaders(headers: Headers): string {
  const contentDisposition = headers.get("Content-Disposition");
  const filenameMatch =
    contentDisposition && contentDisposition.match(/filename="?(.+?)_?"?$/i);
  return filenameMatch ? decodeURIComponent(filenameMatch[1]) : "filer.zip";
}

async function streamToBlob(
  readableStream: ReadableStream<Uint8Array> | null,
  totalSize: number,
  onProgress: (downloadedBytes: number) => void
): Promise<Blob> {
  if (!readableStream) {
    throw new Error("No readable stream available");
  }
  const reader = readableStream.getReader();
  const chunks: Uint8Array[] = [];
  let downloadedBytes = 0;
  let lastUpdateTime = Date.now();

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    chunks.push(value);
    downloadedBytes += value.length;

    // Update progress at most every 0.5 seconds
    const currentTime = Date.now();
    if (currentTime - lastUpdateTime > 500) {
      onProgress(Math.min(downloadedBytes, totalSize));
      lastUpdateTime = currentTime;
    }
  }

  // Final update with total downloaded size
  onProgress(Math.min(downloadedBytes, totalSize));

  // TypeScript has issues with Uint8Array<ArrayBufferLike> vs BlobPart, so we cast to any
  return new Blob(chunks as any, { type: "application/zip" });
}

function downloadBlob(blob: Blob, filename: string): void {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.style.display = "none";
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
}

export function useExamPaper(options: UseExamPaperOptions = {}): UseExamPaperReturn {
  const {
    schoolId,
    period,
    fromSessionStorage = false,
    sessionStorageKey = "examGroupData",
  } = options;

  const [data, setData] = useState<IExamPaperInternal[] | IExamPaperInternal | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchTrigger, setSearchTrigger] = useState(0);

  // Download state
  const [downloadState, setDownloadState] = useState<DownloadState>({
    downloading: false,
    progress: 0,
    missingFiles: [],
    noFilesFound: false,
  });

  const shouldFetch = !fromSessionStorage && !!schoolId && !!period;

  // SWR for API data fetching
  const {
    data: swrData,
    error: swrError,
    isLoading: swrLoading,
    mutate,
  } = useSWR(
    shouldFetch
      ? [
          `/api/getExamPaper`,
          period,
          schoolId,
          searchTrigger,
        ]
      : null,
    ([url, period, schoolId]) =>
      fetcher(url, { period, schoolId } as IExamPaperPayload),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      shouldRetryOnError: false,
      errorRetryCount: 0,
    }
  );

  // Handle SWR data
  useEffect(() => {
    if (swrData) {
      setData(swrData.length === 0 ? [] : swrData);
      setError(null);
    }
  }, [swrData]);

  // Handle SWR errors
  useEffect(() => {
    if (swrError) {
      console.error("Feil ved henting av eksamenspapirer:", swrError);
      setError(
        "Det oppstod en uventet feil ved henting av kandidatgrupper. Vennligst prøv igjen senere."
      );
      setData(null);
    }
  }, [swrError]);

  // Handle sessionStorage data
  useEffect(() => {
    if (fromSessionStorage) {
      const storedData = sessionStorage.getItem(sessionStorageKey);
      if (storedData) {
        try {
          const storedDataJson: IExamPaperInternal = JSON.parse(storedData);
          setData(storedDataJson);
          setError(null);
        } catch (err) {
          console.error("Error parsing sessionStorage data:", err);
          setError("Feil ved lasting av data fra session storage");
        }
      }
    }
  }, [fromSessionStorage, sessionStorageKey]);

  // Calculate summary statistics
  const summary = useMemo((): ExamPaperSummary => {
    let totalCandidates = 0;
    let deliveredCandidates = 0;
    let totalFiles = 0;
    let totalSize = 0;

    if (!data) {
      return { totalCandidates: 0, deliveredCandidates: 0, totalFiles: 0, totalSize: 0 };
    }

    const deliveredCandidateIds = new Set<string>();

    // Handle both array of exam papers (main page) and single exam paper (slug page)
    const examPapers = Array.isArray(data) ? data : [data];

    examPapers.forEach((examPaper) => {
      if (examPaper?.candidates) {
        totalCandidates += examPaper.candidates.length;
        
        examPaper.candidates.forEach((candidate) => {
          if (candidate.documents.length > 0) {
            deliveredCandidateIds.add(candidate.candidateNumber);
          }

          totalFiles += candidate.documents.length;

          // Calculate total size (mainly for download functionality)
          candidate.documents.forEach((doc) => {
            const fileSizeInBytes = parseInt(doc.fileSize, 10);
            totalSize += isNaN(fileSizeInBytes) ? 0 : fileSizeInBytes;
          });
        });
      }
    });

    deliveredCandidates = deliveredCandidateIds.size;

    return { totalCandidates, deliveredCandidates, totalFiles, totalSize };
  }, [data]);

  // Download group submissions
  const downloadGroup = useCallback(async (examPaper: IExamPaperInternal) => {
    setDownloadState(prev => ({ 
      ...prev, 
      downloading: true, 
      progress: 0, 
      missingFiles: [], 
      noFilesFound: false 
    }));

    try {
      const response = await fetch(`/api/downloadGroupSubmissions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(examPaper),
      });

      if (!response.ok) {
        if (response.status === 404) {
          toast({
            variant: "destructive",
            title: "Ingen filer lastet ned",
            description: "Ingen filer ble funnet for nedlasting. Prøv igjen senere.",
          });
          setDownloadState(prev => ({ ...prev, noFilesFound: true, downloading: false }));
          return;
        }
        throw new Error(`HTTP-feil! Status: ${response.status}`);
      }

      const contentType = response.headers.get("Content-Type");
      if (contentType !== "application/octet-stream") {
        throw new Error(`Uventet innholdstype: ${contentType}`);
      }

      const filename = getFilenameFromHeaders(response.headers);
      const missingFilesHeader = response.headers.get("X-Missing-Files");
      if (missingFilesHeader) {
        const newMissingFiles = JSON.parse(missingFilesHeader);
        setDownloadState(prev => ({ ...prev, missingFiles: newMissingFiles }));
      }

      const blob = await streamToBlob(
        response.body,
        summary.totalSize,
        (downloadedBytes) => {
          setDownloadState(prev => ({ ...prev, progress: downloadedBytes }));
        }
      );

      downloadBlob(blob, filename);

      setTimeout(() => {
        setDownloadState(prev => ({ ...prev, downloading: false, progress: 0 }));
      }, 1000);
    } catch (error) {
      console.error("Feil ved nedlasting av alle besvarelser", error);
      toast({
        variant: "destructive",
        title: "Feil ved nedlasting",
        description: "Det oppstod en feil under nedlastingen av besvarelser.",
      });
      setDownloadState(prev => ({ ...prev, downloading: false }));
    }
  }, [summary.totalSize]);

  // Download candidate submissions
  const downloadCandidate = useCallback(async (
    candidate: ICandidate,
    subjectCode: string,
    subjectName: string,
    testPeriod: string
  ) => {
    const candidateTotalSize = candidate.documents.reduce(
      (sum, doc) => sum + parseInt(doc.fileSize, 10),
      0
    );

    setDownloadState(prev => ({ 
      ...prev, 
      downloading: true, 
      progress: 0, 
      missingFiles: [], 
      noFilesFound: false 
    }));

    try {
      const requestBody = {
        documents: candidate.documents,
        candidateNumber: candidate.candidateNumber,
        subjectCode,
        subjectName,
        testPeriod,
      };

      const response = await fetch("/api/downloadUserSubmissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        if (response.status === 404) {
          const data = await response.json();
          setDownloadState(prev => ({ 
            ...prev, 
            missingFiles: data.missingfiles || [], 
            downloading: false 
          }));
          toast({
            variant: "destructive",
            title: "Ingen filer funnet",
            description: "Ingen filer ble funnet for nedlasting.",
          });
          return;
        }
        throw new Error(`HTTP-feil! Status: ${response.status}`);
      }

      const contentType = response.headers.get("Content-Type");
      if (contentType !== "application/octet-stream") {
        throw new Error(`Uventet innholdstype: ${contentType}`);
      }

      const filename = getFilenameFromHeaders(response.headers);
      const missingFilesHeader = response.headers.get("X-Missing-Files");
      if (missingFilesHeader) {
        const newMissingFiles = JSON.parse(missingFilesHeader);
        setDownloadState(prev => ({ ...prev, missingFiles: newMissingFiles }));
      }

      const blob = await streamToBlob(
        response.body,
        candidateTotalSize,
        (downloadedBytes) => {
          setDownloadState(prev => ({ ...prev, progress: downloadedBytes }));
        }
      );

      downloadBlob(blob, filename);

      setTimeout(() => {
        setDownloadState(prev => ({ ...prev, downloading: false, progress: 0 }));
      }, 1000);
    } catch (error) {
      console.error("Feil ved nedlasting av filer:", error);
      toast({
        variant: "destructive",
        title: "Feil ved nedlasting",
        description: "En uventet feil oppstod ved nedlasting av filer, prøv igjen senere.",
      });
      setDownloadState(prev => ({ ...prev, downloading: false }));
    }
  }, []);

  // Show missing files toast when they are detected
  useEffect(() => {
    if (downloadState.missingFiles.length > 0) {
      const fileList = downloadState.missingFiles.slice(0, 15).join(', ');
      const additionalCount = downloadState.missingFiles.length - 15;
      const description = additionalCount > 0 
        ? `${fileList} ... og ${additionalCount} filer til`
        : fileList;
        
      toast({
        variant: "destructive",
        title: "Kunne ikke laste ned følgende filer:",
        description,
      });
    }
  }, [downloadState.missingFiles]);

  // Refetch function
  const refetch = useCallback(() => {
    if (shouldFetch) {
      setError(null);
      setSearchTrigger((prev) => prev + 1);
      mutate();
    }
  }, [shouldFetch, mutate]);

  return {
    data,
    isLoading: fromSessionStorage ? !data : swrLoading,
    error,
    summary,
    refetch,
    download: {
      state: downloadState,
      downloadGroup,
      downloadCandidate,
    },
  };
}
