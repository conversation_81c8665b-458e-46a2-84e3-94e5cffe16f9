import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../../auth/authOptions";
import { sanitizeString } from "@/lib/shared/validation";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import { generateAccessToken } from "@/lib/server/getAccessTokenForEksamen";

const telemetryClient = getAppInsightsServer();

// Helper function for external verification DELETE API call
async function deleteExternalVerification(documentGuid: string) {
  const externalApiUrl = process.env.PAS_VERIFICATION_API_URL;

  if (!externalApiUrl) {
    console.warn(
      "PAS_VERIFICATION_API_URL not configured, skipping external verification DELETE call"
    );
    return;
  }

  try {
    // Generate access token for PAS verification API
    const token = await generateAccessToken();

    const response = await fetch(`${externalApiUrl}/${documentGuid}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Ekstern verifisering DELETE-kall feilet med status: ${response.status}`
      );
    }
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "externalVerificationDelete",
        documentGuid,
        error: error instanceof Error ? error.message : "Unknown error",
      },
    });

    console.error(
      "External verification DELETE call failed for",
      documentGuid,
      ":",
      error
    );
    // Throw error to notify client of PAS verification DELETE failure
    throw new Error(
      `PAS verification DELETE failed: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

export const dynamic = "force-dynamic";

export async function DELETE(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const userRoles = Array.isArray(session.user.role)
      ? session.user.role
      : [session.user.role];

    const hasAdminRole = userRoles.some(
      (role) => role === "urn:udir:pgsa:administrator"
    );

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { documentGuid } = body;

    // Validate required field
    if (!documentGuid) {
      return NextResponse.json(
        {
          error: "Missing documentGuid for verification DELETE",
        },
        { status: 400 }
      );
    }

    // Send external verification DELETE call
    await deleteExternalVerification(documentGuid);

    return NextResponse.json({
      success: true,
      message: "Ekstern verifisering DELETE-kall var vellykket",
    });
  } catch (error) {
    console.error("Error in verification DELETE:", error);

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Intern serverfeil",
      },
      { status: 500 }
    );
  }
}
