"use client";

import { useState } from 'react';
import { FaDownload, FaTimes, FaSearch } from 'react-icons/fa';
import { Button } from '@/components/ui/button';

interface MissingFile {
  fagkode: string;
  variantkode?: string;
  fagnavn: string;
  eksamensdel: string;
  filename: string;
  blobName: string;
}

interface MissingFilesReportProps {
  missingFiles: MissingFile[];
  onClose: () => void;
}

export function MissingFilesReport({ missingFiles, onClose }: MissingFilesReportProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredFiles = missingFiles.filter(file =>
    file.fagkode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    file.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
    file.fagnavn.toLowerCase().includes(searchTerm.toLowerCase()) ||
    file.eksamensdel.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const downloadReport = () => {
    const csvContent = [
      'Fagkode,Variant,Fagnavn,Eksamensdel,Filnavn,Blob-referanse',
      ...missingFiles.map(file =>
        `"${file.fagkode}","${file.variantkode || ''}","${file.fagnavn}","${file.eksamensdel}","${file.filename}","${file.blobName}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `manglende-filer-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl max-h-[90vh] w-full mx-4 flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-red-700 flex items-center gap-2">
            <span>⚠️ Manglende filer</span>
            <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
              {missingFiles.length}
            </span>
          </h2>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              onClick={downloadReport}
              title="Last ned rapport som CSV"
            >
              <FaDownload className="h-4 w-4 mr-2" />
              Last ned CSV
            </Button>
            <Button
              variant="ghost"
              onClick={onClose}
              title="Lukk rapport"
            >
              <FaTimes className="h-4 w-4 mr-2" />
              Lukk
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="p-4 border-b bg-gray-50">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Søk etter fagkode, filnavn, fagnavn eller eksamensdel..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          {searchTerm && (
            <div className="mt-2 text-sm text-gray-600">
              Viser {filteredFiles.length} av {missingFiles.length} manglende filer
            </div>
          )}
        </div>

        {/* Table */}
        <div className="flex-1 overflow-auto">
          {filteredFiles.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-center text-gray-500">
                {searchTerm ? (
                  <>
                    <p>Ingen filer funnet som matcher søket</p>
                    <p className="text-sm mt-1">"{searchTerm}"</p>
                  </>
                ) : (
                  <p>Ingen manglende filer funnet</p>
                )}
              </div>
            </div>
          ) : (
            <table className="w-full text-sm">
              <thead className="bg-gray-50 sticky top-0 border-b">
                <tr>
                  <th className="px-4 py-3 text-left font-medium text-gray-700">Fagkode</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-700">Variant</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-700">Fagnavn</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-700">Eksamensdel</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-700">Filnavn</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-700">Blob-referanse</th>
                </tr>
              </thead>
              <tbody>
                {filteredFiles.map((file, index) => (
                  <tr
                    key={`${file.fagkode}-${file.blobName}-${index}`}
                    className="border-b hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-4 py-3 font-medium text-gray-900">
                      {file.fagkode}
                    </td>
                    <td className="px-4 py-3 text-gray-700">
                      {file.variantkode || '-'}
                    </td>
                    <td className="px-4 py-3 text-gray-700 max-w-xs">
                      <div className="truncate" title={file.fagnavn}>
                        {file.fagnavn}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-gray-700">
                      {file.eksamensdel}
                    </td>
                    <td className="px-4 py-3 text-gray-700 max-w-xs">
                      <div className="break-all" title={file.filename}>
                        {file.filename}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-xs text-gray-500 max-w-xs">
                      <div className="break-all font-mono" title={file.blobName}>
                        {file.blobName}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {/* Footer with summary */}
        <div className="p-4 border-t bg-gray-50 text-sm text-gray-600">
          <div className="flex justify-between items-center">
            <span>
              Total manglende filer: <strong>{missingFiles.length}</strong>
            </span>
            <span>
              Unik fagkoder påvirket: <strong>
                {new Set(missingFiles.map(f => f.fagkode)).size}
              </strong>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}