"use client";

import { useState, useEffect, useMemo } from "react";
import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  MdOutlineKeyboardArrowDown,
  MdOutlineKeyboardArrowUp,
} from "react-icons/md";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, Loader2, Bar<PERSON><PERSON>3, Info, CalendarIcon } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { format } from "date-fns";
import { nb } from "date-fns/locale";

interface StatisticsData {
  eksamensperiode: string;
  generell: {
    totaltAntallKandidater: number;
    totaltAntallGrupper: number;
    gjennomsnittKandidaterPerGruppe: number;
    kandidaterUtenGruppe: number;
    kandidaterIGrupperUtenFagkode: number;
    grupperUtenKandidater: number;
    antallUnikeSkoler: number;
    kandidaterDetaljer?: Array<{ kandidateksamenID: number; kandidatpameldingsID: string; fodselsnummer: string; kandidatnummer: string | null; fornavn: string | null; etternavn: string | null }>;
    grupperDetaljer?: Array<{ kandidatgruppeID: number; kandidatgruppekode: string; kandidatgruppenavn: string | null }>;
    kandidaterUtenGruppeDetaljer?: Array<{ kandidateksamenID: number; kandidatpameldingsID: string; fodselsnummer: string; kandidatnummer: string | null; fornavn: string | null; etternavn: string | null }>;
    grupperUtenKandidaterDetaljer?: Array<{ kandidatgruppeID: number; kandidatgruppekode: string; kandidatgruppenavn: string | null }>;
    skolerDetaljer?: Array<{ skoleID: string; antallKandidater: number; antallGrupper: number }>;
  };
  perSkole: Array<{
    skoleID: string;
    antallKandidater: number;
    antallGrupper: number;
  }>;
  perFagkode: Array<{
    fagkode: string;
    fagnavn: string;
    antallKandidater: number;
    antallGrupper: number;
    eksamensdato: string | null;
  }>;
  tidsmessig: {
    nyesteOpprettetDato: string | null;
    eldsteOpprettetDato: string | null;
    opprettelserPerDag: Array<{
      dato: string;
      antallKandidater: number;
      antallGrupper: number;
    }>;
  };
}

interface BesvarelseStatisticsData {
  eksamensperiode: string;
  innleveringsoversikt: {
    totalBesvarelsesdeler: number;
    totalLeverteBesvarelsesdeler: number;
    totalKandidaterMedLevering: number;
    totalKandidater: number;
    totaltForventetKandidater: number;
    gjennomforingsgradProsent: number;
    perFagkode: Array<{
      fagkode: string;
      fagkodenavn: string;
      antallBesvarelsesdeler: number;
      antallLeverteBesvarelsesdeler: number;
      antallKandidaterMedLevering: number;
      antallKandidater: number;
      forventetAntallKandidater: number;
      gjennomforingsgradProsent: number;
    }>;
    statusOversikt: Array<{
      status: string;
      antallKandidater: number;
      prosent: number;
    }>;
    innleveringerPerTime: Array<{
      time: string;
      antall: number;
      prosent: number;
    }>;
  };
  filstatistikk: {
    totaltAntallFiler: number;
    totalFilstorrelse: string;
    totalFilstorrelseMB: number;
    gjennomsnittligAntallFilerPerKandidat: number;
    gjennomsnittligFilstorrelse: string;
    gjennomsnittligFilstorrelseMB: number;
    filtyper: Array<{
      filtype: string;
      antall: number;
      prosent: number;
    }>;
    filstorrelseDistribusjon: Array<{
      kategori: string;
      antall: number;
      prosent: number;
    }>;
  };
}

export default function KandidatStatistikkPage() {
  const [eksamensperiode, setEksamensperiode] = useState("");
  const [fraDato, setFraDato] = useState<Date | undefined>();
  const [tilDato, setTilDato] = useState<Date | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [rawStatisticsData, setRawStatisticsData] = useState<StatisticsData | null>(null);
  const [besvarelseData, setBesvarelseData] = useState<BesvarelseStatisticsData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [availableEksamensperioder, setAvailableEksamensperioder] = useState<string[]>([]);
  const [showDateFilter, setShowDateFilter] = useState(false);

  useEffect(() => {
    const generateEksamensperioder = () => {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;

      const currentPeriod = currentMonth >= 7
        ? `H-${currentYear}`
        : `V-${currentYear}`;

      const periods: string[] = [];
      const startYear = currentYear - 3;
      const endYear = currentYear + 1;

      for (let year = startYear; year <= endYear; year++) {
        periods.push(`V-${year}`);
        periods.push(`H-${year}`);
      }

      return { periods, currentPeriod };
    };
    const { periods, currentPeriod } = generateEksamensperioder();
    setAvailableEksamensperioder(periods);
    setEksamensperiode(currentPeriod);
  }, []);

  const handleFetchStatistics = async () => {
    if (!eksamensperiode) {
      setError("Vennligst velg en eksamensperiode");
      return;
    }

    setIsLoading(true);
    setError(null);
    setRawStatisticsData(null);
    setBesvarelseData(null);

    try {
      const queryParams = new URLSearchParams();
      queryParams.append("eksamensperiode", eksamensperiode);
      if (fraDato) queryParams.append("fraDato", fraDato.toISOString());
      if (tilDato) queryParams.append("tilDato", tilDato.toISOString());

      // Fetch both kandidat and besvarelse statistics in parallel
      const [kandidatResponse, besvarelseResponse] = await Promise.all([
        fetch(`/api/admin/kandidat-statistics?${queryParams.toString()}`),
        fetch(`/api/admin/besvarelse-statistics?${queryParams.toString()}`),
      ]);

      const [kandidatData, besvarelseDataResult] = await Promise.all([
        kandidatResponse.json(),
        besvarelseResponse.json(),
      ]);

      if (kandidatResponse.ok) {
        setRawStatisticsData(kandidatData);
      } else {
        setError(kandidatData.error || "En feil oppstod under henting av kandidatstatistikk");
      }

      if (besvarelseResponse.ok) {
        setBesvarelseData(besvarelseDataResult);
      } else {
        console.error("Kunne ikke hente besvarelsesstatistikk:", besvarelseDataResult.error);
        // Don't set error here, just log it - besvarelse data is optional
      }
    } catch (err) {
      setError("Kunne ikke koble til serveren");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (eksamensperiode) {
      handleFetchStatistics();
    }
  }, [eksamensperiode, fraDato, tilDato]);

  return (
    <>
      <div className="py-6 bg-header">
        <div className="container-wrapper-lg flex flex-col gap-3">
          <div>
            <h1 className="text-4xl">Statistikk</h1>
            <p className="mt-4">
              Vis statistikk for kandidater og grupper per eksamensperiode
            </p>
          </div>
        </div>
      </div>

      <div className="container-wrapper-lg mb-8 mt-4">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Filtre
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="eksamensperiode">Eksamensperiode</Label>
                  <Select
                    value={eksamensperiode}
                    onValueChange={setEksamensperiode}
                    disabled={isLoading}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Velg periode" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableEksamensperioder.map((periode) => (
                        <SelectItem key={periode} value={periode}>
                          {periode} ({periode.startsWith("H") ? "Høst" : "Vår"} {periode.split("-")[1]})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Eksamensdato</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="justify-start text-left font-normal h-10 text-sm w-full"
                        disabled={isLoading}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4 shrink-0" />
                        <span className="truncate">{fraDato ? format(fraDato, "d. MMM yyyy", { locale: nb }) : "Fra dato"}</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={fraDato}
                        onSelect={setFraDato}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="justify-start text-left font-normal h-10 text-sm w-full"
                        disabled={isLoading}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4 shrink-0" />
                        <span className="truncate">{tilDato ? format(tilDato, "d. MMM yyyy", { locale: nb }) : "Til dato"}</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={tilDato}
                        onSelect={setTilDato}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <Button
                    onClick={handleFetchStatistics}
                    disabled={isLoading || !eksamensperiode}
                    className="flex items-center gap-2 h-10 w-full"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Henter statistikk...
                      </>
                    ) : (
                      <>
                        <BarChart3 className="w-4 h-4" />
                        Hent statistikk
                      </>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    className="flex items-center gap-1 h-10 w-full"
                    onClick={() => {
                      setFraDato(undefined);
                      setTilDato(undefined);
                    }}
                    disabled={isLoading}
                  >
                    Nullstill datofilter
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="lg:col-span-3 space-y-6">


          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <AlertTitle className="text-red-800">Feil</AlertTitle>
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {rawStatisticsData && (
            <Tabs defaultValue="kandidater" className="w-full">
              <TabsList>
                <TabsTrigger value="kandidater">Kandidater og grupper</TabsTrigger>
                <TabsTrigger value="innleveringer">Innleveringer</TabsTrigger>
                <TabsTrigger value="filer">Filstatistikk</TabsTrigger>
              </TabsList>

              <TabsContent value="kandidater" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      Generell statistikk
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <Info className="h-4 w-4 text-gray-500" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80">
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Forklaring</h4>
                            <div className="text-sm space-y-1">
                              <p><strong>Totalt antall kandidater:</strong> Alle kandidater i valgt periode (inkluderer også de i grupper uten fagkodeeksamen)</p>
                              <p><strong>Totalt antall grupper:</strong> Alle kandidatgrupper i perioden</p>
                              <p><strong>Snitt per gruppe:</strong> Gjennomsnittlig antall kandidater per gruppe</p>
                              <p><strong>Antall skoler med kandidater:</strong> Antall unike skoler som har kandidater i perioden</p>
                              <p><strong>Kandidater uten gruppe:</strong> Kandidater som ikke er tilknyttet en gruppe</p>
                              <p><strong>Kandidater i grupper uten fagkodeeksamen:</strong> Kandidater i grupper som mangler kobling til fagkodeeksamen</p>
                              <p><strong>Grupper uten kandidater:</strong> Grupper som ikke har noen registrerte kandidater</p>
                              <p className="text-xs text-gray-500 mt-2">Klikk på hver boks for å se detaljer</p>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </CardTitle>
                    <CardDescription>Oversikt for {rawStatisticsData.eksamensperiode}</CardDescription>
                  </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <Popover>
                      <PopoverTrigger asChild>
                        <div className="p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            Totalt antall kandidater
                            <Info className="h-3 w-3" />
                          </p>
                          <p className="text-3xl font-bold text-blue-600">{rawStatisticsData.generell.totaltAntallKandidater}</p>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-[1000px] max-h-96 overflow-auto">
                        <h4 className="font-medium text-sm mb-2">Kandidater</h4>
                        <div className="max-h-64 overflow-y-auto">
                          <table className="w-full text-xs">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                <th className="p-1 text-left">Kandidatnr</th>
                                <th className="p-1 text-left">Påmeldings-ID</th>
                                <th className="p-1 text-left">Fødselsnummer</th>
                                <th className="p-1 text-left">Fornavn</th>
                                <th className="p-1 text-left">Etternavn</th>
                              </tr>
                            </thead>
                            <tbody>
                              {rawStatisticsData.generell.kandidaterDetaljer?.map((k, idx) => (
                                <tr key={idx} className="border-t text-xs">
                                  <td className="p-1">{k.kandidatnummer || '-'}</td>
                                  <td className="p-1 font-mono">{k.kandidatpameldingsID}</td>
                                  <td className="p-1 font-mono">{k.fodselsnummer}</td>
                                  <td className="p-1">{k.fornavn || '-'}</td>
                                  <td className="p-1">{k.etternavn || '-'}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </PopoverContent>
                    </Popover>

                    <Popover>
                      <PopoverTrigger asChild>
                        <div className="p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            Totalt antall grupper
                            <Info className="h-3 w-3" />
                          </p>
                          <p className="text-3xl font-bold text-green-600">{rawStatisticsData.generell.totaltAntallGrupper}</p>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-[500px] max-h-96 overflow-auto">
                        <h4 className="font-medium text-sm mb-2">Grupper</h4>
                        <div className="max-h-64 overflow-y-auto">
                          <table className="w-full text-xs">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                <th className="p-1 text-left">Kode</th>
                                <th className="p-1 text-left">Navn</th>
                              </tr>
                            </thead>
                            <tbody>
                              {rawStatisticsData.generell.grupperDetaljer?.map((g, idx) => (
                                <tr key={idx} className="border-t text-xs">
                                  <td className="p-1 font-mono">{g.kandidatgruppekode}</td>
                                  <td className="p-1">{g.kandidatgruppenavn || '-'}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </PopoverContent>
                    </Popover>

                    <div className="p-4 rounded-lg border">
                      <p className="text-sm text-muted-foreground">Snitt kandidater per gruppe</p>
                      <p className="text-3xl font-bold text-purple-600">{rawStatisticsData.generell.gjennomsnittKandidaterPerGruppe}</p>
                    </div>

                    <Popover>
                      <PopoverTrigger asChild>
                        <div className="p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            Antall skoler med kandidater
                            <Info className="h-3 w-3" />
                          </p>
                          <p className="text-3xl font-bold text-teal-600">{rawStatisticsData.generell.antallUnikeSkoler}</p>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-[500px] max-h-96 overflow-auto">
                        <h4 className="font-medium text-sm mb-2">Skoler</h4>
                        <div className="max-h-64 overflow-y-auto">
                          <table className="w-full text-xs">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                <th className="p-1 text-left">Skole-ID</th>
                                <th className="p-1 text-right">Kandidater</th>
                                <th className="p-1 text-right">Grupper</th>
                              </tr>
                            </thead>
                            <tbody>
                              {rawStatisticsData.generell.skolerDetaljer?.map((s, idx) => (
                                <tr key={idx} className="border-t text-xs">
                                  <td className="p-1 font-mono">{s.skoleID}</td>
                                  <td className="p-1 text-right font-bold">{s.antallKandidater}</td>
                                  <td className="p-1 text-right">{s.antallGrupper}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </PopoverContent>
                    </Popover>

                    <Popover>
                      <PopoverTrigger asChild>
                        <div className={`p-4 rounded-lg border cursor-pointer transition-colors hover:bg-gray-50 ${
                          rawStatisticsData.generell.kandidaterUtenGruppe > 0
                            ? 'border-yellow-400'
                            : 'border-gray-200'
                        }`}>
                          <p className={`text-sm flex items-center gap-1 ${rawStatisticsData.generell.kandidaterUtenGruppe > 0 ? 'text-yellow-600' : 'text-muted-foreground'}`}>
                            Kandidater uten gruppe
                            <Info className="h-3 w-3" />
                          </p>
                          <p className={`text-3xl font-bold ${rawStatisticsData.generell.kandidaterUtenGruppe > 0 ? 'text-yellow-600' : ''}`}>{rawStatisticsData.generell.kandidaterUtenGruppe}</p>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-[1000px] max-h-96 overflow-auto">
                        <h4 className="font-medium text-sm mb-2">Kandidater uten gruppe</h4>
                        <div className="max-h-64 overflow-y-auto">
                          <table className="w-full text-xs">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                <th className="p-1 text-left">Kandidatnr</th>
                                <th className="p-1 text-left">Påmeldings-ID</th>
                                <th className="p-1 text-left">Fødselsnummer</th>
                                <th className="p-1 text-left">Fornavn</th>
                                <th className="p-1 text-left">Etternavn</th>
                              </tr>
                            </thead>
                            <tbody>
                              {rawStatisticsData.generell.kandidaterUtenGruppeDetaljer?.map((k, idx) => (
                                <tr key={idx} className="border-t text-xs">
                                  <td className="p-1">{k.kandidatnummer || '-'}</td>
                                  <td className="p-1 font-mono">{k.kandidatpameldingsID}</td>
                                  <td className="p-1 font-mono">{k.fodselsnummer}</td>
                                  <td className="p-1">{k.fornavn || '-'}</td>
                                  <td className="p-1">{k.etternavn || '-'}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </PopoverContent>
                    </Popover>

                    <div className={`p-4 rounded-lg border ${rawStatisticsData.generell.kandidaterIGrupperUtenFagkode > 0 ? 'border-amber-400' : 'border-gray-200'}`}>
                      <p className={`text-sm flex items-center gap-1 ${rawStatisticsData.generell.kandidaterIGrupperUtenFagkode > 0 ? 'text-amber-600' : 'text-muted-foreground'}`}>
                        Kandidater i grupper uten fagkodeeksamen
                        <Popover>
                          <PopoverTrigger asChild>
                            <button className="hover:opacity-70">
                              <Info className="h-3 w-3" />
                            </button>
                          </PopoverTrigger>
                          <PopoverContent className="w-80">
                            <div className="space-y-2">
                              <h4 className="font-medium text-sm">Forklaring</h4>
                              <p className="text-xs">
                                Kandidater som tilhører en gruppe, men gruppen mangler kobling til en fagkodeeksamen.
                                Dette kan være datakvalitetsproblem som bør undersøkes.
                              </p>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </p>
                      <p className={`text-3xl font-bold ${rawStatisticsData.generell.kandidaterIGrupperUtenFagkode > 0 ? 'text-amber-600' : ''}`}>{rawStatisticsData.generell.kandidaterIGrupperUtenFagkode}</p>
                    </div>

                    <Popover>
                      <PopoverTrigger asChild>
                        <div className={`p-4 rounded-lg border cursor-pointer transition-colors hover:bg-gray-50 ${
                          rawStatisticsData.generell.grupperUtenKandidater > 0
                            ? 'border-red-400'
                            : 'border-gray-200'
                        }`}>
                          <p className={`text-sm flex items-center gap-1 ${rawStatisticsData.generell.grupperUtenKandidater > 0 ? 'text-red-600' : 'text-muted-foreground'}`}>
                            Grupper uten kandidater
                            <Info className="h-3 w-3" />
                          </p>
                          <p className={`text-3xl font-bold ${rawStatisticsData.generell.grupperUtenKandidater > 0 ? 'text-red-600' : ''}`}>{rawStatisticsData.generell.grupperUtenKandidater}</p>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-[500px] max-h-96 overflow-auto">
                        <h4 className="font-medium text-sm mb-2">Grupper uten kandidater</h4>
                        <div className="max-h-64 overflow-y-auto">
                          <table className="w-full text-xs">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                <th className="p-1 text-left">Kode</th>
                                <th className="p-1 text-left">Navn</th>
                              </tr>
                            </thead>
                            <tbody>
                              {rawStatisticsData.generell.grupperUtenKandidaterDetaljer?.map((g, idx) => (
                                <tr key={idx} className="border-t text-xs">
                                  <td className="p-1 font-mono">{g.kandidatgruppekode}</td>
                                  <td className="p-1">{g.kandidatgruppenavn || '-'}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </CardContent>
              </Card>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Fagkodefordeling</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="max-h-96 overflow-y-auto">
                        <table className="w-full text-sm">
                          <thead className="bg-gray-50 sticky top-0">
                            <tr>
                              <th className="p-2 text-left">Fagkode</th>
                              <th className="p-2 text-left">Fagnavn</th>
                              <th className="p-2 text-right">Kandidater</th>
                              <th className="p-2 text-right">Grupper</th>
                              <th className="p-2 text-right">Eksamensdato</th>
                            </tr>
                          </thead>
                          <tbody>
                            {rawStatisticsData.perFagkode.map((fag, idx) => (
                              <tr key={idx} className="border-t">
                                <td className="p-2 font-mono">{fag.fagkode}</td>
                                <td className="p-2">{fag.fagnavn}</td>
                                <td className="p-2 text-right font-bold">{fag.antallKandidater}</td>
                                <td className="p-2 text-right">{fag.antallGrupper}</td>
                                <td className="p-2 text-right">{fag.eksamensdato ? format(new Date(fag.eksamensdato), "d. MMM yyyy", { locale: nb }) : '-'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Fordeling pr dag</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="max-h-96 overflow-y-auto">
                        <table className="w-full text-sm">
                          <thead className="bg-gray-50 sticky top-0">
                            <tr>
                              <th className="p-2 text-left">Dato</th>
                              <th className="p-2 text-right">Kandidater</th>
                              <th className="p-2 text-right">Grupper</th>
                            </tr>
                          </thead>
                          <tbody>
                            {rawStatisticsData.tidsmessig.opprettelserPerDag.map((dag, idx) => (
                              <tr key={idx} className="border-t">
                                <td className="p-2">{format(new Date(dag.dato), "d. MMM yyyy", { locale: nb })}</td>
                                <td className="p-2 text-right font-bold">{dag.antallKandidater}</td>
                                <td className="p-2 text-right">{dag.antallGrupper}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Innleveringer Tab */}
              <TabsContent value="innleveringer" className="space-y-6">
                {besvarelseData ? (
                  <>
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          Innleveringsoversikt
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <Info className="h-4 w-4 text-gray-500" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-80">
                            <div className="space-y-2">
                              <h4 className="font-medium text-sm">Forklaring</h4>
                              <div className="text-sm space-y-1">
                                <p><strong>Totalt besvarelsesdeler:</strong> Antall besvarelsesdeler totalt (Del 1, Del 2, etc.)</p>
                                <p><strong>Kandidater som har levert:</strong> Antall kandidater med status "Levert digitalt" eller "Sendes i posten"</p>
                                <p><strong>Totalt kandidater:</strong> Alle kandidater med besvarelsesdeler</p>
                                <p><strong>Gjennomføringsgrad:</strong> Prosent av forventede kandidater som har levert</p>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </CardTitle>
                      <CardDescription>Besvarelser for {besvarelseData.eksamensperiode}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="p-4 rounded-lg border">
                          <p className="text-sm text-muted-foreground">Kandidater som har levert</p>
                          <p className="text-3xl font-bold text-emerald-600">{besvarelseData.innleveringsoversikt.totalKandidaterMedLevering}</p>
                        </div>
                        <div className="p-4 rounded-lg border">
                          <p className="text-sm text-muted-foreground">Totalt kandidater</p>
                          <p className="text-3xl font-bold text-amber-600">{besvarelseData.innleveringsoversikt.totalKandidater}</p>
                        </div>
                        <div className="p-4 rounded-lg border">
                          <p className="text-sm text-muted-foreground">Forventet antall</p>
                          <p className="text-3xl font-bold text-sky-600">{besvarelseData.innleveringsoversikt.totaltForventetKandidater}</p>
                        </div>
                        <div className="p-4 rounded-lg border">
                          <p className="text-sm text-muted-foreground">Gjennomføringsgrad</p>
                          <p className="text-3xl font-bold text-violet-600">{besvarelseData.innleveringsoversikt.gjennomforingsgradProsent}%</p>
                        </div>
                      </div>

                      {/* Status oversikt */}
                      {besvarelseData.innleveringsoversikt.statusOversikt.length > 0 && (
                        <div className="mt-4">
                          <p className="text-sm font-medium mb-2">Kandidater gruppert etter status</p>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                            {besvarelseData.innleveringsoversikt.statusOversikt.map((status, idx) => {
                              // Definer forklaringstekst for hver status
                              const statusForklaring: Record<string, string> = {
                                'Levert digitalt': 'Kandidater som har minst én besvarelsesdel med status "Levert digitalt". Dette er kandidater som har fullført og levert sin eksamen digitalt.',
                                'Sendes i posten': 'Kandidater som har minst én besvarelsesdel med status "Sendes i posten". Dette er kandidater som leverer besvarelsen fysisk.',
                                'Laster opp': 'Kandidater som har minst én besvarelsesdel med status "Laster opp". Dette er kandidater som er i ferd med å laste opp filer.',
                                'Ikke lastet opp': 'Kandidater som kun har besvarelsesdeler med status "Ikke lastet opp". Dette er kandidater som ikke har startet opplasting av filer ennå.',
                                'Ingen besvarelse': 'Kandidater som ikke har noen besvarelsesdeler registrert. Dette kan være kandidater med fravær, kandidater som ikke har startet eksamen, eller kandidater der besvarelser ikke er importert ennå. OBS: Inkluderer kun kandidater i grupper med fagkodeeksamen.'
                              };

                              return (
                                <Popover key={idx}>
                                  <PopoverTrigger asChild>
                                    <div className="p-3 bg-white rounded border cursor-pointer hover:bg-gray-50 transition-colors">
                                      <p className="text-xs text-gray-600 flex items-center gap-1">
                                        {status.status}
                                        <Info className="h-3 w-3" />
                                      </p>
                                      <p className="text-lg font-bold">{status.antallKandidater} <span className="text-sm text-gray-500">({status.prosent}%)</span></p>
                                    </div>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-80">
                                    <div className="space-y-2">
                                      <h4 className="font-medium text-sm">{status.status}</h4>
                                      <p className="text-xs text-gray-700">
                                        {statusForklaring[status.status] || 'Ingen beskrivelse tilgjengelig for denne statusen.'}
                                      </p>
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Besvarelser per fagkode</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="max-h-96 overflow-y-auto">
                        <table className="w-full text-sm">
                          <thead className="bg-gray-50 sticky top-0">
                            <tr>
                              <th className="p-2 text-left">Fagkode</th>
                              <th className="p-2 text-left">Navn</th>
                              <th className="p-2 text-right">Deler (levert)</th>
                              <th className="p-2 text-right">Levert</th>
                              <th className="p-2 text-right">Totalt</th>
                              <th className="p-2 text-right">Gjennomføring</th>
                            </tr>
                          </thead>
                          <tbody>
                            {besvarelseData.innleveringsoversikt.perFagkode.map((fag, idx) => (
                              <tr key={idx} className="border-t">
                                <td className="p-2 font-mono">{fag.fagkode}</td>
                                <td className="p-2">{fag.fagkodenavn}</td>
                                <td className="p-2 text-right">
                                  {fag.antallBesvarelsesdeler} <span className="text-xs text-gray-600">({fag.antallLeverteBesvarelsesdeler})</span>
                                </td>
                                <td className="p-2 text-right font-bold">{fag.antallKandidaterMedLevering}</td>
                                <td className="p-2 text-right">{fag.antallKandidater}</td>
                                <td className="p-2 text-right">
                                  <span className={`font-bold ${
                                    fag.gjennomforingsgradProsent >= 90 ? 'text-green-600' :
                                    fag.gjennomforingsgradProsent >= 70 ? 'text-yellow-600' :
                                    'text-red-600'
                                  }`}>
                                    {fag.gjennomforingsgradProsent}%
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Innleveringer per time */}
                  {besvarelseData.innleveringsoversikt.innleveringerPerTime.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Innleveringer per time</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={besvarelseData.innleveringsoversikt.innleveringerPerTime}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="antall" fill="#8884d8" name="Antall innleveringer" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  )}
                  </>
                ) : (
                  <Card>
                    <CardContent className="py-8">
                      <p className="text-center text-gray-500">Ingen innleveringsdata tilgjengelig for denne perioden</p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              {/* Filstatistikk Tab */}
              <TabsContent value="filer" className="space-y-6">
                {besvarelseData ? (
                  <>
                    <Card>
                      <CardHeader>
                        <CardTitle>Filstatistikk</CardTitle>
                        <CardDescription>Oversikt over filer for {besvarelseData.eksamensperiode}</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="p-3 rounded-lg border">
                            <p className="text-xs text-muted-foreground">Totalt antall filer</p>
                            <p className="text-2xl font-bold text-blue-600">{besvarelseData.filstatistikk.totaltAntallFiler}</p>
                          </div>
                          <div className="p-3 rounded-lg border">
                            <p className="text-xs text-muted-foreground">Total filstørrelse</p>
                            <p className="text-2xl font-bold text-green-600">{besvarelseData.filstatistikk.totalFilstorrelseMB} MB</p>
                          </div>
                          <div className="p-3 rounded-lg border">
                            <p className="text-xs text-muted-foreground">Snitt filer per kandidat</p>
                            <p className="text-2xl font-bold text-purple-600">{besvarelseData.filstatistikk.gjennomsnittligAntallFilerPerKandidat}</p>
                          </div>
                          <div className="p-3 rounded-lg border">
                            <p className="text-xs text-muted-foreground">Snitt filstørrelse</p>
                            <p className="text-2xl font-bold text-orange-600">{besvarelseData.filstatistikk.gjennomsnittligFilstorrelseMB} MB</p>
                          </div>
                        </div>

                        {/* Filstørrelse-distribusjon og Filtyper side ved side */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* Filstørrelse-distribusjon */}
                          {besvarelseData.filstatistikk.filstorrelseDistribusjon.length > 0 && (
                            <div className="p-4 rounded-lg border">
                              <p className="text-sm font-medium mb-3">Filstørrelse-distribusjon</p>
                              <div className="space-y-2">
                                {besvarelseData.filstatistikk.filstorrelseDistribusjon.map((dist, idx) => (
                                  <div key={idx} className="flex justify-between items-center text-sm">
                                    <span className="font-medium w-24">{dist.kategori}</span>
                                    <div className="flex items-center gap-2 flex-1">
                                      <div className="flex-1 bg-gray-200 rounded-full h-3">
                                        <div
                                          className="bg-emerald-600 h-3 rounded-full"
                                          style={{ width: `${dist.prosent}%` }}
                                        ></div>
                                      </div>
                                      <span className="text-xs text-gray-600 w-24 text-right">
                                        {dist.antall.toLocaleString()} ({dist.prosent}%)
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Filtyper */}
                          {besvarelseData.filstatistikk.filtyper.length > 0 && (
                            <div className="p-4 rounded-lg border">
                              <p className="text-sm font-medium mb-3">Filtyper (topp 10)</p>
                              <div className="space-y-1">
                                {besvarelseData.filstatistikk.filtyper.slice(0, 10).map((type, idx) => (
                                  <div key={idx} className="flex justify-between items-center text-sm">
                                    <span className="font-mono">.{type.filtype}</span>
                                    <div className="flex items-center gap-2">
                                      <div className="w-32 bg-gray-200 rounded-full h-2">
                                        <div
                                          className="bg-blue-600 h-2 rounded-full"
                                          style={{ width: `${type.prosent}%` }}
                                        ></div>
                                      </div>
                                      <span className="text-xs text-gray-600 w-16 text-right">
                                        {type.antall} ({type.prosent}%)
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </>
                ) : (
                  <Card>
                    <CardContent className="py-8">
                      <p className="text-center text-gray-500">Ingen filstatistikk tilgjengelig for denne perioden</p>
                    </CardContent>
                  </Card>
                )}
                </TabsContent>
              </Tabs>
            )}
          </div>
        </div>
      </div>
      </>
    );
  }
