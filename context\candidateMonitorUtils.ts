import dayjs from "dayjs";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";

// Configuration constants
export const POLL_INTERVAL = 30000;
export const ERROR_RETRY_COUNT = 3;

// API utility functions
export const createApiError = (
  url: string,
  status: number,
  message: string
): Error => {
  return new Error(`API Error (${status}) for ${url}: ${message}`);
};

export const fetcher = async (url: string, body: any): Promise<any> => {
  const response = await fetch(url, {
    method: "POST",
    body: JSON.stringify(body),
    headers: { "Content-Type": "application/json" },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw createApiError(
      url,
      response.status,
      errorText || response.statusText
    );
  }

  return response.json();
};

export const getFetcher = async (url: string): Promise<any> => {
  const response = await fetch(url, {
    method: "GET",
    headers: { "Content-Type": "application/json" },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw createApiError(
      url,
      response.status,
      errorText || response.statusText
    );
  }

  return response.json();
};

// Candidate status calculation utilities
export const calculateCandidateStatuses = (
  examPaper: IExamPaperInternal[],
  serverTime: dayjs.Dayjs
) => {
  const hasAbsence: Record<string, boolean> = {};
  const hasLevert: Record<string, boolean> = {};
  const waitingForExamStart: Record<string, boolean> = {};

  examPaper.forEach((paper) => {
    const partOneStart = paper.partOneStartDateTime
      ? dayjs.utc(paper.partOneStartDateTime).tz("Europe/Oslo")
      : null;
    const partTwoStart = paper.partTwoStartDateTime
      ? dayjs.utc(paper.partTwoStartDateTime).tz("Europe/Oslo")
      : null;

    paper.candidates.forEach((candidate) => {
      const candidateNumber = candidate.candidateNumber;

      // Check for absence status
      const hasAbsenceStatus = [
        candidate.deliveryStatusPart1,
        candidate.deliveryStatusPart2,
      ].some(
        (status) =>
          status === CandidateStatusEnum.DokumentertFravaer ||
          status === CandidateStatusEnum.IkkeDokumentertFravaer
      );

      // Check for delivery status
      const validDeliveryStatuses = [
        CandidateStatusEnum.Levert,
        CandidateStatusEnum.LevertManuelt,
      ];

      const hasLevertStatus =
        (validDeliveryStatuses.includes(candidate.deliveryStatusPart1) &&
          candidate.deliveryStatusPart2 === -1) ||
        (validDeliveryStatuses.includes(candidate.deliveryStatusPart1) &&
          validDeliveryStatuses.includes(candidate.deliveryStatusPart2));

      // Check if waiting for exam start
      const isOnePartExam = candidate.deliveryStatusPart2 === -1;
      let examStarted = false;

      if (isOnePartExam) {
        examStarted = partOneStart ? serverTime.isAfter(partOneStart) : false;
      } else {
        examStarted = partTwoStart ? serverTime.isAfter(partTwoStart) : false;
      }

      // Update the status maps
      hasAbsence[candidateNumber] = hasAbsenceStatus;
      hasLevert[candidateNumber] = hasLevertStatus;
      waitingForExamStart[candidateNumber] =
        !examStarted && !hasLevertStatus && !hasAbsenceStatus;
    });
  });

  return { hasAbsence, hasLevert, waitingForExamStart };
};

// Redis data processing utilities
export const processAuthorizedUsers = (
  redisData: IRedisObject[],
  examPaper: IExamPaperInternal[]
): Record<string, boolean> => {
  // Initialize a map with all candidates from examPaper set to false
  const finalAuthorizedMap: Record<string, boolean> = {};
  examPaper.forEach((paper) => {
    paper.candidates.forEach((candidate) => {
      finalAuthorizedMap[candidate.candidateNumber] = false;
    });
  });

  // Update the map based on ANY authorized session for each candidate
  if (redisData && redisData.length > 0) {
    examPaper.forEach((paper) => {
      paper.candidates.forEach((candidate) => {
        const candidateNumber = candidate.candidateNumber;

        // Get all sessions for this candidate
        const candidateSessions = redisData.filter(
          (item) => item && item.candidateNumber === candidateNumber
        );

        // Check if ANY session is authorized
        const hasAnyAuthorizedSession = candidateSessions.some(
          (session) => session.isAuthorized
        );

        // Set authorization based ONLY on actual sessions, not access requests
        finalAuthorizedMap[candidateNumber] = hasAnyAuthorizedSession;
      });
    });
  }

  return finalAuthorizedMap;
};

// Blocked users processing utilities
export const processBlockedUsers = (
  blockedUserIds: string[],
  examPaper: IExamPaperInternal[]
): Record<string, boolean> => {
  const blockedUsersMap: Record<string, boolean> = {};
  const allCandidates = examPaper.flatMap((paper) => paper.candidates);

  blockedUserIds.forEach((userId: string) => {
    const candidate = allCandidates.find((c) => c.userId === userId);
    if (candidate) {
      blockedUsersMap[candidate.candidateNumber] = true;
    }
  });

  return blockedUsersMap;
};

// Latest session finder utility
export const findLatestSession = (
  candidateNumber: string,
  redisResult: IRedisObject[]
): IRedisObject | undefined => {
  if (!redisResult.length) return undefined;

  const candidateSessions = redisResult.filter(
    (item) => item && item.candidateNumber === candidateNumber
  );

  if (!candidateSessions.length) return undefined;

  return candidateSessions.sort((a, b) =>
    dayjs(b.timeForSession).diff(dayjs(a.timeForSession))
  )[0];
};
