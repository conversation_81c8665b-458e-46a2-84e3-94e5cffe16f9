"use server";

import { getServerSession } from "next-auth/next";
import { generateAccessToken } from "./getAccessTokenForEksamen";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISchools } from "@/interface/ISchools";
import { ISession } from "@/interface/ISession";
import { getAppInsightsServer } from "./appInsightsServer";

const EksamenApiUrl = process.env.PGSA_EKSAMENAPI_URL;

const telemetryClient = getAppInsightsServer();

export async function getSchools(): Promise<ISchools[]> {
  let response = null;

  try {
    const session: ISession | null = await getServerSession(authOptions);
    const userId = session?.user.userInfo.userId;

    const accessToken = await generateAccessToken();

    response = await fetch(`${EksamenApiUrl}/schools/${userId}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error("Response not ok");
    }

    return response.json();
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "getSchools",
        statuscode: response ? response.status : 0,
        response: response ? await response.text() : "Tom respons",
      },
    });

    throw new Error("Error in getSchools API");
  }
}
