"use server";

import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { NextRequest, NextResponse } from "next/server";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import { AuditLogService } from "@/db/services/auditLogService";

const telemetryClient = getAppInsightsServer();

// Utility function to sanitize and validate userId input
function sanitizeAndValidateUserId(userId: string): string | null {
  if (!userId || typeof userId !== "string") {
    return null;
  }

  // Remove any potentially dangerous characters and trim whitespace
  // Only allow digits and hyphens for candidate IDs
  const sanitized = userId.trim().replace(/[^0-9\-]/g, "");

  // Validate format: either GUID format with only numbers or pure numeric
  const isValidGuid = /^[0-9]{8}-[0-9]{4}-[0-9]{4}-[0-9]{4}-[0-9]{12}$/.test(
    sanitized
  );
  const isValidNumeric = /^\d+$/.test(sanitized);

  if (!isValidGuid && !isValidNumeric) {
    return null;
  }

  // Convert numeric to GUID format if needed
  if (isValidNumeric) {
    const paddedNumber = sanitized.padStart(12, "0");
    return `00000000-0000-0000-0000-${paddedNumber}`;
  }

  return sanitized;
}

export async function POST(request: NextRequest) {
  let userId: string | undefined;

  try {
    const body = await request.json();
    userId = body.userId;

    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!userId) {
      return NextResponse.json(
        { message: "Mangler userid/kandidatpåmeldingsid i forespørselen" },
        { status: 400 }
      );
    }

    // Input validation and sanitization
    const sanitizedUserId = sanitizeAndValidateUserId(userId);
    if (!sanitizedUserId) {
      // Log security event
      telemetryClient?.trackEvent({
        name: "InvalidActivityLogInput",
        properties: {
          action: "activitylogv2",
          originalInput: userId,
          userAgent: request.headers.get("user-agent") || "unknown",
          clientIp: request.headers.get("x-forwarded-for") || "unknown",
        },
      });

      return NextResponse.json(
        { error: "Ugyldig kandidatnummer format" },
        { status: 400 }
      );
    }

    // Get audit logs using AuditLogService
    const auditLogService = new AuditLogService();
    const auditLogs = await auditLogService.getAuditLogsForCandidate(
      sanitizedUserId
    );

    return NextResponse.json(auditLogs);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "activitylogv2",
        payload: userId || "unknown",
        statusCode: 500,
        response: "Error occurred while fetching audit logs",
      },
    });
    console.error("Error in getting activity log", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
