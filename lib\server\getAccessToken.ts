"use server";

import { getValueFromRedis, setValueInRedis } from "@/app/lib/redisHelper";
import { getAppInsightsServer } from "./appInsightsServer";

const telemetryClient = getAppInsightsServer();

export async function getAccessToken(
  clientId: string,
  clientSecret: string,
  scope: string,
  accesstokenKey: string
): Promise<string> {
  let accessToken = await getValueFromRedis(accesstokenKey);

  if (accessToken) return JSON.parse(accessToken);

  const tokenEndpoint: string = `${process.env.NEXTAUTH_UIDP_URL}/connect/token`;
  let tokenExpiresInSeconds: number = 2400; // 40 minutes

  const params = new URLSearchParams();

  params.append("client_id", clientId);
  params.append("client_secret", clientSecret);
  params.append("grant_type", "client_credentials");
  params.append("scope", scope);

  let response = null;
  try {
    response = await fetch(tokenEndpoint, {
      method: "POST",
      body: params,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(
        `Feil ved henting av token mot PASX. Statuskode: ${response.status}. Feilmelding: ${response.statusText}`
      );
    }

    const data: any = await response.json();
    accessToken = await data.access_token;
    await setValueInRedis(
      accesstokenKey,
      JSON.stringify(accessToken),
      tokenExpiresInSeconds
    );

    return accessToken ? accessToken : "";
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "getAccessToken",
        status: response ? response.status : 0,
        response: response ? await response.text() : "Tom respons",
      },
    });

    console.error(
      "Klarte ikke å hente token for å autentisere api-kall:",
      error
    );
    throw new Error("Klarte ikke å hente token for å autentisere api-kall");
  }
}
