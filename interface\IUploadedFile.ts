import { TestPartsEnum } from "@/enums/TestPart";
import dayjs from "dayjs";

export interface IUploadedFile {
  FileGuid: string;
  File: File | null;
  UploadedDate: dayjs.Dayjs;
  Name: string;
  CandidateName?: string;
  SubjectCode?: string;
  Size: number;
  TestPartId: TestPartsEnum;
  Errors?: string[];
  IsRejected: boolean;
  IsLoading: boolean;
  Candididate: string;
  candidateRegistrationId?: string; // From filevalidate API response (candiateRegistrationId)
  UploadFinished: boolean;
  SchoolId: string;
  IsDeleting: boolean;
  Groupcode: string;
  Delivered: boolean;
  SubmittedDate?: dayjs.Dayjs;
  IsSubmitting: boolean;
  UploadedBy?: string;
  Checked?: boolean;
}
