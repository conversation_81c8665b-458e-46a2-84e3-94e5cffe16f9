"use client";
import Image from "next/image";
import { createContext, useEffect, useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { sendFeedBack } from "@/lib/server/sendFeedbackForm";
import { useFormState, useFormStatus } from "react-dom";
import { error } from "console";
import React from "react";
import { Loader2 } from "lucide-react";
/*
async function send() {
  await sendFeedBack();
}*/

interface IfeedBackButtonProp {
  pathname?: string;
  setState?: React.Dispatch<React.SetStateAction<boolean>>;
}
export default function SendBtn({ pathname, setState }: IfeedBackButtonProp) {
  const initialState = {
    status: "",
    message: "",
  };

  const maxChars = 500;

  const [feedbackText, setFeedbackText] = useState("");

  const [state, formAction] = useFormState(sendFeedBack, initialState);
  const [loading, setLoading] = useState(false);

  function hideFeedbackForm() {
    if (setState) {
      setState(false);
    }
  }
  useEffect(() => {
    if (state?.status === "error" || state?.status === "success") {
      setFeedbackText("");
      setLoading(false);
    }
    setTimeout(() => {
      state.status = "";
    }, 2000);
  }, [state?.status]);

  return (
    <>
      <form
        action={formAction}
        onSubmit={() => {
          setLoading(true);
        }}
      >
        <Label htmlFor="tilbakemelding" id="tilbakemeldingLabel">
          Din tilbakemelding
        </Label>
        <input
          type="hidden"
          name="role"
          id="role"
          value={sessionStorage?.getItem("selectedRole") ?? ""}
        />
        <input type="hidden" name="path" id="path" value={pathname} />
        <Textarea
          className={` h-32 ${pathname ? "" : "w-1/2"}`}
          name="tilbakemelding"
          id="tilbakemelding"
          autoComplete="on"
          value={feedbackText}
          maxLength={maxChars}
          onChange={(e) => setFeedbackText(e.target.value)}
        />
        <div className="text-sm text-gray-600">
          {feedbackText.length}/{maxChars} tegn
        </div>
        <div className="gap-4 flex mt-3">
          <Button variant="default" disabled={loading} type="submit">
            {loading ? (
              <span className="flex items-center">
                <Loader2 className="animate-spin mr-2 h-4 w-4" />
                Sender...
              </span>
            ) : (
              "Send tilbakemelding"
            )}
          </Button>
          <Button
            variant="outline"
            type="button"
            className=""
            onClick={() => {
              hideFeedbackForm();
            }}
          >
            Avbryt
          </Button>
        </div>
      </form>
      {state?.status === "error" && (
        <div className="text-red-500">{state?.message}</div>
      )}
      {state?.status === "success" && <div className="">{state?.message}</div>}
    </>
  );
}
