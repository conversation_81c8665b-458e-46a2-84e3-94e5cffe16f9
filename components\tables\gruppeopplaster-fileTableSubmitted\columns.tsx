"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { TestPartsEnum } from "@/enums/TestPart";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { ColumnDef, Table } from "@tanstack/react-table";
import dayjs from "dayjs";
import { ArrowUpDown } from "lucide-react";

function formatFileSize(bytes: number) {
  const KB = 1024;
  const MB = 1024 * KB;
  if (bytes < MB) {
    return `${(bytes / KB).toFixed(0)} kB`;
  } else {
    return `${(bytes / MB).toFixed(2)} MB`;
  }
}

function formatTestPartId(testpartId: TestPartsEnum) {
  switch (testpartId) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og 2";
    default:
      return "Angi eksamensdel";
  }
}
function minimizeSuffix(filename: string) {
  const parts = filename.split(".");
  if (parts.length > 1) {
    const suffix = parts.pop();
    return parts.join(".") + "." + suffix?.toLowerCase();
  }

  return filename;
}

function sumFileSize(table: Table<IUploadedFile>) {
  let totalSize = 0;
  const rowCount = table.getRowCount();
  for (let i = 0; i < rowCount; i++) {
    const row = table.getRow(i.toString()); // Convert i to a string
    const rowData = row.original;
    totalSize += rowData.Size;
  }
  return totalSize;
}

function getDistinctCandidatesNumber(table: Table<IUploadedFile>) {
  const candidatesSet = new Set();

  for (let i = 0; i < table.getRowCount(); i++) {
    const row = table.getRow(i.toString()); // Convert i to a string
    const file = row.original.File;
    const hyphenIndex = file?.name.indexOf("-");

    if (hyphenIndex !== -1) {
      const candidateNumber = file?.name.substring(0, hyphenIndex);
      candidatesSet.add(candidateNumber);
    }
  }

  return candidatesSet.size;
}
export const columnsSubmitted: ColumnDef<IUploadedFile>[] = [
  {
    accessorKey: "Name",
    header: ({ table, column }) => {
      const rowCount = table.getRowCount();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize">Filer ({rowCount})</div>
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <span className=" mt-1">{minimizeSuffix(row.original.Name)}</span>;
    },
  },
  {
    accessorKey: "Candididate",
    header: ({ table, column }) => {
      const rowCount = table.getRowCount();

      return (
        <Button
          className="p-0 hover:bg-transparent"
          variant="ghost"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize ">
            Kandidater ({getDistinctCandidatesNumber(table)})
          </div>
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="">{row.original.Candididate}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "CandididateName",
    header: ({ table, column }) => {
      const rowCount = table.getRowCount();

      return (
        <Button
          className="p-0 hover:bg-transparent"
          variant="ghost"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize ">
            Kandidatnavn ({getDistinctCandidatesNumber(table)})
          </div>
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="">{row.original.CandidateName}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "SubjectCode",
    header: ({ table, column }) => {
      const rowCount = table.getRowCount();
      const subjectCodesSet = new Set();
      for (let i = 0; i < rowCount; i++) {
        const row = table.getRow(i.toString()); // Convert i to a string
        const subjectCode = row.original.SubjectCode;
        subjectCodesSet.add(subjectCode);
      }
      const uniqueSubjectCodesCount = subjectCodesSet.size;
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize">Fagkoder ({uniqueSubjectCodesCount})</div>
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "Size",
    header: ({ table, column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div>{`Størrelse (${formatFileSize(sumFileSize(table))})`}</div>
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="">{formatFileSize(row.getValue("Size"))}</div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "TestPartId",
    header: ({ table, column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize">Eksamensdel</div>
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="capitalize">
        {formatTestPartId(row.getValue("TestPartId"))}
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "SubmittedDate",
    header: ({ table, column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize">Opplastet</div>
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date: string = row.getValue("SubmittedDate");
      // Sjekk om date er en gyldig dato
      const formattedDate = dayjs(date).isValid()
        ? dayjs(date).format("HH:mm")
        : "Ugyldig dato";
      return <div className="capitalize">{formattedDate}</div>;
    },
    enableSorting: true,
  },
];
