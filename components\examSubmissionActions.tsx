"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { FaChevronDown } from "react-icons/fa";

import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import useFileHandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import { useCandidate } from "@/context/CandidateMonitorContext";
import { useDialogMonitor } from "@/context/DialogMonitorContext";
import { useRole } from "@/context/RoleContext";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import AbsenceOptions from "./absenceOptions";
import BlockDigitalAccess from "./blockDigitalAccess";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { TestPartsEnum } from "@/enums/TestPart";
import { DocumentStatusEnum } from "@/enums/DocumentStatusEnum";
import { ActivityLogger } from "@/lib/shared/ActivityLogger";
import { OperationEnum } from "@/enums/OperationEnum";

const ABSENCE_STATUSES = [
  CandidateStatusEnum.DokumentertFravaer,
  CandidateStatusEnum.IkkeDokumentertFravaer,
];

const ExamSubmissionActions: React.FC<{ candidateInfo: ICandidateMonitor }> = ({
  candidateInfo,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const { setCandidateNumber } = useFileHandlerForMonitor();
  const { openDialog } = useDialogMonitor();
  const {
    handleStatusUpdate,
    updateAuthorizedCandidates,
    authorizedCandidates,
    refreshRedisData,
    hasAbsence,
  } = useCandidate();
  const { selectedRole } = useRole();

  const isOnePartExam = candidateInfo.deliveryStatusPart2 === -1;
  const isAbsent = hasAbsence[candidateInfo.candidateNumber];

  const hasPaperStatus = isOnePartExam
    ? candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.LevertManuelt
    : candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.LevertManuelt &&
      candidateInfo.deliveryStatusPart2 === CandidateStatusEnum.LevertManuelt;
  const canDeliverForCandidate =
    !isAbsent &&
    !hasPaperStatus &&
    !(
      candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.LevertManuelt ||
      candidateInfo.deliveryStatusPart2 === CandidateStatusEnum.LevertManuelt
    );
  const canOpenNewDelivery = isOnePartExam
    ? candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.Levert
    : candidateInfo.deliveryStatusPart2 === CandidateStatusEnum.Levert;

  const canDeliverOnPaper =
    !isAbsent &&
    (isOnePartExam
      ? candidateInfo.deliveryStatusPart1 !==
          CandidateStatusEnum.LevertManuelt &&
        candidateInfo.deliveryStatusPart1 !== CandidateStatusEnum.Levert
      : !(
          candidateInfo.deliveryStatusPart1 ===
            CandidateStatusEnum.LevertManuelt &&
          candidateInfo.deliveryStatusPart2 ===
            CandidateStatusEnum.LevertManuelt
        ) &&
        (candidateInfo.deliveryStatusPart1 !== CandidateStatusEnum.Levert ||
          candidateInfo.deliveryStatusPart2 !== CandidateStatusEnum.Levert) &&
        !(
          (candidateInfo.deliveryStatusPart1 ===
            CandidateStatusEnum.LevertManuelt &&
            candidateInfo.deliveryStatusPart2 === CandidateStatusEnum.Levert) ||
          (candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.Levert &&
            candidateInfo.deliveryStatusPart2 ===
              CandidateStatusEnum.LevertManuelt)
        ));

  const canUndoDeliveredOnPaper =
    !isAbsent &&
    (isOnePartExam
      ? candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.LevertManuelt
      : candidateInfo.deliveryStatusPart1 ===
          CandidateStatusEnum.LevertManuelt ||
        candidateInfo.deliveryStatusPart2 ===
          CandidateStatusEnum.LevertManuelt);

  const showToast = (title: string, description: string, isError = false) => {
    toast({ title, description, variant: isError ? "destructive" : "default" });
  };

  const updateCandidateStatus = async (userId: string) => {
    const response = await fetch(`/api/candidateMetadata/${userId}`);
    if (!response.ok) {
      showToast("Feil", "Klarte ikke å hente kandidatmetadata", true);
      return null;
    }
    return await response.json();
  };

  const handleStatusUpdateAndToast = async (
    apiEndpoint: string,
    errorMessage: string,
    testpartId?: number
  ) => {
    try {
      const response = await fetch(`/api/${apiEndpoint}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: candidateInfo.userId,
          testpartId,
        }),
      });

      if (!response.ok) {
        showToast("En feil har oppstått", errorMessage, true);
        throw new Error(errorMessage);
      }

      const metadata = await updateCandidateStatus(candidateInfo.userId);

      if (!metadata) return;

      handleStatusUpdate({
        candidateNumber: candidateInfo.candidateNumber,
        statusPart1: metadata.deliveryStatusPart1,
        statusPart2: metadata.deliveryStatusPart2,
        testPartId:
          testpartId ??
          (isOnePartExam ? TestPartsEnum.Eksamen : TestPartsEnum.EksamenDel2),
      });

      updateAuthorizedCandidates({
        ...authorizedCandidates,
        [candidateInfo.candidateNumber]: false,
      });

      showToast(
        `${candidateInfo.candidateName} (${candidateInfo.candidateNumber})`,
        "Status er endret"
      );
    } catch (error: any) {
      showToast("En feil har oppstått", errorMessage, true);
    }
  };

  const handleOpenNewDelivery = async () => {
    await handleStatusUpdateAndToast(
      "openForNewDelivery",
      "Klarte ikke å åpne for ny levering"
    );

    /* const testPartId =
      candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.LevertManuelt
        ? TestPartsEnum.EksamenDel1
        : TestPartsEnum.EksamenDel2; */

    const testPartId = isOnePartExam
      ? TestPartsEnum.Eksamen
      : TestPartsEnum.EksamenDel2;

    await ActivityLogger.logOpenForNewDelivery(
      candidateInfo,
      `${selectedRole?.displayRoleName}`,
      isOnePartExam
    );
  };

  const setDelivered = async () => {
    const deauthorizePromise = fetch(`/api/deauthorizeUserObjects`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        candidateNumber: candidateInfo.candidateNumber,
      }),
    }).catch((error) => {
      // Fang feil i deauthorize-kallet slik at Promise.all ikke stopper opp
      console.error("Error deauthorizing user:", error);
      // Returner en verdi for å indikere at kallet feilet, om nødvendig
      return { success: false, error };
    });

    const statusUpdatePromise = handleStatusUpdateAndToast(
      "setDeliveredStatus",
      "Klarte ikke å sette levert status"
    );

    await Promise.all([deauthorizePromise, statusUpdatePromise]);

    const isOnePartExam = candidateInfo.deliveryStatusPart2 === -1;

    await ActivityLogger.logSetDelivered(
      candidateInfo,
      `${selectedRole?.displayRoleName}`,
      isOnePartExam
    );
  };


  const handleDeliveredOnPaper = async (testPartId: number) => {
    await handleStatusUpdateAndToast(
      "deliveredOnPaper",
      "Klarte ikke å sette sendes i posten",
      testPartId
    );

    try {
      // Remove access and deauthorize user
      await Promise.all([
        fetch(`/api/grantAccessRequest`, {
          method: "DELETE",
          body: JSON.stringify({
            candidateNumber: candidateInfo.candidateNumber,
            schoolId: selectedRole?.selectedSchoolId,
          }),
        }),
        fetch(`/api/deauthorizeUserObjects`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            candidateNumber: candidateInfo.candidateNumber,
          }),
        }),
      ]);
    } catch (error) {
      console.error("Error removing access:", error);
    }

    // Log activity with ActivityLogger
    await ActivityLogger.logDeliverOnPaper(
      candidateInfo,
      `${selectedRole?.displayRoleName}`,
      testPartId
    );

    await refreshRedisData();
  };


  const handleUndoDeliveredOnPaper = async (testPartId: number) => {
    await handleStatusUpdateAndToast(
      "revertDeliveredOnPaper",
      "Klarte ikke å gjøre om status sendes i posten",
      testPartId
    );

    // Log activity with ActivityLogger
    await ActivityLogger.logUndoDeliverOnPaper(
      candidateInfo,
      `${selectedRole?.displayRoleName}`,
      testPartId
    );
  };

  const handleDeliverForCandidate = () => {
    sessionStorage.setItem("selectedCandidate", JSON.stringify(candidateInfo));
    setCandidateNumber(candidateInfo.candidateNumber);

    const currentSearch = window.location.search;
    const currentParams = new URLSearchParams(currentSearch);
    const queryParams = new URLSearchParams();

    queryParams.set("part1", candidateInfo.deliveryStatusPart1.toString());
    queryParams.set("part2", candidateInfo.deliveryStatusPart2.toString());

    // Add all existing parameters without using iteration
    // Get the keys from the currentParams
    const keys = Array.from(
      new Set(
        currentSearch
          .replace(/^\?/, "")
          .split("&")
          .map((param) => param.split("=")[0])
      )
    ).filter((key) => key !== "" && key !== "part1" && key !== "part2");

    // Add each key-value pair to queryParams
    keys.forEach((key) => {
      const value = currentParams.get(key);
      if (value !== null) {
        queryParams.set(key, value);
      }
    });

    router.push(
      `/pgs-monitor/${
        candidateInfo.candidateNumber
      }?${queryParams.toString()}`
    );
  };

  const openDeliveredOnPaperDialog = () => {
    if (isOnePartExam) {
      if (!candidateInfo.documents?.length) {
        openDialog("warning", candidateInfo, {
          onConfirmDeliveredOnPaper: () =>
            handleDeliveredOnPaper(TestPartsEnum.Eksamen),
        });
      } else {
        openDialog("warning", candidateInfo, {
          onConfirmDeliveredOnPaper: handleDeliveredOnPaper,
        });
      }
    } else {
      openDialog("partSelect", candidateInfo, {
        onConfirmDeliveredOnPaper: handleDeliveredOnPaper,
      });
    }
  };

  const openUndoDeliveredDialog = () => {
    if (isOnePartExam) {
      handleUndoDeliveredOnPaper(TestPartsEnum.Eksamen);
    } else {
      if (
        candidateInfo.deliveryStatusPart1 ===
          CandidateStatusEnum.LevertManuelt &&
        candidateInfo.deliveryStatusPart2 === CandidateStatusEnum.LevertManuelt
      ) {
        openDialog("undoDelivered", candidateInfo, {
          onConfirmUndoDelivered: handleUndoDeliveredOnPaper,
        });
      } else {
        const testPartId =
          candidateInfo.deliveryStatusPart1 ===
          CandidateStatusEnum.LevertManuelt
            ? TestPartsEnum.EksamenDel1
            : TestPartsEnum.EksamenDel2;
        handleUndoDeliveredOnPaper(testPartId);
      }
    }
  };

  const shouldShowBlockDigitalAccess = () => {
    const completedStatuses = [
      CandidateStatusEnum.DokumentertFravaer,
      CandidateStatusEnum.IkkeDokumentertFravaer,
    ];

    if (isOnePartExam) {
      return !completedStatuses.includes(candidateInfo.deliveryStatusPart1);
    }

    return !completedStatuses.includes(candidateInfo.deliveryStatusPart2);
  };

  const canSetDelivered =
    candidateInfo.deliveryStatusPart2 !== -1
      ? candidateInfo.deliveryStatusPart2 === CandidateStatusEnum.LastetOpp &&
        candidateInfo.documents
          .filter(
            (doc) =>
              doc.testPartId === TestPartsEnum.EksamenDel2 ||
              doc.testPartId === TestPartsEnum.EksamenDel1ogDel2
          )
          .every((doc) => doc.status === DocumentStatusEnum.Delivered)
      : candidateInfo.deliveryStatusPart1 === CandidateStatusEnum.LastetOpp &&
        candidateInfo.documents
          .filter((doc) => doc.testPartId === TestPartsEnum.Eksamen)
          .every((doc) => doc.status === DocumentStatusEnum.Delivered);

  const hasMenuItems =
    canDeliverForCandidate ||
    canOpenNewDelivery ||
    canDeliverOnPaper ||
    canUndoDeliveredOnPaper ||
    canSetDelivered;

  const hasBlockDigitalAccess = shouldShowBlockDigitalAccess();

  return (
    <div className="flex items-center gap-3 ">
      <DropdownMenu modal={false} onOpenChange={(open) => setIsOpen(open)}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="h-8 w-32 text-sm border bg-transparent hover:bg-udirLightAzure-100 flex items-center justify-between"
          >
            <span>Flere valg</span>
            <FaChevronDown
              className={`h-3 w-3 transition-transform duration-200  ${
                isOpen ? "rotate-180" : ""
              }`}
              aria-hidden="true"
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="bg-udirLightAzure-100 md:w-48 w-38 z-40"
          align="start" // Venstrejuster dropdown-menuen
        >
          <AbsenceOptions candidateInfo={candidateInfo} />

          {hasMenuItems && <DropdownMenuSeparator className="bg-slate-400" />}

          <DropdownMenuGroup>
            {canDeliverForCandidate && (
              <DropdownMenuItem
                onSelect={handleDeliverForCandidate}
                className="cursor-pointer focus:bg-[#D8D8ED]"
              >
                Lever for kandidat
              </DropdownMenuItem>
            )}
            {canOpenNewDelivery && (
              <DropdownMenuItem
                onSelect={handleOpenNewDelivery}
                className="cursor-pointer focus:bg-[#D8D8ED]"
              >
                Åpne for ny levering
              </DropdownMenuItem>
            )}
            {canDeliverOnPaper && (
              <DropdownMenuItem
                className="cursor-pointer  focus:bg-[#D8D8ED]"
                onSelect={openDeliveredOnPaperDialog}
              >
                Sendes i posten
              </DropdownMenuItem>
            )}
            {canUndoDeliveredOnPaper && (
              <DropdownMenuItem
                className="cursor-pointer focus:bg-[#D8D8ED]"
                onSelect={openUndoDeliveredDialog}
              >
                Angre sendes i posten
              </DropdownMenuItem>
            )}
            {canSetDelivered && (
              <DropdownMenuItem
                className="cursor-pointer focus:bg-[#D8D8ED]"
                onSelect={setDelivered}
              >
                Sett som levert digitalt
              </DropdownMenuItem>
            )}
          </DropdownMenuGroup>

          {hasBlockDigitalAccess && (
            <DropdownMenuSeparator className="bg-slate-400" />
          )}

          {shouldShowBlockDigitalAccess() && (
            <DropdownMenuItem className="cursor-pointer focus:bg-[#D8D8ED]">
              <BlockDigitalAccess candidateInfo={candidateInfo} />
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator className="bg-slate-400" />

          <DropdownMenuItem
            className="cursor-pointer focus:bg-[#D8D8ED]"
            onSelect={() => {
              openDialog("info", candidateInfo);
            }}
          >
            Vis filer ({candidateInfo.documents.length})
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer focus:bg-[#D8D8ED]"
            onSelect={() => {
              openDialog("ip", candidateInfo);
            }}
          >
            Se logg
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default ExamSubmissionActions;
