import AppDataSource from "./data-source";
import { DataSource } from "typeorm";
import { getAzureSqlToken, CONNECTION_SAFE_MARGIN_MS } from "@/lib/server/getAzureSqlToken";

let connection: DataSource | null = null;
let connectionPromise: Promise<DataSource> | null = null;
let tokenRefreshTimer: NodeJS.Timeout | null = null;

const isLocalhost = process.env.PGS_ENVIRONMENT === "localhost";
const TOKEN_REFRESH_INTERVAL = 2 * 60 * 60 * 1000; // 2 hours (managed identity tokens valid 24h)

/**
 * Gets database connection with automatic token management
 */
export const getDbConnection = async (): Promise<DataSource> => {
  // Return existing healthy connection
  if (connection?.isInitialized) {
    return connection;
  }

  // Prevent multiple simultaneous connection attempts
  if (connectionPromise) {
    return connectionPromise;
  }

  connectionPromise = createConnection();

  try {
    const result = await connectionPromise;
    return result;
  } catch (error) {
    // Reset state on any error - forces fresh connection on next call
    connection = null;
    throw error;
  } finally {
    connectionPromise = null;
  }
};

/**
 * Creates new database connection
 */
async function createConnection(): Promise<DataSource> {
  // Close existing connection
  if (connection?.isInitialized) {
    await connection.destroy();
  }

  // Update token for Azure with safe margin for 45-minute connection lifetime
  if (!isLocalhost) {
    const token = await getAzureSqlToken(CONNECTION_SAFE_MARGIN_MS);
    const options = AppDataSource.options as any;

    if (
      options.authentication?.type === "azure-active-directory-access-token"
    ) {
      options.authentication.options.token = token;
    }
  }

  // Initialize connection
  connection = await AppDataSource.initialize();

  // Start token refresh for Azure
  if (!isLocalhost) {
    startTokenRefresh();
  }

  console.log("Database connected successfully");
  return connection;
}

/**
 * Checks if connection is healthy
 */
async function isConnectionHealthy(): Promise<boolean> {
  if (!connection?.isInitialized) return false;

  try {
    await connection.query("SELECT 1");
    return true;
  } catch {
    return false;
  }
}

/**
 * Starts proactive token refresh
 */
function startTokenRefresh(): void {
  if (tokenRefreshTimer) {
    clearInterval(tokenRefreshTimer);
  }

  tokenRefreshTimer = setInterval(async () => {
    try {
      // Close connection to force refresh on next use
      if (connection?.isInitialized) {
        await connection.destroy();
        connection = null;
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      connection = null;
    }
  }, TOKEN_REFRESH_INTERVAL);
}

/**
 * Closes connection and cleans up
 */
export async function closeDbConnection(): Promise<void> {
  if (tokenRefreshTimer) {
    clearInterval(tokenRefreshTimer);
    tokenRefreshTimer = null;
  }

  if (connection?.isInitialized) {
    try {
      await connection.destroy();
    } catch (error) {
      console.error("Error closing connection:", error);
    }
  }

  connection = null;
  connectionPromise = null;
}

// Graceful shutdown
process.on("SIGTERM", closeDbConnection);
process.on("SIGINT", closeDbConnection);

export default { getDbConnection, closeDbConnection };
