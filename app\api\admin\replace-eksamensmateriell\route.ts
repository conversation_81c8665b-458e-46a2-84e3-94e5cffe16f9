import { NextRequest, NextResponse } from "next/server";
import { EksamensmateriellService } from "@/db/services/eksamensmateriellService";
import { IEksamensmateriellData } from "@/db/services/eksamensmateriellService";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/authOptions";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";
import { generateAccessToken } from "@/lib/server/getAccessTokenForEksamen";

const telemetryClient = getAppInsightsServer();

// Helper function for PAS verification API call
async function sendExternalVerification(
  data: IEksamensmateriellData,
  searchFormData?: { fagkode: string; eksamensperiode: string; variant?: string }
) {
  const externalApiUrl = process.env.PAS_VERIFICATION_API_URL;

  if (!externalApiUrl) {
    console.warn(
      "PAS_VERIFICATION_API_URL not configured, skipping external verification call"
    );
    return;
  }

  try {
    // Use fagkode and eksamensperiode from search form (convert fagkode to uppercase)
    const fagkode = (searchFormData?.fagkode || data.fagkode)?.toUpperCase();
    const variant = searchFormData?.variant || data.variant;
    const eksamensperiode = searchFormData?.eksamensperiode || data.eksamensperiode;

    // Prepare fagkode with variant if present (uppercase)
    const fagkodeWithVariant = variant
      ? `${fagkode}-${variant.toUpperCase()}`
      : fagkode;

    // Generate access token for PAS verification API
    const token = await generateAccessToken();

    const payload = {
      documentGuid: data.blobReferanse,
      eksamensdel: data.eksamensdel,
      eksamensmaterialtype: data.eksamensmateriellkategori,
      eksamensperiode: eksamensperiode,
      fagkoder: [fagkodeWithVariant],
      malform: data.eksamensmateriellMalform,
      orginaltFilnavn: data.originalFilename,
      opphavsrett: data.opphavsrett || false,
      vedlegg: data.vedlegg || false,
    };


    const response = await fetch(externalApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "Could not read error response");
      console.error("PAS verification failed (replace):", {
        status: response.status,
        statusText: response.statusText,
        body: errorText,
        payload: payload
      });
      throw new Error(
        `PAS-verifisering feilet med status: ${response.status}. ${errorText}`
      );
    }

    telemetryClient?.trackEvent({
      name: "ExternalVerificationCallSuccessful",
      properties: {
        documentGuid: data.blobReferanse,
        eksamensdel: data.eksamensdel,
        eksamensmaterialtype: data.eksamensmateriellkategori,
      },
    });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "externalVerificationCall",
        documentGuid: data.blobReferanse,
        error: error instanceof Error ? error.message : "Unknown error",
      },
    });

    console.error(
      "External verification call failed for",
      data.blobReferanse,
      ":",
      error
    );
    // Throw error to notify client of PAS verification failure
    throw new Error(
      `PAS-verifisering feilet: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`
    );
  }
}

// Helper function for PGSA API call
async function sendPgsaNotification(
  data: IEksamensmateriellData,
  searchFormData?: { fagkode: string; eksamensperiode: string; variant?: string }
) {
  const pgsaBaseUrl = process.env.PGSA_VERIFICATION_API_URL;
  const pgsaCode = process.env.PGSA_VERIFICATION_API_URL_CODE;

  if (!pgsaBaseUrl || !pgsaCode) {
    console.warn(
      "PGSA_VERIFICATION_API_URL or PGSA_VERIFICATION_API_URL_CODE not configured, skipping PGSA notification call"
    );
    return;
  }

  const pgsaApiUrl = `${pgsaBaseUrl}/api/examdata?code=${pgsaCode}`;

  try {
    // Use fagkode and eksamensperiode from search form
    const fagkode = searchFormData?.fagkode || data.fagkode;
    const eksamensperiode = searchFormData?.eksamensperiode || data.eksamensperiode;
    const variant = searchFormData?.variant || data.variant;

    // Get exam metadata from database to determine TestType and SubjectName
    const examMetadata = await EksamensmateriellService.getExamMetadata(
      fagkode,
      variant,
      eksamensperiode
    );
    const testType = EksamensmateriellService.mapOpplaeringsniveToTestType(
      examMetadata.opplaeringsniva
    );
    const subjectName =
      examMetadata.fagnavn || `${fagkode} ${data.eksamensdel}`;

    const payload = {
      DocumentCode: data.blobReferanse,
      OrgFileName: data.originalFilename,
      TestType: testType,
      SubjectCode: fagkode,
      LanguageVariant: data.eksamensmateriellMalform || "Felles",
      TestPeriod: eksamensperiode,
      Mime: data.mimeType,
      FileExtension: "." + data.originalFilename.split(".").pop(),
      SubjectName: subjectName,
      CopyrightProtected: data.opphavsrett ? 1 : 0,
      FileSize: data.fileSize.toString(),
      VariantCode: variant || "",
    };

    const response = await fetch(pgsaApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`PGSA API kall feilet med status: ${response.status}`);
    }
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "pgsaNotificationCall",
        documentCode: data.blobReferanse,
        error: error instanceof Error ? error.message : "Unknown error",
      },
    });

    console.error(
      "PGSA notification call failed for",
      data.blobReferanse,
      ":",
      error
    );
    // Throw error to notify client of PGSA notification failure
    throw new Error(
      `PGSA-notifikasjon feilet: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`
    );
  }
}

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const userRoles = Array.isArray(session.user.role)
      ? session.user.role
      : [session.user.role];

    const hasAdminRole = userRoles.some(
      (role) => role === "urn:udir:pgsa:administrator"
    );

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const {
      eksamensmateriellData,
      oldBlobReference,
      searchFormData,
    }: {
      eksamensmateriellData: IEksamensmateriellData[];
      oldBlobReference: string;
      searchFormData?: { fagkode: string; eksamensperiode: string; variant?: string };
    } = await request.json();

    if (
      !eksamensmateriellData ||
      !Array.isArray(eksamensmateriellData) ||
      !oldBlobReference
    ) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Invalid data format - missing eksamensmateriellData or oldBlobReference",
        },
        { status: 400 }
      );
    }

    let savedCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    // Process each replacement
    for (const data of eksamensmateriellData) {
      try {
        await EksamensmateriellService.replaceEksamensmateriell(
          oldBlobReference,
          data,
          searchFormData
        );
        savedCount++;

        // Send external verification call after successful replacement
        await sendExternalVerification(data, searchFormData);

        // Send PGSA notification call after successful verification
        await sendPgsaNotification(data, searchFormData);
      } catch (error) {
        failedCount++;
        const errorMessage =
          error instanceof Error ? error.message : "Erstatning feilet";
        errors.push(`${data.originalFilename}: ${errorMessage}`);
        console.error(
          `Failed to replace eksamensmateriell with blob ${oldBlobReference}:`,
          error
        );
      }
    }

    const success = failedCount === 0;
    const message = success
      ? `${savedCount} fil${savedCount !== 1 ? "er" : ""} ble erstattet`
      : `${savedCount} fil${
          savedCount !== 1 ? "er" : ""
        } ble erstattet, ${failedCount} feilet`;

    return NextResponse.json({
      success,
      message,
      savedCount,
      failedCount,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Error replacing exam materials:", error);

    return NextResponse.json(
      { success: false, error: "Intern serverfeil under erstatning" },
      { status: 500 }
    );
  }
}
