import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/authOptions";
import { BesvarelseService } from "@/db/services/besvarelseService";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const eksamensperiode = searchParams.get("eksamensperiode");
    const fraDatoParam = searchParams.get("fraDato");
    const tilDatoParam = searchParams.get("tilDato");

    const fraDato = fraDatoParam ? new Date(fraDatoParam) : undefined;
    const tilDato = tilDatoParam ? new Date(tilDatoParam) : undefined;

    if (!eksamensperiode) {
      return NextResponse.json(
        { error: "Eksamensperiode er påkrevd" },
        { status: 400 }
      );
    }

    const besvarelseService = new BesvarelseService();

    // Fetch both statistics in parallel
    const [innleveringsoversikt, filstatistikk] = await Promise.all([
      besvarelseService.getInnleveringsoversikt(eksamensperiode, fraDato, tilDato),
      besvarelseService.getFilstatistikk(eksamensperiode, fraDato, tilDato),
    ]);

    return NextResponse.json({
      eksamensperiode,
      innleveringsoversikt,
      filstatistikk,
    });
  } catch (error) {
    console.error("Error fetching besvarelse statistics:", error);
    return NextResponse.json(
      {
        error: "Kunne ikke hente besvarelsesstatistikk",
        details: error instanceof Error ? error.message : "Ukjent feil",
      },
      { status: 500 }
    );
  }
}
