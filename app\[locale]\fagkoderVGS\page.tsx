
import React from "react";
import ExcelTableViewer from "./excelData";
import { getCurrentTermCode } from "@/lib/shared/getCurrentTermCode";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Fagkoder - PGS-admin",
 
};

export default async function Home() {
  return (
    <>
      <div className="p-6 bg-header">
        <div className="container-wrapper">
          <h1 className="text-4xl">Fagkoder for videregående skole i PGS</h1>

          <p className="mt-4">
            Fagkoder med eksamen i PGS for {getCurrentTermCode()}. Merk at det
            ikke er påmeldinger i alle fag.
          </p>
        </div>
      </div>
      <div className="container-wrapper flex justify-between md:gap-8 p-10">
        <div className="">
          <ExcelTableViewer />
        </div>
      </div>
    </>
  );
}
