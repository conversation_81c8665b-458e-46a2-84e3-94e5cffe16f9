import { NextRequest, NextResponse } from "next/server";
import archiver from "archiver";
import { Readable, PassThrough } from "stream";
import { IExamDocument } from "@/interface/IExamDocument";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";
import { getPGSXContainerClient } from "@/lib/server/blobHelper";
import { ContainerClient, BlobClient } from "@azure/storage-blob";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

export const dynamic = "force-dynamic";
const telemetryClient = getAppInsightsServer();

interface ProcessDocumentResult {
  success: boolean;
  error?: string;
}

export interface IDownloadUserSubmissionsRequest {
  documents: IExamDocument[];
  candidateNumber: string;
  subjectCode: string;
  subjectName: string;
  testPeriod: string;
}

export interface IDownloadUserSubmissionsResponse {
  error?: string;
  zipfilename?: string;
  missingfiles?: string[];
}

function addToMissingFiles(
  candidateNumber: string,
  doc: IExamDocument,
  missingFiles: string[]
): void {
  missingFiles.push(`${candidateNumber}/${doc.fileName}`);
}

function setupArchiver(): archiver.Archiver {
  const archive = archiver("zip", { zlib: { level: 9 } });

  archive.on("warning", (err) => {
    if (err.code !== "ENOENT") console.warn("Archive warning:", err);
  });

  archive.on("error", (err) => {
    console.error("Archive error:", err);
    throw err;
  });

  return archive;
}

async function processDocument(
  doc: IExamDocument,
  candidateNumber: string,
  blobClient: BlobClient,
  archive: archiver.Archiver,
  missingFiles: string[]
): Promise<ProcessDocumentResult> {
  try {
    const exists = await blobClient.exists();

    if (!exists) {
      addToMissingFiles(candidateNumber, doc, missingFiles);
      return { success: false };
    }

    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      throw new Error(`Unable to download ${doc.documentCode}`);
    }

    const nodeReadableStream = Readable.from(
      downloadResponse.readableStreamBody
    );
    archive.append(nodeReadableStream, {
      name: `${candidateNumber}/${doc.fileName}`,
    });

    return { success: true };
  } catch (error) {
    console.error(`Error processing file ${doc.documentCode}:`, error);
    return { success: false, error: (error as Error).message };
  }
}

async function processDocuments(
  documents: IExamDocument[],
  candidateNumber: string,
  containerClient: ContainerClient,
  archive: archiver.Archiver,
  missingFiles: string[]
): Promise<boolean> {
  const results = await Promise.all(
    documents.map((doc) => {
      const blobClient = containerClient.getBlobClient(doc.documentCode);
      return processDocument(
        doc,
        candidateNumber,
        blobClient,
        archive,
        missingFiles
      );
    })
  );
  return results.some((result) => result.success);
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const requestBody: IDownloadUserSubmissionsRequest = await req.json();
    const { documents, candidateNumber, subjectCode, subjectName, testPeriod } =
      requestBody;

    const containerClient = getPGSXContainerClient();
    const archive = setupArchiver();
    const missingFiles: string[] = [];
    const processStream = new PassThrough();
    archive.pipe(processStream);

    const processingPromise = processDocuments(
      documents,
      candidateNumber,
      containerClient,
      archive,
      missingFiles
    ).then((filesAdded) => {
      archive.finalize();
      return filesAdded;
    });

    const readableStream = new ReadableStream({
      start(controller) {
        processStream.on("data", (chunk) => controller.enqueue(chunk));
        processStream.on("end", () => controller.close());
        processStream.on("error", (error) => controller.error(error));
      },
    });

    const filesAdded = await processingPromise;
    const zipFileName = `${candidateNumber}_${subjectCode}_${subjectName}_${testPeriod}.zip`;

    if (!filesAdded) {
      const response: IDownloadUserSubmissionsResponse = {
        error: "No files found",
        zipfilename: zipFileName,
        missingfiles: missingFiles,
      };
      return NextResponse.json(response, { status: 404 });
    }

    const headers = new Headers({
      "Content-Type": "application/octet-stream",
      "Content-Disposition": `attachment; filename="${encodeURIComponent(
        zipFileName
      )}"`,
      "Transfer-Encoding": "chunked",
      "X-Missing-Files": JSON.stringify(missingFiles),
    });

    return new NextResponse(readableStream, { headers });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "downloadUserSubmissions",
        response: error instanceof Error ? error.message : "Unknown error",
      },
    });
    console.error("Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
