import { generateSignalRAccessToken } from "./generateSignalRAccessToken";
import { SignalRMessageEnum } from "@/enums/SignalRMessageEnum";
import { IAuthorizationRestoredPayload } from "@/interface/IAuthorizationRestoredPayload";

// Configuration for Azure SignalR with endpoint extraction from connection string
export const SIGNALR_CONFIG = {
  hubName: process.env.AZURE_SIGNALR_HUB_NAME || "pgshub",
  connectionString:
    process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "",
  get endpoint() {
    return this.connectionString.match(/Endpoint=(.*?);/)?.[1] ?? "";
  },
};

export function buildSignalRUrl(path: string): string {
  return `${SIGNALR_CONFIG.endpoint}/api/v1/hubs/${SIGNALR_CONFIG.hubName}/${path}`;
}

export async function sendSignalRMessage(url: string, message: any) {
  const accessToken = generateSignalRAccessToken({
    audience: url,
    lifetime: 60, // Token expires after 60 seconds
  });

  return fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(message),
  });
}

/**
 * Sends an authorization restored SignalR message to a specific candidate
 * This message informs the candidate that their access has been restored
 */
export async function sendAuthorizationRestoredMessage(
  candidateNumber: string,
  sessionToRestore?: string
) {
  try {
    const url = buildSignalRUrl(`users/${candidateNumber}`);
    
    const payload: IAuthorizationRestoredPayload = {
      candidateNumber,
      sessionToRestore: sessionToRestore || "", // Empty string if no specific session
    };
    
    const message = {
      target: SignalRMessageEnum.AuthorizationRestored,
      arguments: [payload],
    };
    
    await sendSignalRMessage(url, message);
  } catch (error) {
    console.error("Failed to send authorizationRestored SignalR message:", error);
    // Don't throw the error - SignalR failures should not break the main operation
  }
}
