"use server";

import { getServerSession } from "next-auth/next";
import { getAccessToken } from "@/lib/server/getAccessToken";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { NextRequest, NextResponse } from "next/server";
import { getClientIp } from "@/lib/server/getClientIp";
import { getAppInsightsServer } from "@/lib/server/appInsightsServer";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

const telemetryClient = getAppInsightsServer();

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  let response = null;

  const { fileId } = await params;
  const documentcode = fileId;
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get access token
    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    if (!accessToken) {
      return NextResponse.json(
        { error: "Failed to obtain access token" },
        { status: 500 }
      );
    }

    response = await fetch(
      `${PgsaApiUrl}/api/Monitor/exampapers/${documentcode}`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      const data = await response.text();
      console.log("Error in deleting document monitor", data);

      return NextResponse.json(data, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in deleting document monitor", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "deleteFile monitor",
        documentcode,
        response: response ? await response.text() : "Tom respons",
        statuscode: response ? response.status : 0,
      },
    });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
