import "reflect-metadata";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  PrimaryColumn,
} from "typeorm";

@Entity("BesvarelsesDelBesvarelsesfil")
export class BesvarelsesDelBesvarelsesfil {
  @PrimaryColumn({ type: "varchar", length: 255 })
  BesvarelsesdelID!: string;

  @PrimaryColumn({ type: "int" })
  BesvarelsesfilID!: number;

  @CreateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  CreatedDate!: Date;

  // Lazy relationship to Besvarelsesdel to avoid circular dependency
  @ManyToOne("Besvarelsesdel", (del: any) => del.besvarelsesDelBesvarelsesfiler)
  @JoinColumn({ name: "BesvarelsesdelID" })
  besvarelsesdel?: any;

  // Lazy relationship to Besvarelsesfil to avoid circular dependency
  @ManyToOne("Besvarelsesfil", (fil: any) => fil.besvarelsesDelBesvarelsesfiler)
  @JoinColumn({ name: "BesvarelsesfilID" })
  besvarelsesfil?: any;
}
